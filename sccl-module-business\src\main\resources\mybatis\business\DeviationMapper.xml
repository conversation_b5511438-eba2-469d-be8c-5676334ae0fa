<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.DeviationMapper">
    <resultMap type="com.sccl.modules.business.cost.domain.DeviationMssBusi" id="DeviationResult">
        <id property="id" column="id"/>
        <result property="auditTime" column="audit_time"/>
        <result property="stationCode" column="stationCode"/>
        <result property="stationName" column="stationName"/>
        <result property="stationType" column="stationType"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="company" column="company"/>
        <result property="country" column="country"/>
        <result property="cityCode" column="cityCode"/>
        <result property="countyCode" column="countyCode"/>
        <result property="mssMoney" column="mssMoney"/>
        <result property="directPrice" column="directPrice"/>
        <result property="directPower" column="directPower"/>
        <result property="transPrice" column="transPrice"/>
        <result property="transPower" column="transPower"/>
        <result property="busMoney" column="busMoney"/>
        <result property="createdBy" column="createdBy"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="selectVo">
        select id,
            audit_time,
            stationCode,
            stationName,
            stationType,
            `year`,
            `month`,
            company,
            country,
            cityCode,
            countyCode,
            mssMoney,
            directPrice,
            directPower,
            transPrice,
            transPower,
            busMoney,
            createdBy,
            createTime,
            updateTime,
            del_flag
        from deviation_mss_busi
    </sql>

    <sql id="other-condition">
        <if test="id != null">and id = #{id}</if>
        <if test="auditTime != null">and audit_time = #{auditTime}</if>
        <if test="stationCode != null">and stationCode = #{stationCode}</if>
        <if test="stationName != null">and stationName = #{stationName}</if>
        <if test="stationType != null">and stationType = #{stationType}</if>
        <if test="year != null">and `year` = #{year}</if>
        <if test="month != null">and `month` = #{month}</if>
        <if test="company != null">and company = #{company}</if>
        <if test="country != null">and country = #{country}</if>
        <if test="cityCode != null">and cityCode = #{cityCode}</if>
        <if test="countyCode != null">and countyCode = #{countyCode}</if>
        <if test="mssMoney != null">and mssMoney = #{mssMoney}</if>
        <if test="directPrice != null">and directPrice = #{directPrice}</if>
        <if test="directPower != null">and directPower = #{directPower}</if>
        <if test="transPrice != null">and transPrice = #{transPrice}</if>
        <if test="transPower != null">and transPower = #{transPower}</if>
        <if test="busMoney != null">and busMoney = #{busMoney}</if>
        <if test="createdBy != null">and createdBy = #{createdBy}</if>
        <if test="createTime != null">and createTime = #{createTime}</if>
        <if test="updateTime != null">and updateTime = #{updateTime}</if>
        <if test="delFlag != null">and del_flag = #{delFlag}</if>
    </sql>

    <sql id="like-condition">
        <if test="id != null">and id like concat('%', #{id}, '%')</if>
        <if test="auditTime != null">and audit_time like concat('%', #{auditTime}, '%')</if>
        <if test="stationCode != null">and stationCode like concat('%', #{stationCode}, '%')</if>
        <if test="stationName != null">and stationName like concat('%', #{stationName}, '%')</if>
        <if test="stationType != null">and stationType like concat('%', #{stationType}, '%')</if>
        <if test="year != null">and `year` like concat('%', #{year}, '%')</if>
        <if test="month != null">and `month` like concat('%', #{month}, '%')</if>
        <if test="company != null">and company like concat('%', #{company}, '%')</if>
        <if test="country != null">and country like concat('%', #{country}, '%')</if>
        <if test="cityCode != null">and cityCode like concat('%', #{cityCode}, '%')</if>
        <if test="countyCode != null">and countyCode like concat('%', #{countyCode}, '%')</if>
        <if test="mssMoney != null">and mssMoney like concat('%', #{mssMoney}, '%')</if>
        <if test="directPrice != null">and directPrice like concat('%', #{directPrice}, '%')</if>
        <if test="directPower != null">and directPower like concat('%', #{directPower}, '%')</if>
        <if test="transPrice != null">and transPrice like concat('%', #{transPrice}, '%')</if>
        <if test="transPower != null">and transPower like concat('%', #{transPower}, '%')</if>
        <if test="busMoney != null">and busMoney like concat('%', #{busMoney}, '%')</if>
        <if test="createdBy != null">and createdBy like concat('%', #{createdBy}, '%')</if>
        <if test="createTime != null">and createTime like concat('%', #{createTime}, '%')</if>
        <if test="updateTime != null">and updateTime like concat('%', #{updateTime}, '%')</if>
        <if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
    </sql>


    <select id="selectByLike" parameterType="com.sccl.modules.business.cost.domain.DeviationMssBusi" resultMap="DeviationResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="DeviationResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="DeviationResult">
        <include refid="selectVo"/>
        where del_flag = '0' and id = #{id}
    </select>

    <select id="count" parameterType="com.sccl.modules.business.cost.domain.DeviationMssBusi" resultType="Integer">
        select count(*) from deviation_mss_busi
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>

    <select id="selectModle" resultMap="DeviationResult">
        <include refid="selectVo"/>
        <where>
            del_flag = 0
            <include refid="other-condition"/>
        </where>
    </select>


    <insert id="insert" parameterType="com.sccl.modules.business.cost.domain.DeviationMssBusi" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into deviation_mss_busi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id, audit_time,stationCode,stationName,stationType,`year`,`month`,
            company,country,cityCode,countyCode,mssMoney,directPrice,directPower,transPrice,transPower,busMoney,
            createdBy,createTime,updateTime,del_flag
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{auditTime},
            #{stationCode},
            #{stationName},
            #{stationType},
            #{year},
            #{month},
            #{company},
            #{country},
            #{cityCode},
            #{countyCode},
            #{mssMoney},
            #{directPrice},
            #{directPower},
            #{transPrice},
            #{transPower},
            #{busMoney},
            #{createdBy},
            #{createTime},
            #{updateTime},
            #{delFlag}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into deviation_mss_busi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            audit_time,
            stationCode,
            stationName,
            stationType,
            `year`,
            `month`,
            company,
            country,
            cityCode,
            countyCode,
            mssMoney,
            directPrice,
            directPower,
            transPrice,
            transPower,
            busMoney,
            createdBy,
            createTime,
            updateTime,
            del_flag
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.auditTime},
                #{item.stationCode},
                #{item.stationName},
                #{item.stationType},
                #{item.year},
                #{item.month},
                #{item.company},
                #{item.country},
                #{item.cityCode},
                #{item.countyCode},
                #{item.mssMoney},
                #{item.directPrice},
                #{item.directPower},
                #{item.transPrice},
                #{item.transPower},
                #{item.busMoney},
                #{item.createdBy},
                #{item.createTime},
                #{item.updateTime},
                #{item.delFlag}
            </trim>
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.sccl.modules.business.cost.domain.DeviationMssBusi">
        update deviation_mss_busi
        <trim prefix="SET" suffixOverrides=",">
            audit_time = #{auditTime},
            stationCode = #{stationCode},
            stationName = #{stationName},
            stationType = #{stationType},
            `year` = #{year},
            `month` = #{month},
            company = #{company},
            country = #{country},
            cityCode = #{cityCode},
            countyCode = #{countyCode},
            mssMoney = #{mssMoney},
            directPrice = #{directPrice},
            directPower = #{directPower},
            transPrice = #{transPrice},
            transPower = #{transPower},
            busMoney = #{busMoney},
            createdBy = #{createdBy},
            createTime = #{createTime},
            updateTime = #{updateTime},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="com.sccl.modules.business.cost.domain.DeviationMssBusi">
        update deviation_mss_busi
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="company != null">company = #{company},</if>
            <if test="country != null">country = #{country},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="mssMoney != null">mssMoney = #{mssMoney},</if>
            <if test="directPrice != null">directPrice = #{directPrice},</if>
            <if test="directPower != null">directPower = #{directPower},</if>
            <if test="transPrice != null">transPrice = #{transPrice},</if>
            <if test="transPower != null">transPower = #{transPower},</if>
            <if test="busMoney != null">busMoney = #{busMoney},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModelBatch" parameterType="com.sccl.modules.business.cost.domain.DeviationMssBusi">
        update deviation_mss_busi
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="company != null">company = #{company},</if>
            <if test="country != null">country = #{country},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="mssMoney != null">mssMoney = #{mssMoney},</if>
            <if test="directPrice != null">directPrice = #{directPrice},</if>
            <if test="directPower != null">directPower = #{directPower},</if>
            <if test="transPrice != null">transPrice = #{transPrice},</if>
            <if test="transPower != null">transPower = #{transPower},</if>
            <if test="busMoney != null">busMoney = #{busMoney},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE deviation_mss_busi SET DEL_FLAG='1' where id = #{id}
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE deviation_mss_busi SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from deviation_mss_busi where id = #{id}
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from deviation_mss_busi where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="delByAuditTime">
        delete from deviation_mss_busi
        where audit_time = #{auditTime}
    </delete>

    <select id="getDirectTransPrice" resultType="com.sccl.modules.business.cost.vo.DeviationAuditVo">
        select (case when a.zhdltotal = 0 then 0 else round(a.zhdftotal/a.zhdltotal,4) end) directPrice,
	           (case when a.zgdltotal = 0 then 0 else round(a.zgdftotal/a.zgdltotal,4) end) transPrice
        from (
            select ifnull(sum(zhdltotal),0) zhdltotal,
                   ifnull(sum(zhdftotal),0) zhdftotal,
                   ifnull(sum(zgdltotal),0) zgdltotal,
                   ifnull(sum(zgdftotal),0) zgdftotal
            from statistical_turnandsupply
            WHERE del_flag = '0'
            and company = '-1'
            and country = '-1'
            and startdate = #{startdate}
            and enddate = #{enddate}
        ) a
    </select>

    <insert id="audit" parameterType="com.sccl.modules.business.cost.vo.DeviationAuditVo">
        insert into deviation_mss_busi(audit_time,stationCode,stationName,stationType,`year`,`month`,company,
        country,cityCode,countyCode,mssMoney,directPrice,directPower,transPrice,transPower,busMoney,
        createdBy,createTime,updateTime,del_flag)
        select #{auditTime} audit_time,
            a.stationCode,
            a.stationName,
            a.stationType,
            #{year} `year`,
            #{month} `month`,
            c.org_code company,
            d.country,
            a.cityCode,
            a.countyCode,
            round(ifnull(b.mssMoney,0),4) mssMoney,
            #{directPrice} directPrice,
            round(ifnull(a.directPower,0),4) directPower,
            #{transPrice} transPrice,
            round(ifnull(a.transPower,0),4) transPower,
            round(#{directPrice}*ifnull(a.directPower,0) + #{transPrice}*ifnull(a.transPower,0),4) busMoney,
            '-1' createdBy,
            #{createTime} createTime,
            #{updateTime} updateTime,
            '0' del_flag
        from (
            select a.stationCode,
                   a.stationName,
                   b.stationType,
                   a.cityCode,
                   a.countyCode,
                   sum(case b.supplyWay when '1' then a.power else 0 end) directPower,
                   sum(case b.supplyWay when '2' then a.power else 0 end) transPower
            from deviation_busi a
            left join deviation_station b on b.cityCode = a.cityCode and b.countyCode = a.countyCode and b.stationCode = a.stationCode
            where a.audit_time = #{auditTime}
            group by a.cityCode,a.countyCode,a.stationCode
        ) a
        left join deviation_mss b on b.audit_time = #{auditTime} and b.cityCode = a.cityCode and b.countyCode = a.countyCode and b.stationCode = a.stationCode
        left join power_city_organization c on c.v_org_code = a.cityCode
        left join power_country_org d on d.countyCode = a.countyCode
    </insert>


    <sql id="listFromAndWhere">
            sum(a.mssMoney) mssMoney,
            a.directPrice directPrice,
            sum(a.directPower) directPower,
            a.transPrice transPrice,
            sum(a.transPower) transPower,
            sum(a.busMoney) busMoney
        from deviation_mss_busi a
        where a.del_Flag = '0'
        <if test="tjsj != null and tjsj != ''">
            and a.audit_time = #{tjsj}
        </if>
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="companys != null">
            and  a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
    </sql>
    <select id="list" resultType="com.sccl.modules.business.cost.vo.DeviationResultVo"
            parameterType="com.sccl.modules.business.cost.vo.DeviationSearchVo">
       select t.company,
            t.companyName,
            ifnull(round(t.mssMoney/10000,4),'——') mssMoney,
            ifnull(round(t.directPrice,4),'——') directPrice,
            ifnull(round(t.directPower,4),'——') directPower,
            ifnull(round(t.transPrice,4),'——') transPrice,
            ifnull(round(t.transPower,4),'——') transPower,
            ifnull(round(t.busMoney/10000,4),'——') busMoney,
            ifnull(concat(t.rate,'%'),'——') rate,
            abs(t.rate) rateAbs
        from (
            select '' company,
                   a.org_name companyName,
                   '10' sort,
                   b.mssMoney,
                   b.directPrice,
                   b.directPower,
                   b.transPrice,
                   b.transPower,
                   b.busMoney,
                   (case when b.busMoney is null and b.mssMoney is null then null
                         when ifnull(b.busMoney,0) = 0 then 0
                         else round(100*(ifnull(b.mssMoney,0) - b.busMoney)/b.busMoney,2) end) rate
            from (select '0' id, '全省' org_name) a
            left join(
                select '0' company,
                <include refid="listFromAndWhere" />
            ) b on b.company = a.id

            union all

            select a.id company,
                   a.org_name companyName,
                   '11' sort,
                   b.mssMoney,
                   b.directPrice,
                   b.directPower,
                   b.transPrice,
                   b.transPower,
                   b.busMoney,
                   (case when b.busMoney is null and b.mssMoney is null then null
                         when ifnull(b.busMoney,0) = 0 then 0
                         else round(100*(ifnull(b.mssMoney,0) - b.busMoney)/b.busMoney,2) end) rate
            from rmp.sys_organizations a
            left join (select a.company,
                       <include refid="listFromAndWhere" />
                       group by a.company
            ) b on b.company = a.id
            where a.org_type = '1'
            and a.parent_company_no = #{prov}
            and a.del_flag = '0'
        ) t order by t.sort,abs(t.rate) desc
    </select>

    <select id="listCountry" resultType="com.sccl.modules.business.cost.vo.DeviationResultVo"
            parameterType="com.sccl.modules.business.cost.vo.DeviationSearchVo">
        select t.company,
            t.country,
            t.companyName,
            ifnull(round(t.mssMoney/10000,4),'——') mssMoney,
            ifnull(round(t.directPrice,4),'——') directPrice,
            ifnull(round(t.directPower,4),'——') directPower,
            ifnull(round(t.transPrice,4),'——') transPrice,
            ifnull(round(t.transPower,4),'——') transPower,
            ifnull(round(t.busMoney/10000,4),'——') busMoney,
            ifnull(concat(t.rate,'%'),'——') rate,
            abs(t.rate) rateAbs
        from(
            <if test="admin != '3'.toString() and (country == null or country == '')">
                SELECT #{company} company,
                    '' country,
                    '全市' companyName,
                    '10' sort,
                    b.mssMoney,
                    b.directPrice,
                    b.directPower,
                    b.transPrice,
                    b.transPower,
                    b.busMoney,
                    (case when b.busMoney is null and b.mssMoney is null then null
                    when ifnull(b.busMoney,0) = 0 then 0
                    else round(100*(ifnull(b.mssMoney,0) - b.busMoney)/b.busMoney,2) end) rate
                FROM (
                    select '0' country,
                    <include refid="listFromAndWhere" />
                ) b

                union all
            </if>

            SELECT b.company,
                b.country,
                concat(e.org_name,'-',d.org_name) companyName,
                '11' sort,
                b.mssMoney,
                b.directPrice,
                b.directPower,
                b.transPrice,
                b.transPower,
                b.busMoney,
                (case when b.busMoney is null and b.mssMoney is null then null
                when ifnull(b.busMoney,0) = 0 then 0
                else round(100*(ifnull(b.mssMoney,0) - b.busMoney)/b.busMoney,2) end) rate
            FROM (
                select a.company,
                       a.country,
                <include refid="listFromAndWhere" />
                group by a.company,a.country
            ) b
            left join rmp.sys_organizations d on d.id = b.country
            left join rmp.sys_organizations e on e.id = d.parent_company_no
        ) t order by t.sort asc,abs(t.rate) desc
    </select>

    <select id="detailsList" resultType="com.sccl.modules.business.cost.vo.DeviationXqResultVo"
            parameterType="com.sccl.modules.business.cost.vo.DeviationXqSearchVo">
        select a.stationName station,
            a.stationCode stationCode,
            (case when pec.id is null then ec.type_name else CONCAT(pec.type_name,'/',ec.type_name) end) stationType,
            b.org_name companyName,
            c.org_name countryName,
            round(a.mssMoney/10000,4) mssMoney,
            round(a.directPrice,4) directPrice,
            round(a.directPower,4) directPower,
            round(a.transPrice,4) transPrice,
            round(a.transPower,4) transPower,
            round(a.busMoney/10000,4) busMoney,
            concat(case when ifnull(a.busMoney,0) = 0 then 0
                    else round(100*(ifnull(a.mssMoney,0) - a.busMoney)/a.busMoney,2) end,'%') rate,
            abs(case when ifnull(a.busMoney,0) = 0 then 0
                     else round(100*(ifnull(a.mssMoney,0) - a.busMoney)/a.busMoney,2) end) rateAbs
        from deviation_mss_busi a
        left join rmp.sys_organizations b on b.id = a.company
        left join rmp.sys_organizations c on c.id = a.country
        left join power_electric_classification ec on ec.id = a.stationType
        left join power_electric_classification pec on pec.id = ec.parent_id
        where a.del_Flag = '0'
        and a.audit_time = #{tjsj}
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="companys != null">
            and  a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
        <if test="stationCode != null and stationCode != ''">
            and a.stationCode = #{stationCode}
        </if>
        <if test="stationType != null and stationType != ''">
            and a.stationType = #{stationType}
        </if>

        <if test="rateFrom != null">
            and (case when ifnull(a.busMoney,0) = 0 then 0
            else round(100*(ifnull(a.mssMoney,0) - a.busMoney)/a.busMoney,2) end) >= #{rateFrom}
        </if>
        <if test="rateTo != null">
            and (case when ifnull(a.busMoney,0) = 0 then 0
            else round(100*(ifnull(a.mssMoney,0) - a.busMoney)/a.busMoney,2) end) &lt;= #{rateTo}
        </if>
        <if test="key != null and key != ''">
            and a.stationName like concat('%',#{key},'%')
        </if>
        order by abs(case when ifnull(a.busMoney,0) = 0 then 0 else round(100*(ifnull(a.mssMoney,0) - a.busMoney)/a.busMoney,2) end) desc,a.stationCode
    </select>
</mapper>