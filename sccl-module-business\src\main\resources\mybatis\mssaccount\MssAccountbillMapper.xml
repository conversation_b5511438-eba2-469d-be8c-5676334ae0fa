<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper">

    <resultMap type="MssAccountbill" id="MssAccountbillResult">
        <id property="id" column="ID"/>
        <result property="paymentType" column="PAYMENT_TYPE"/>
        <result property="status" column="STATUS"/>
        <result property="clientName" column="CLIENT_NAME"/>
        <result property="clientCode" column="CLIENT_CODE"/>
        <result property="supplierName" column="SUPPLIER_NAME"/>
        <result property="supplierCode" column="SUPPLIER_CODE"/>
        <result property="supplierBank" column="SUPPLIER_BANK"/>
        <result property="supplierAccount" column="SUPPLIER_ACCOUNT"/>
        <result property="budgetItemGroupId" column="BUDGET_ITEM_GROUP_ID"/>
        <result property="packupArchiveId" column="PACKUP_ARCHIVE_ID"/>
        <result property="bizType" column="BIZ_TYPE"/>
        <result property="isPay" column="IS_PAY"/>
        <result property="conTrans" column="CON_TRANS"/>
        <result property="isToPack" column="IS_TO_PACK"/>
        <result property="budgetCode" column="BUDGET_CODE"/>
        <result property="budgetName" column="BUDGET_NAME"/>
        <result property="budgetItemCode" column="BUDGET_ITEM_CODE"/>
        <result property="budgetItemName" column="BUDGET_ITEM_NAME"/>
        <result property="cityId" column="CITY_ID"/>
        <result property="cityName" column="CITY_NAME"/>
        <result property="profitCenterCode" column="PROFIT_CENTER_CODE"/>
        <result property="profitCenterName" column="PROFIT_CENTER_NAME"/>
        <result property="costCenterId" column="COST_CENTER_ID"/>
        <result property="costCenterName" column="COST_CENTER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="isCategory" column="IS_CATEGORY"/>
        <result property="creditAccountCode" column="CREDIT_ACCOUNT_CODE"/>
        <result property="creditAccountName" column="CREDIT_ACCOUNT_NAME"/>
        <result property="isPrepay" column="IS_PREPAY"/>
        <result property="taxAdjustType" column="TAX_ADJUST_TYPE"/>
        <result property="taxAdjustSum" column="TAX_ADJUST_SUM"/>
        <result property="taxAdjustComments" column="TAX_ADJUST_COMMENTS"/>
        <result property="assessDataUrl" column="ASSESS_DATA_URL"/>
        <result property="provinceCode" column="PROVINCE_CODE"/>
        <result property="pickingMode" column="PICKING_MODE"/>
        <result property="budgetGroupItemCode" column="BUDGET_GROUP_ITEM_CODE"/>
        <result property="budgetGroupItemName" column="BUDGET_GROUP_ITEM_NAME"/>
        <result property="responseCenterCode" column="RESPONSE_CENTER_CODE"/>
        <result property="responseCenterName" column="RESPONSE_CENTER_NAME"/>
        <result property="attchmentgAdd" column="ATTCHMENTG_ADD"/>
        <result property="isTaxAdjust" column="IS_TAX_ADJUST"/>
        <result property="abstractValue" column="ABSTRACT"/>
        <result property="fillInProfitCenterId" column="FILL_IN_PROFIT_CENTER_ID"/>
        <result property="fillInCostCenterId" column="FILL_IN_COST_CENTER_ID"/>
        <result property="fillInProfitCenterName" column="FILL_IN_PROFIT_CENTER_NAME"/>
        <result property="fillInCostCenterName" column="FILL_IN_COST_CENTER_NAME"/>
        <result property="replenishFillIn" column="REPLENISH_FILL_IN"/>
        <result property="fillInRoleId" column="FILL_IN_ROLE_ID"/>
        <result property="beAfterOperateSum" column="BE_AFTER_OPERATE_SUM"/>
        <result property="feeType" column="FEE_TYPE"/>
        <result property="happenDate" column="HAPPEN_DATE"/>
        <result property="sapRemarkDate" column="SAP_REMARK_DATE"/>
        <result property="clearSum" column="CLEAR_SUM"/>
        <result property="preusedAndShareLabel" column="PREUSED_AND_SHARE_LABEL"/>
        <result property="auditShareSum" column="AUDIT_SHARE_SUM"/>
        <result property="firstShareSum" column="FIRST_SHARE_SUM"/>
        <result property="shareAccountCode" column="SHARE_ACCOUNT_CODE"/>
        <result property="shareAccountName" column="SHARE_ACCOUNT_NAME"/>
        <result property="contractno" column="CONTRACTNO"/>
        <result property="moveTypeCode" column="MOVE_TYPE_CODE"/>
        <result property="moveTypeName" column="MOVE_TYPE_NAME"/>
        <!--  <result property="usage"                       column="USAGE"                         /> -->
        <result property="usagedetail" column="USAGEDETAIL"/>
        <result property="budgetsetname" column="BUDGETSETNAME"/>
        <result property="disposemode" column="DISPOSEMODE"/>
        <result property="iscanupdate" column="ISCANUPDATE"/>
        <result property="usagedetailCode" column="USAGEDETAIL_CODE"/>
        <result property="archiveEntryType" column="ARCHIVE_ENTRY_TYPE"/>
        <result property="budgetsetid" column="BUDGETSETID"/>
        <result property="cashBudgetInstanceCode" column="CASH_BUDGET_INSTANCE_CODE"/>
        <result property="cashBudgetInstanceName" column="CASH_BUDGET_INSTANCE_NAME"/>
        <result property="loanBudgetInstanceCode" column="LOAN_BUDGET_INSTANCE_CODE"/>
        <result property="loanBudgetInstanceName" column="LOAN_BUDGET_INSTANCE_NAME"/>
        <result property="telephone" column="TELEPHONE"/>
        <result property="onceSupplierCity" column="ONCE_SUPPLIER_CITY"/>
        <result property="onceSupplierName" column="ONCE_SUPPLIER_NAME"/>
        <result property="isOnceSupplier" column="IS_ONCE_SUPPLIER"/>
        <result property="isUnifyPay" column="IS_UNIFY_PAY"/>
        <result property="creditAccountCompanyCode" column="CREDIT_ACCOUNT_COMPANY_CODE"/>
        <result property="salaryCode" column="SALARY_CODE"/>
        <result property="isProvincePay" column="IS_PROVINCE_PAY"/>
        <result property="contractName" column="CONTRACT_NAME"/>
        <result property="contractSum" column="CONTRACT_SUM"/>
        <result property="bizTypeCode" column="BIZ_TYPE_CODE"/>
        <result property="bizEntryCode" column="BIZ_ENTRY_CODE"/>
        <result property="contractDescription" column="CONTRACT_DESCRIPTION"/>
        <result property="payoffDescription" column="PAYOFF_DESCRIPTION"/>
        <result property="projectName" column="PROJECT_NAME"/>
        <result property="supplierCompanyCode" column="SUPPLIER_COMPANY_CODE"/>
        <result property="bankCode" column="BANK_CODE"/>
        <result property="isSplitCredit" column="IS_SPLIT_CREDIT"/>
        <result property="unDisplayCode" column="UN_DISPLAY_CODE"/>
        <result property="buildFee" column="BUILD_FEE"/>
        <result property="writeoffCheckInfoId" column="WRITEOFF_CHECK_INFO_ID"/>
        <result property="isImageAudited" column="IS_IMAGE_AUDITED"/>
        <result property="clearAccount" column="CLEAR_ACCOUNT"/>
        <result property="existClearAccount" column="EXIST_CLEAR_ACCOUNT"/>
        <result property="carryoverInstanceid" column="CARRYOVER_INSTANCEID"/>
        <result property="auditReason" column="AUDIT_REASON"/>
        <result property="contractInvoiceSum" column="CONTRACT_INVOICE_SUM"/>
        <result property="sapCertificateCode" column="SAP_CERTIFICATE_CODE"/>
        <result property="adjustType" column="ADJUST_TYPE"/>
        <result property="isPayment" column="IS_PAYMENT"/>
        <result property="isEmergency" column="IS_EMERGENCY"/>
        <result property="shareBeginYear" column="SHARE_BEGIN_YEAR"/>
        <result property="shareBeginMonth" column="SHARE_BEGIN_MONTH"/>
        <result property="shareEndYear" column="SHARE_END_YEAR"/>
        <result property="shareEndMonth" column="SHARE_END_MONTH"/>
        <result property="businessType" column="BUSINESS_TYPE"/>
        <result property="location" column="LOCATION"/>
        <result property="auditHappenDate" column="AUDIT_HAPPEN_DATE"/>
        <result property="companyNameTxt" column="COMPANY_NAME_TXT"/>
        <result property="isGdtelInvoice" column="IS_GDTEL_INVOICE"/>
        <result property="relativeTypeCode" column="RELATIVE_TYPE_CODE"/>
        <result property="relativeTypeName" column="RELATIVE_TYPE_NAME"/>
        <result property="isNewContract" column="IS_NEW_CONTRACT"/>
        <result property="outGoodsSum" column="OUT_GOODS_SUM"/>
        <result property="payableTaxfeeSum" column="PAYABLE_TAXFEE_SUM"/>
        <result property="bizEntryName" column="BIZ_ENTRY_NAME"/>
        <result property="tradeType" column="TRADE_TYPE"/>
        <result property="currency" column="CURRENCY"/>
        <result property="isSapFlag" column="IS_SAP_FLAG"/>
        <result property="cashItemName" column="CASH_ITEM_NAME"/>
        <result property="withholdingCityId" column="WITHHOLDING_CITY_ID"/>
        <result property="withholdingCityName" column="WITHHOLDING_CITY_NAME"/>
        <result property="bizItemInstId" column="BIZ_ITEM_INST_ID"/>
        <result property="accountCode" column="ACCOUNT_CODE"/>
        <result property="accountName" column="ACCOUNT_NAME"/>
        <result property="accountCompanyCode" column="ACCOUNT_COMPANY_CODE"/>
        <result property="isDebitCredit" column="IS_DEBIT_CREDIT"/>
        <result property="bizItemType" column="BIZ_ITEM_TYPE"/>
        <result property="isStaffPayment" column="IS_STAFF_PAYMENT"/>
        <result property="isCarryover" column="IS_CARRYOVER"/>
        <result property="timestamp" column="TIMESTAMP"/>
        <result property="taxFeeMainId" column="TAX_FEE_MAIN_ID"/>
        <result property="riskLevelFlag" column="RISK_LEVEL_FLAG"/>
        <result property="isContractFirstPay" column="IS_CONTRACT_FIRST_PAY"/>
        <result property="otherSysMainId" column="OTHER_SYS_MAIN_ID"/>
        <result property="proxyTaxAmount" column="PROXY_TAX_AMOUNT"/>
        <result property="invoiceType" column="INVOICE_TYPE"/>
        <result property="isExistKindGift" column="IS_EXIST_KIND_GIFT"/>
        <result property="kindGiftSum" column="KIND_GIFT_SUM"/>
        <result property="kindGiftTaxSum" column="KIND_GIFT_TAX_SUM"/>
        <result property="isInputTax" column="IS_INPUT_TAX"/>
        <result property="inputTaxSum" column="INPUT_TAX_SUM"/>
        <result property="isVatAuthenticated" column="IS_VAT_AUTHENTICATED"/>
        <result property="inputTaxTurnSum" column="INPUT_TAX_TURN_SUM"/>
        <result property="inputTaxTurnBizType" column="INPUT_TAX_TURN_BIZ_TYPE"/>
        <result property="isExcpayment" column="IS_EXCPAYMENT"/>
        <result property="contractUsedTimes" column="CONTRACT_USED_TIMES"/>
        <result property="isGeneralPayer" column="IS_GENERAL_PAYER"/>
        <result property="isNeedImage" column="IS_NEED_IMAGE"/>
        <result property="showDataUrl" column="SHOW_DATA_URL"/>
        <result property="writeoffArchiveId" column="WRITEOFF_ARCHIVE_ID"/>
        <result property="writeoffInstanceCode" column="WRITEOFF_INSTANCE_CODE"/>
        <result property="formTypeCode" column="FORM_TYPE_CODE"/>
        <result property="sum" column="SUM"/>
        <result property="auditSum" column="AUDIT_SUM"/>
        <result property="payoffSum" column="PAYOFF_SUM"/>
        <result property="auditPayoffSum" column="AUDIT_PAYOFF_SUM"/>
        <result property="fillInAccount" column="FILL_IN_ACCOUNT"/>
        <result property="fillInName" column="FILL_IN_NAME"/>
        <result property="fillInDep" column="FILL_IN_DEP"/>
        <result property="formAmount" column="FORM_AMOUNT"/>
        <result property="paytaxattr" column="PAYTAXATTR"/>
        <result property="busihappendtimeflag" column="BUSIHAPPENDTIMEFLAG"/>
        <result property="operatorid" column="OPERATORID"/>
        <result property="processinstid" column="PROCESSINSTID"/>
        <result property="selectpreid" column="SELECTPREID"/>
        <result property="guid" column="GUID"/>
        <result property="parentid" column="PARENTID"/>
        <result property="exceptiontype" column="EXCEPTIONTYPE"/>
        <result property="sapcreator" column="SAPCREATOR"/>
        <result property="sapcompanycode" column="SAPCOMPANYCODE"/>
        <result property="year" column="YEAR"/>
        <result property="billtype" column="BILLTYPE"/>
        <result property="iresult" column="IRESULT"/>
        <result property="suppliertype" column="SUPPLIERTYPE"/>
        <result property="orgid" column="ORGID"/>
        <result property="energytype" column="ENERGYTYPE"/>
        <result property="sumAmount" column="sumAmount"/>

    </resultMap>

    <sql id="selectVo">
        select PAYMENT_TYPE,
               STATUS,
               CLIENT_NAME,
               CLIENT_CODE,
               SUPPLIER_NAME,
               SUPPLIER_CODE,
               SUPPLIER_BANK,
               SUPPLIER_ACCOUNT,
               BUDGET_ITEM_GROUP_ID,
               PACKUP_ARCHIVE_ID,
               BIZ_TYPE,
               IS_PAY,
               CON_TRANS,
               IS_TO_PACK,
               BUDGET_CODE,
               BUDGET_NAME,
               BUDGET_ITEM_CODE,
               BUDGET_ITEM_NAME,
               CITY_ID,
               CITY_NAME,
               PROFIT_CENTER_CODE,
               PROFIT_CENTER_NAME,
               COST_CENTER_ID,
               COST_CENTER_NAME,
               CREATE_DATE,
               COMPANY_CODE,
               IS_CATEGORY,
               CREDIT_ACCOUNT_CODE,
               CREDIT_ACCOUNT_NAME,
               ID,
               IS_PREPAY,
               TAX_ADJUST_TYPE,
               TAX_ADJUST_SUM,
               TAX_ADJUST_COMMENTS,
               ASSESS_DATA_URL,
               PROVINCE_CODE,
               PICKING_MODE,
               BUDGET_GROUP_ITEM_CODE,
               BUDGET_GROUP_ITEM_NAME,
               RESPONSE_CENTER_CODE,
               RESPONSE_CENTER_NAME,
               ATTCHMENTG_ADD,
               IS_TAX_ADJUST,
               ABSTRACT,
               FILL_IN_PROFIT_CENTER_ID,
               FILL_IN_COST_CENTER_ID,
               FILL_IN_PROFIT_CENTER_NAME,
               FILL_IN_COST_CENTER_NAME,
               REPLENISH_FILL_IN,
               FILL_IN_ROLE_ID,
               BE_AFTER_OPERATE_SUM,
               FEE_TYPE,
               HAPPEN_DATE,
               SAP_REMARK_DATE,
               CLEAR_SUM,
               PREUSED_AND_SHARE_LABEL,
               AUDIT_SHARE_SUM,
               FIRST_SHARE_SUM,
               SHARE_ACCOUNT_CODE,
               SHARE_ACCOUNT_NAME,
               CONTRACTNO,
               MOVE_TYPE_CODE,
               MOVE_TYPE_NAME,
               USAGEDETAIL,
               BUDGETSETNAME,
               DISPOSEMODE,
               ISCANUPDATE,
               USAGEDETAIL_CODE,
               ARCHIVE_ENTRY_TYPE,
               BUDGETSETID,
               CASH_BUDGET_INSTANCE_CODE,
               CASH_BUDGET_INSTANCE_NAME,
               LOAN_BUDGET_INSTANCE_CODE,
               LOAN_BUDGET_INSTANCE_NAME,
               TELEPHONE,
               ONCE_SUPPLIER_CITY,
               ONCE_SUPPLIER_NAME,
               IS_ONCE_SUPPLIER,
               IS_UNIFY_PAY,
               CREDIT_ACCOUNT_COMPANY_CODE,
               SALARY_CODE,
               IS_PROVINCE_PAY,
               CONTRACT_NAME,
               CONTRACT_SUM,
               BIZ_TYPE_CODE,
               BIZ_ENTRY_CODE,
               CONTRACT_DESCRIPTION,
               PAYOFF_DESCRIPTION,
               PROJECT_NAME,
               SUPPLIER_COMPANY_CODE,
               BANK_CODE,
               IS_SPLIT_CREDIT,
               UN_DISPLAY_CODE,
               BUILD_FEE,
               WRITEOFF_CHECK_INFO_ID,
               IS_IMAGE_AUDITED,
               CLEAR_ACCOUNT,
               EXIST_CLEAR_ACCOUNT,
               CARRYOVER_INSTANCEID,
               AUDIT_REASON,
               CONTRACT_INVOICE_SUM,
               SAP_CERTIFICATE_CODE,
               ADJUST_TYPE,
               IS_PAYMENT,
               IS_EMERGENCY,
               SHARE_BEGIN_YEAR,
               SHARE_BEGIN_MONTH,
               SHARE_END_YEAR,
               SHARE_END_MONTH,
               BUSINESS_TYPE,
               LOCATION,
               AUDIT_HAPPEN_DATE,
               COMPANY_NAME_TXT,
               IS_GDTEL_INVOICE,
               RELATIVE_TYPE_CODE,
               RELATIVE_TYPE_NAME,
               IS_NEW_CONTRACT,
               OUT_GOODS_SUM,
               PAYABLE_TAXFEE_SUM,
               BIZ_ENTRY_NAME,
               TRADE_TYPE,
               CURRENCY,
               IS_SAP_FLAG,
               CASH_ITEM_NAME,
               WITHHOLDING_CITY_ID,
               WITHHOLDING_CITY_NAME,
               BIZ_ITEM_INST_ID,
               ACCOUNT_CODE,
               ACCOUNT_NAME,
               ACCOUNT_COMPANY_CODE,
               IS_DEBIT_CREDIT,
               BIZ_ITEM_TYPE,
               IS_STAFF_PAYMENT,
               IS_CARRYOVER, TIMESTAMP, TAX_FEE_MAIN_ID, RISK_LEVEL_FLAG, IS_CONTRACT_FIRST_PAY, OTHER_SYS_MAIN_ID,
            PROXY_TAX_AMOUNT, INVOICE_TYPE, IS_EXIST_KIND_GIFT, KIND_GIFT_SUM, KIND_GIFT_TAX_SUM, IS_INPUT_TAX,
            INPUT_TAX_SUM, IS_VAT_AUTHENTICATED, INPUT_TAX_TURN_SUM, INPUT_TAX_TURN_BIZ_TYPE, IS_EXCPAYMENT,
            CONTRACT_USED_TIMES, IS_GENERAL_PAYER, IS_NEED_IMAGE, SHOW_DATA_URL, WRITEOFF_ARCHIVE_ID,
            WRITEOFF_INSTANCE_CODE, FORM_TYPE_CODE, SUM, AUDIT_SUM, PAYOFF_SUM, AUDIT_PAYOFF_SUM, FILL_IN_ACCOUNT,
            FILL_IN_NAME, FILL_IN_DEP, FORM_AMOUNT, PAYTAXATTR, BUSIHAPPENDTIMEFLAG, OPERATORID, PROCESSINSTID, SELECTPREID,
            GUID, PARENTID, EXCEPTIONTYPE, SAPCREATOR, SAPCOMPANYCODE, YEAR, BILLTYPE, IRESULT, SUPPLIERTYPE, ORGID,
            ENERGYTYPE, (select sum(ifnull(AMOUNT, 0)) from mss_accountbillitem where
            WRITEOFF_INSTANCE_ID=MSS_ACCOUNTBILL.ID) sumAmount
        from MSS_ACCOUNTBILL
    </sql>

    <sql id="other-condition">
        <if test="paymentType != null">and PAYMENT_TYPE = #{paymentType}</if>
        <if test="status != null">and STATUS = #{status}</if>
        <if test="clientName != null">and CLIENT_NAME = #{clientName}</if>
        <if test="clientCode != null">and CLIENT_CODE = #{clientCode}</if>
        <if test="supplierName != null">and SUPPLIER_NAME = #{supplierName}</if>
        <if test="supplierCode != null">and SUPPLIER_CODE = #{supplierCode}</if>
        <if test="supplierBank != null">and SUPPLIER_BANK = #{supplierBank}</if>
        <if test="supplierAccount != null">and SUPPLIER_ACCOUNT = #{supplierAccount}</if>
        <if test="budgetItemGroupId != null">and BUDGET_ITEM_GROUP_ID = #{budgetItemGroupId}</if>
        <if test="packupArchiveId != null">and PACKUP_ARCHIVE_ID = #{packupArchiveId}</if>
        <if test="bizType != null">and BIZ_TYPE = #{bizType}</if>
        <if test="isPay != null">and IS_PAY = #{isPay}</if>
        <if test="conTrans != null">and CON_TRANS = #{conTrans}</if>
        <if test="isToPack != null">and IS_TO_PACK = #{isToPack}</if>
        <if test="budgetCode != null">and BUDGET_CODE = #{budgetCode}</if>
        <if test="budgetName != null">and BUDGET_NAME = #{budgetName}</if>
        <if test="budgetItemCode != null">and BUDGET_ITEM_CODE = #{budgetItemCode}</if>
        <if test="budgetItemName != null">and BUDGET_ITEM_NAME = #{budgetItemName}</if>
        <if test="cityId != null">and CITY_ID = #{cityId}</if>
        <if test="cityName != null">and CITY_NAME = #{cityName}</if>
        <if test="profitCenterCode != null">and PROFIT_CENTER_CODE = #{profitCenterCode}</if>
        <if test="profitCenterName != null">and PROFIT_CENTER_NAME = #{profitCenterName}</if>
        <if test="costCenterId != null">and COST_CENTER_ID = #{costCenterId}</if>
        <if test="costCenterName != null">and COST_CENTER_NAME = #{costCenterName}</if>
        <if test="createDate != null">and CREATE_DATE = #{createDate}</if>
        <if test="companyCode != null">and COMPANY_CODE = #{companyCode}</if>
        <if test="isCategory != null">and IS_CATEGORY = #{isCategory}</if>
        <if test="creditAccountCode != null">and CREDIT_ACCOUNT_CODE = #{creditAccountCode}</if>
        <if test="creditAccountName != null">and CREDIT_ACCOUNT_NAME = #{creditAccountName}</if>
        <if test="id != null">and ID = #{id}</if>
        <if test="isPrepay != null">and IS_PREPAY = #{isPrepay}</if>
        <if test="taxAdjustType != null">and TAX_ADJUST_TYPE = #{taxAdjustType}</if>
        <if test="taxAdjustSum != null">and TAX_ADJUST_SUM = #{taxAdjustSum}</if>
        <if test="taxAdjustComments != null">and TAX_ADJUST_COMMENTS = #{taxAdjustComments}</if>
        <if test="assessDataUrl != null">and ASSESS_DATA_URL = #{assessDataUrl}</if>
        <if test="provinceCode != null">and PROVINCE_CODE = #{provinceCode}</if>
        <if test="pickingMode != null">and PICKING_MODE = #{pickingMode}</if>
        <if test="budgetGroupItemCode != null">and BUDGET_GROUP_ITEM_CODE = #{budgetGroupItemCode}</if>
        <if test="budgetGroupItemName != null">and BUDGET_GROUP_ITEM_NAME = #{budgetGroupItemName}</if>
        <if test="responseCenterCode != null">and RESPONSE_CENTER_CODE = #{responseCenterCode}</if>
        <if test="responseCenterName != null">and RESPONSE_CENTER_NAME = #{responseCenterName}</if>
        <if test="attchmentgAdd != null">and ATTCHMENTG_ADD = #{attchmentgAdd}</if>
        <if test="isTaxAdjust != null">and IS_TAX_ADJUST = #{isTaxAdjust}</if>
        <if test="abstractValue != null">and ABSTRACT = #{abstractValue}</if>
        <if test="fillInProfitCenterId != null">and FILL_IN_PROFIT_CENTER_ID = #{fillInProfitCenterId}</if>
        <if test="fillInCostCenterId != null">and FILL_IN_COST_CENTER_ID = #{fillInCostCenterId}</if>
        <if test="fillInProfitCenterName != null">and FILL_IN_PROFIT_CENTER_NAME = #{fillInProfitCenterName}</if>
        <if test="fillInCostCenterName != null">and FILL_IN_COST_CENTER_NAME = #{fillInCostCenterName}</if>
        <if test="replenishFillIn != null">and REPLENISH_FILL_IN = #{replenishFillIn}</if>
        <if test="fillInRoleId != null">and FILL_IN_ROLE_ID = #{fillInRoleId}</if>
        <if test="beAfterOperateSum != null">and BE_AFTER_OPERATE_SUM = #{beAfterOperateSum}</if>
        <if test="feeType != null">and FEE_TYPE = #{feeType}</if>
        <if test="happenDate != null">and HAPPEN_DATE = #{happenDate}</if>
        <if test="sapRemarkDate != null">and SAP_REMARK_DATE = #{sapRemarkDate}</if>
        <if test="clearSum != null">and CLEAR_SUM = #{clearSum}</if>
        <if test="preusedAndShareLabel != null">and PREUSED_AND_SHARE_LABEL = #{preusedAndShareLabel}</if>
        <if test="auditShareSum != null">and AUDIT_SHARE_SUM = #{auditShareSum}</if>
        <if test="firstShareSum != null">and FIRST_SHARE_SUM = #{firstShareSum}</if>
        <if test="shareAccountCode != null">and SHARE_ACCOUNT_CODE = #{shareAccountCode}</if>
        <if test="shareAccountName != null">and SHARE_ACCOUNT_NAME = #{shareAccountName}</if>
        <if test="contractno != null">and CONTRACTNO = #{contractno}</if>
        <if test="moveTypeCode != null">and MOVE_TYPE_CODE = #{moveTypeCode}</if>
        <if test="moveTypeName != null">and MOVE_TYPE_NAME = #{moveTypeName}</if>
        <!--  <if test="usage != null"> and USAGE = #{usage}</if> -->
        <if test="usagedetail != null">and USAGEDETAIL = #{usagedetail}</if>
        <if test="budgetsetname != null">and BUDGETSETNAME = #{budgetsetname}</if>
        <if test="disposemode != null">and DISPOSEMODE = #{disposemode}</if>
        <if test="iscanupdate != null">and ISCANUPDATE = #{iscanupdate}</if>
        <if test="usagedetailCode != null">and USAGEDETAIL_CODE = #{usagedetailCode}</if>
        <if test="archiveEntryType != null">and ARCHIVE_ENTRY_TYPE = #{archiveEntryType}</if>
        <if test="budgetsetid != null">and BUDGETSETID = #{budgetsetid}</if>
        <if test="cashBudgetInstanceCode != null">and CASH_BUDGET_INSTANCE_CODE = #{cashBudgetInstanceCode}</if>
        <if test="cashBudgetInstanceName != null">and CASH_BUDGET_INSTANCE_NAME = #{cashBudgetInstanceName}</if>
        <if test="loanBudgetInstanceCode != null">and LOAN_BUDGET_INSTANCE_CODE = #{loanBudgetInstanceCode}</if>
        <if test="loanBudgetInstanceName != null">and LOAN_BUDGET_INSTANCE_NAME = #{loanBudgetInstanceName}</if>
        <if test="telephone != null">and TELEPHONE = #{telephone}</if>
        <if test="onceSupplierCity != null">and ONCE_SUPPLIER_CITY = #{onceSupplierCity}</if>
        <if test="onceSupplierName != null">and ONCE_SUPPLIER_NAME = #{onceSupplierName}</if>
        <if test="isOnceSupplier != null">and IS_ONCE_SUPPLIER = #{isOnceSupplier}</if>
        <if test="isUnifyPay != null">and IS_UNIFY_PAY = #{isUnifyPay}</if>
        <if test="creditAccountCompanyCode != null">and CREDIT_ACCOUNT_COMPANY_CODE = #{creditAccountCompanyCode}</if>
        <if test="salaryCode != null">and SALARY_CODE = #{salaryCode}</if>
        <if test="isProvincePay != null">and IS_PROVINCE_PAY = #{isProvincePay}</if>
        <if test="contractName != null">and CONTRACT_NAME = #{contractName}</if>
        <if test="contractSum != null">and CONTRACT_SUM = #{contractSum}</if>
        <if test="bizTypeCode != null">and BIZ_TYPE_CODE = #{bizTypeCode}</if>
        <if test="bizEntryCode != null">and BIZ_ENTRY_CODE = #{bizEntryCode}</if>
        <if test="contractDescription != null">and CONTRACT_DESCRIPTION = #{contractDescription}</if>
        <if test="payoffDescription != null">and PAYOFF_DESCRIPTION = #{payoffDescription}</if>
        <if test="projectName != null">and PROJECT_NAME = #{projectName}</if>
        <if test="supplierCompanyCode != null">and SUPPLIER_COMPANY_CODE = #{supplierCompanyCode}</if>
        <if test="bankCode != null">and BANK_CODE = #{bankCode}</if>
        <if test="isSplitCredit != null">and IS_SPLIT_CREDIT = #{isSplitCredit}</if>
        <if test="unDisplayCode != null">and UN_DISPLAY_CODE = #{unDisplayCode}</if>
        <if test="buildFee != null">and BUILD_FEE = #{buildFee}</if>
        <if test="writeoffCheckInfoId != null">and WRITEOFF_CHECK_INFO_ID = #{writeoffCheckInfoId}</if>
        <if test="isImageAudited != null">and IS_IMAGE_AUDITED = #{isImageAudited}</if>
        <if test="clearAccount != null">and CLEAR_ACCOUNT = #{clearAccount}</if>
        <if test="existClearAccount != null">and EXIST_CLEAR_ACCOUNT = #{existClearAccount}</if>
        <if test="carryoverInstanceid != null">and CARRYOVER_INSTANCEID = #{carryoverInstanceid}</if>
        <if test="auditReason != null">and AUDIT_REASON = #{auditReason}</if>
        <if test="contractInvoiceSum != null">and CONTRACT_INVOICE_SUM = #{contractInvoiceSum}</if>
        <if test="sapCertificateCode != null">and SAP_CERTIFICATE_CODE = #{sapCertificateCode}</if>
        <if test="adjustType != null">and ADJUST_TYPE = #{adjustType}</if>
        <if test="isPayment != null">and IS_PAYMENT = #{isPayment}</if>
        <if test="isEmergency != null">and IS_EMERGENCY = #{isEmergency}</if>
        <if test="shareBeginYear != null">and SHARE_BEGIN_YEAR = #{shareBeginYear}</if>
        <if test="shareBeginMonth != null">and SHARE_BEGIN_MONTH = #{shareBeginMonth}</if>
        <if test="shareEndYear != null">and SHARE_END_YEAR = #{shareEndYear}</if>
        <if test="shareEndMonth != null">and SHARE_END_MONTH = #{shareEndMonth}</if>
        <if test="businessType != null">and BUSINESS_TYPE = #{businessType}</if>
        <if test="location != null">and LOCATION = #{location}</if>
        <if test="auditHappenDate != null">and AUDIT_HAPPEN_DATE = #{auditHappenDate}</if>
        <if test="companyNameTxt != null">and COMPANY_NAME_TXT = #{companyNameTxt}</if>
        <if test="isGdtelInvoice != null">and IS_GDTEL_INVOICE = #{isGdtelInvoice}</if>
        <if test="relativeTypeCode != null">and RELATIVE_TYPE_CODE = #{relativeTypeCode}</if>
        <if test="relativeTypeName != null">and RELATIVE_TYPE_NAME = #{relativeTypeName}</if>
        <if test="isNewContract != null">and IS_NEW_CONTRACT = #{isNewContract}</if>
        <if test="outGoodsSum != null">and OUT_GOODS_SUM = #{outGoodsSum}</if>
        <if test="payableTaxfeeSum != null">and PAYABLE_TAXFEE_SUM = #{payableTaxfeeSum}</if>
        <if test="bizEntryName != null">and BIZ_ENTRY_NAME = #{bizEntryName}</if>
        <if test="tradeType != null">and TRADE_TYPE = #{tradeType}</if>
        <if test="currency != null">and CURRENCY = #{currency}</if>
        <if test="isSapFlag != null">and IS_SAP_FLAG = #{isSapFlag}</if>
        <if test="cashItemName != null">and CASH_ITEM_NAME = #{cashItemName}</if>
        <if test="withholdingCityId != null">and WITHHOLDING_CITY_ID = #{withholdingCityId}</if>
        <if test="withholdingCityName != null">and WITHHOLDING_CITY_NAME = #{withholdingCityName}</if>
        <if test="bizItemInstId != null">and BIZ_ITEM_INST_ID = #{bizItemInstId}</if>
        <if test="accountCode != null">and ACCOUNT_CODE = #{accountCode}</if>
        <if test="accountName != null">and ACCOUNT_NAME = #{accountName}</if>
        <if test="accountCompanyCode != null">and ACCOUNT_COMPANY_CODE = #{accountCompanyCode}</if>
        <if test="isDebitCredit != null">and IS_DEBIT_CREDIT = #{isDebitCredit}</if>
        <if test="bizItemType != null">and BIZ_ITEM_TYPE = #{bizItemType}</if>
        <if test="isStaffPayment != null">and IS_STAFF_PAYMENT = #{isStaffPayment}</if>
        <if test="isCarryover != null">and IS_CARRYOVER = #{isCarryover}</if>
        <if test="timestamp != null">and TIMESTAMP = #{timestamp}</if>
        <if test="taxFeeMainId != null">and TAX_FEE_MAIN_ID = #{taxFeeMainId}</if>
        <if test="riskLevelFlag != null">and RISK_LEVEL_FLAG = #{riskLevelFlag}</if>
        <if test="isContractFirstPay != null">and IS_CONTRACT_FIRST_PAY = #{isContractFirstPay}</if>
        <if test="otherSysMainId != null">and OTHER_SYS_MAIN_ID = #{otherSysMainId}</if>
        <if test="proxyTaxAmount != null">and PROXY_TAX_AMOUNT = #{proxyTaxAmount}</if>
        <if test="invoiceType != null">and INVOICE_TYPE = #{invoiceType}</if>
        <if test="isExistKindGift != null">and IS_EXIST_KIND_GIFT = #{isExistKindGift}</if>
        <if test="kindGiftSum != null">and KIND_GIFT_SUM = #{kindGiftSum}</if>
        <if test="kindGiftTaxSum != null">and KIND_GIFT_TAX_SUM = #{kindGiftTaxSum}</if>
        <if test="isInputTax != null">and IS_INPUT_TAX = #{isInputTax}</if>
        <if test="inputTaxSum != null">and INPUT_TAX_SUM = #{inputTaxSum}</if>
        <if test="isVatAuthenticated != null">and IS_VAT_AUTHENTICATED = #{isVatAuthenticated}</if>
        <if test="inputTaxTurnSum != null">and INPUT_TAX_TURN_SUM = #{inputTaxTurnSum}</if>
        <if test="inputTaxTurnBizType != null">and INPUT_TAX_TURN_BIZ_TYPE = #{inputTaxTurnBizType}</if>
        <if test="isExcpayment != null">and IS_EXCPAYMENT = #{isExcpayment}</if>
        <if test="contractUsedTimes != null">and CONTRACT_USED_TIMES = #{contractUsedTimes}</if>
        <if test="isGeneralPayer != null">and IS_GENERAL_PAYER = #{isGeneralPayer}</if>
        <if test="isNeedImage != null">and IS_NEED_IMAGE = #{isNeedImage}</if>
        <if test="showDataUrl != null">and SHOW_DATA_URL = #{showDataUrl}</if>
        <if test="writeoffArchiveId != null">and WRITEOFF_ARCHIVE_ID = #{writeoffArchiveId}</if>
        <if test="writeoffInstanceCode != null">and WRITEOFF_INSTANCE_CODE = #{writeoffInstanceCode}</if>
        <if test="formTypeCode != null">and FORM_TYPE_CODE = #{formTypeCode}</if>
        <if test="sum != null">and SUM = #{sum}</if>
        <if test="auditSum != null">and AUDIT_SUM = #{auditSum}</if>
        <if test="payoffSum != null">and PAYOFF_SUM = #{payoffSum}</if>
        <if test="auditPayoffSum != null">and AUDIT_PAYOFF_SUM = #{auditPayoffSum}</if>
        <if test="fillInAccount != null">and FILL_IN_ACCOUNT = #{fillInAccount}</if>
        <if test="fillInName != null">and FILL_IN_NAME = #{fillInName}</if>
        <if test="fillInDep != null">and FILL_IN_DEP = #{fillInDep}</if>
        <if test="formAmount != null">and FORM_AMOUNT = #{formAmount}</if>
        <if test="paytaxattr != null">and PAYTAXATTR = #{paytaxattr}</if>
        <if test="busihappendtimeflag != null">and BUSIHAPPENDTIMEFLAG = #{busihappendtimeflag}</if>
        <if test="operatorid != null">and OPERATORID = #{operatorid}</if>
        <if test="processinstid != null">and PROCESSINSTID = #{processinstid}</if>
        <if test="selectpreid != null">and SELECTPREID = #{selectpreid}</if>
        <if test="guid != null">and GUID = #{guid}</if>
        <if test="parentid != null">and PARENTID = #{parentid}</if>
        <if test="exceptiontype != null">and EXCEPTIONTYPE = #{exceptiontype}</if>
        <if test="sapcreator != null">and SAPCREATOR = #{sapcreator}</if>
        <if test="sapcompanycode != null">and SAPCOMPANYCODE = #{sapcompanycode}</if>
        <if test="year != null">and YEAR = #{year}</if>
        <if test="billtype != null">and BILLTYPE = #{billtype}</if>
        <if test="iresult != null">and IRESULT = #{iresult}</if>
        <if test="suppliertype != null">and SUPPLIERTYPE = #{suppliertype}</if>
        <if test="orgid != null">and ORGID = #{orgid}</if>
        <if test="energytype != null">and ENERGYTYPE = #{energytype}</if>
    </sql>

    <sql id="like-condition">
        <if test="paymentType != null">and PAYMENT_TYPE like concat('%', #{paymentType}, '%')</if>
        <if test="status != null">and STATUS like concat('%', #{status}, '%')</if>
        <if test="clientName != null">and CLIENT_NAME like concat('%', #{clientName}, '%')</if>
        <if test="clientCode != null">and CLIENT_CODE like concat('%', #{clientCode}, '%')</if>
        <if test="supplierName != null">and SUPPLIER_NAME like concat('%', #{supplierName}, '%')</if>
        <if test="supplierCode != null">and SUPPLIER_CODE like concat('%', #{supplierCode}, '%')</if>
        <if test="supplierBank != null">and SUPPLIER_BANK like concat('%', #{supplierBank}, '%')</if>
        <if test="supplierAccount != null">and SUPPLIER_ACCOUNT like concat('%', #{supplierAccount}, '%')</if>
        <if test="budgetItemGroupId != null">and BUDGET_ITEM_GROUP_ID like concat('%', #{budgetItemGroupId}, '%')</if>
        <if test="packupArchiveId != null">and PACKUP_ARCHIVE_ID like concat('%', #{packupArchiveId}, '%')</if>
        <if test="bizType != null">and BIZ_TYPE like concat('%', #{bizType}, '%')</if>
        <if test="isPay != null">and IS_PAY like concat('%', #{isPay}, '%')</if>
        <if test="conTrans != null">and CON_TRANS like concat('%', #{conTrans}, '%')</if>
        <if test="isToPack != null">and IS_TO_PACK like concat('%', #{isToPack}, '%')</if>
        <if test="budgetCode != null">and BUDGET_CODE like concat('%', #{budgetCode}, '%')</if>
        <if test="budgetName != null">and BUDGET_NAME like concat('%', #{budgetName}, '%')</if>
        <if test="budgetItemCode != null">and BUDGET_ITEM_CODE like concat('%', #{budgetItemCode}, '%')</if>
        <if test="budgetItemName != null">and BUDGET_ITEM_NAME like concat('%', #{budgetItemName}, '%')</if>
        <if test="cityId != null">and CITY_ID like concat('%', #{cityId}, '%')</if>
        <if test="cityName != null">and CITY_NAME like concat('%', #{cityName}, '%')</if>
        <if test="profitCenterCode != null">and PROFIT_CENTER_CODE like concat('%', #{profitCenterCode}, '%')</if>
        <if test="profitCenterName != null">and PROFIT_CENTER_NAME like concat('%', #{profitCenterName}, '%')</if>
        <if test="costCenterId != null">and COST_CENTER_ID like concat('%', #{costCenterId}, '%')</if>
        <if test="costCenterName != null">and COST_CENTER_NAME like concat('%', #{costCenterName}, '%')</if>
        <if test="createDate != null">and CREATE_DATE like concat('%', #{createDate}, '%')</if>
        <if test="companyCode != null">and COMPANY_CODE like concat('%', #{companyCode}, '%')</if>
        <if test="isCategory != null">and IS_CATEGORY like concat('%', #{isCategory}, '%')</if>
        <if test="creditAccountCode != null">and CREDIT_ACCOUNT_CODE like concat('%', #{creditAccountCode}, '%')</if>
        <if test="creditAccountName != null">and CREDIT_ACCOUNT_NAME like concat('%', #{creditAccountName}, '%')</if>
        <if test="id != null">and ID like concat('%', #{id}, '%')</if>
        <if test="isPrepay != null">and IS_PREPAY like concat('%', #{isPrepay}, '%')</if>
        <if test="taxAdjustType != null">and TAX_ADJUST_TYPE like concat('%', #{taxAdjustType}, '%')</if>
        <if test="taxAdjustSum != null">and TAX_ADJUST_SUM like concat('%', #{taxAdjustSum}, '%')</if>
        <if test="taxAdjustComments != null">and TAX_ADJUST_COMMENTS like concat('%', #{taxAdjustComments}, '%')</if>
        <if test="assessDataUrl != null">and ASSESS_DATA_URL like concat('%', #{assessDataUrl}, '%')</if>
        <if test="provinceCode != null">and PROVINCE_CODE like concat('%', #{provinceCode}, '%')</if>
        <if test="pickingMode != null">and PICKING_MODE like concat('%', #{pickingMode}, '%')</if>
        <if test="budgetGroupItemCode != null">and BUDGET_GROUP_ITEM_CODE like concat('%', #{budgetGroupItemCode},
            '%')
        </if>
        <if test="budgetGroupItemName != null">and BUDGET_GROUP_ITEM_NAME like concat('%', #{budgetGroupItemName},
            '%')
        </if>
        <if test="responseCenterCode != null">and RESPONSE_CENTER_CODE like concat('%', #{responseCenterCode}, '%')</if>
        <if test="responseCenterName != null">and RESPONSE_CENTER_NAME like concat('%', #{responseCenterName}, '%')</if>
        <if test="attchmentgAdd != null">and ATTCHMENTG_ADD like concat('%', #{attchmentgAdd}, '%')</if>
        <if test="isTaxAdjust != null">and IS_TAX_ADJUST like concat('%', #{isTaxAdjust}, '%')</if>
        <if test="abstractValue != null">and ABSTRACT like concat('%', #{abstractValue}, '%')</if>
        <if test="fillInProfitCenterId != null">and FILL_IN_PROFIT_CENTER_ID like concat('%', #{fillInProfitCenterId},
            '%')
        </if>
        <if test="fillInCostCenterId != null">and FILL_IN_COST_CENTER_ID like concat('%', #{fillInCostCenterId}, '%')
        </if>
        <if test="fillInProfitCenterName != null">and FILL_IN_PROFIT_CENTER_NAME like concat('%',
            #{fillInProfitCenterName}, '%')
        </if>
        <if test="fillInCostCenterName != null">and FILL_IN_COST_CENTER_NAME like concat('%', #{fillInCostCenterName},
            '%')
        </if>
        <if test="replenishFillIn != null">and REPLENISH_FILL_IN like concat('%', #{replenishFillIn}, '%')</if>
        <if test="fillInRoleId != null">and FILL_IN_ROLE_ID like concat('%', #{fillInRoleId}, '%')</if>
        <if test="beAfterOperateSum != null">and BE_AFTER_OPERATE_SUM like concat('%', #{beAfterOperateSum}, '%')</if>
        <if test="feeType != null">and FEE_TYPE like concat('%', #{feeType}, '%')</if>
        <if test="happenDate != null">and HAPPEN_DATE like concat('%', #{happenDate}, '%')</if>
        <if test="sapRemarkDate != null">and SAP_REMARK_DATE like concat('%', #{sapRemarkDate}, '%')</if>
        <if test="clearSum != null">and CLEAR_SUM like concat('%', #{clearSum}, '%')</if>
        <if test="preusedAndShareLabel != null">and PREUSED_AND_SHARE_LABEL like concat('%', #{preusedAndShareLabel},
            '%')
        </if>
        <if test="auditShareSum != null">and AUDIT_SHARE_SUM like concat('%', #{auditShareSum}, '%')</if>
        <if test="firstShareSum != null">and FIRST_SHARE_SUM like concat('%', #{firstShareSum}, '%')</if>
        <if test="shareAccountCode != null">and SHARE_ACCOUNT_CODE like concat('%', #{shareAccountCode}, '%')</if>
        <if test="shareAccountName != null">and SHARE_ACCOUNT_NAME like concat('%', #{shareAccountName}, '%')</if>
        <if test="contractno != null">and CONTRACTNO like concat('%', #{contractno}, '%')</if>
        <if test="moveTypeCode != null">and MOVE_TYPE_CODE like concat('%', #{moveTypeCode}, '%')</if>
        <if test="moveTypeName != null">and MOVE_TYPE_NAME like concat('%', #{moveTypeName}, '%')</if>
        <!-- <if test="usage != null"> and USAGE like concat('%', #{usage}, '%') </if> -->
        <if test="usagedetail != null">and USAGEDETAIL like concat('%', #{usagedetail}, '%')</if>
        <if test="budgetsetname != null">and BUDGETSETNAME like concat('%', #{budgetsetname}, '%')</if>
        <if test="disposemode != null">and DISPOSEMODE like concat('%', #{disposemode}, '%')</if>
        <if test="iscanupdate != null">and ISCANUPDATE like concat('%', #{iscanupdate}, '%')</if>
        <if test="usagedetailCode != null">and USAGEDETAIL_CODE like concat('%', #{usagedetailCode}, '%')</if>
        <if test="archiveEntryType != null">and ARCHIVE_ENTRY_TYPE like concat('%', #{archiveEntryType}, '%')</if>
        <if test="budgetsetid != null">and BUDGETSETID like concat('%', #{budgetsetid}, '%')</if>
        <if test="cashBudgetInstanceCode != null">and CASH_BUDGET_INSTANCE_CODE like concat('%',
            #{cashBudgetInstanceCode}, '%')
        </if>
        <if test="cashBudgetInstanceName != null">and CASH_BUDGET_INSTANCE_NAME like concat('%',
            #{cashBudgetInstanceName}, '%')
        </if>
        <if test="loanBudgetInstanceCode != null">and LOAN_BUDGET_INSTANCE_CODE like concat('%',
            #{loanBudgetInstanceCode}, '%')
        </if>
        <if test="loanBudgetInstanceName != null">and LOAN_BUDGET_INSTANCE_NAME like concat('%',
            #{loanBudgetInstanceName}, '%')
        </if>
        <if test="telephone != null">and TELEPHONE like concat('%', #{telephone}, '%')</if>
        <if test="onceSupplierCity != null">and ONCE_SUPPLIER_CITY like concat('%', #{onceSupplierCity}, '%')</if>
        <if test="onceSupplierName != null">and ONCE_SUPPLIER_NAME like concat('%', #{onceSupplierName}, '%')</if>
        <if test="isOnceSupplier != null">and IS_ONCE_SUPPLIER like concat('%', #{isOnceSupplier}, '%')</if>
        <if test="isUnifyPay != null">and IS_UNIFY_PAY like concat('%', #{isUnifyPay}, '%')</if>
        <if test="creditAccountCompanyCode != null">and CREDIT_ACCOUNT_COMPANY_CODE like concat('%',
            #{creditAccountCompanyCode}, '%')
        </if>
        <if test="salaryCode != null">and SALARY_CODE like concat('%', #{salaryCode}, '%')</if>
        <if test="isProvincePay != null">and IS_PROVINCE_PAY like concat('%', #{isProvincePay}, '%')</if>
        <if test="contractName != null">and CONTRACT_NAME like concat('%', #{contractName}, '%')</if>
        <if test="contractSum != null">and CONTRACT_SUM like concat('%', #{contractSum}, '%')</if>
        <if test="bizTypeCode != null">and BIZ_TYPE_CODE like concat('%', #{bizTypeCode}, '%')</if>
        <if test="bizEntryCode != null">and BIZ_ENTRY_CODE like concat('%', #{bizEntryCode}, '%')</if>
        <if test="contractDescription != null">and CONTRACT_DESCRIPTION like concat('%', #{contractDescription}, '%')
        </if>
        <if test="payoffDescription != null">and PAYOFF_DESCRIPTION like concat('%', #{payoffDescription}, '%')</if>
        <if test="projectName != null">and PROJECT_NAME like concat('%', #{projectName}, '%')</if>
        <if test="supplierCompanyCode != null">and SUPPLIER_COMPANY_CODE like concat('%', #{supplierCompanyCode}, '%')
        </if>
        <if test="bankCode != null">and BANK_CODE like concat('%', #{bankCode}, '%')</if>
        <if test="isSplitCredit != null">and IS_SPLIT_CREDIT like concat('%', #{isSplitCredit}, '%')</if>
        <if test="unDisplayCode != null">and UN_DISPLAY_CODE like concat('%', #{unDisplayCode}, '%')</if>
        <if test="buildFee != null">and BUILD_FEE like concat('%', #{buildFee}, '%')</if>
        <if test="writeoffCheckInfoId != null">and WRITEOFF_CHECK_INFO_ID like concat('%', #{writeoffCheckInfoId},
            '%')
        </if>
        <if test="isImageAudited != null">and IS_IMAGE_AUDITED like concat('%', #{isImageAudited}, '%')</if>
        <if test="clearAccount != null">and CLEAR_ACCOUNT like concat('%', #{clearAccount}, '%')</if>
        <if test="existClearAccount != null">and EXIST_CLEAR_ACCOUNT like concat('%', #{existClearAccount}, '%')</if>
        <if test="carryoverInstanceid != null">and CARRYOVER_INSTANCEID like concat('%', #{carryoverInstanceid}, '%')
        </if>
        <if test="auditReason != null">and AUDIT_REASON like concat('%', #{auditReason}, '%')</if>
        <if test="contractInvoiceSum != null">and CONTRACT_INVOICE_SUM like concat('%', #{contractInvoiceSum}, '%')</if>
        <if test="sapCertificateCode != null">and SAP_CERTIFICATE_CODE like concat('%', #{sapCertificateCode}, '%')</if>
        <if test="adjustType != null">and ADJUST_TYPE like concat('%', #{adjustType}, '%')</if>
        <if test="isPayment != null">and IS_PAYMENT like concat('%', #{isPayment}, '%')</if>
        <if test="isEmergency != null">and IS_EMERGENCY like concat('%', #{isEmergency}, '%')</if>
        <if test="shareBeginYear != null">and SHARE_BEGIN_YEAR like concat('%', #{shareBeginYear}, '%')</if>
        <if test="shareBeginMonth != null">and SHARE_BEGIN_MONTH like concat('%', #{shareBeginMonth}, '%')</if>
        <if test="shareEndYear != null">and SHARE_END_YEAR like concat('%', #{shareEndYear}, '%')</if>
        <if test="shareEndMonth != null">and SHARE_END_MONTH like concat('%', #{shareEndMonth}, '%')</if>
        <if test="businessType != null">and BUSINESS_TYPE like concat('%', #{businessType}, '%')</if>
        <if test="location != null">and LOCATION like concat('%', #{location}, '%')</if>
        <if test="auditHappenDate != null">and AUDIT_HAPPEN_DATE like concat('%', #{auditHappenDate}, '%')</if>
        <if test="companyNameTxt != null">and COMPANY_NAME_TXT like concat('%', #{companyNameTxt}, '%')</if>
        <if test="isGdtelInvoice != null">and IS_GDTEL_INVOICE like concat('%', #{isGdtelInvoice}, '%')</if>
        <if test="relativeTypeCode != null">and RELATIVE_TYPE_CODE like concat('%', #{relativeTypeCode}, '%')</if>
        <if test="relativeTypeName != null">and RELATIVE_TYPE_NAME like concat('%', #{relativeTypeName}, '%')</if>
        <if test="isNewContract != null">and IS_NEW_CONTRACT like concat('%', #{isNewContract}, '%')</if>
        <if test="outGoodsSum != null">and OUT_GOODS_SUM like concat('%', #{outGoodsSum}, '%')</if>
        <if test="payableTaxfeeSum != null">and PAYABLE_TAXFEE_SUM like concat('%', #{payableTaxfeeSum}, '%')</if>
        <if test="bizEntryName != null">and BIZ_ENTRY_NAME like concat('%', #{bizEntryName}, '%')</if>
        <if test="tradeType != null">and TRADE_TYPE like concat('%', #{tradeType}, '%')</if>
        <if test="currency != null">and CURRENCY like concat('%', #{currency}, '%')</if>
        <if test="isSapFlag != null">and IS_SAP_FLAG like concat('%', #{isSapFlag}, '%')</if>
        <if test="cashItemName != null">and CASH_ITEM_NAME like concat('%', #{cashItemName}, '%')</if>
        <if test="withholdingCityId != null">and WITHHOLDING_CITY_ID like concat('%', #{withholdingCityId}, '%')</if>
        <if test="withholdingCityName != null">and WITHHOLDING_CITY_NAME like concat('%', #{withholdingCityName}, '%')
        </if>
        <if test="bizItemInstId != null">and BIZ_ITEM_INST_ID like concat('%', #{bizItemInstId}, '%')</if>
        <if test="accountCode != null">and ACCOUNT_CODE like concat('%', #{accountCode}, '%')</if>
        <if test="accountName != null">and ACCOUNT_NAME like concat('%', #{accountName}, '%')</if>
        <if test="accountCompanyCode != null">and ACCOUNT_COMPANY_CODE like concat('%', #{accountCompanyCode}, '%')</if>
        <if test="isDebitCredit != null">and IS_DEBIT_CREDIT like concat('%', #{isDebitCredit}, '%')</if>
        <if test="bizItemType != null">and BIZ_ITEM_TYPE like concat('%', #{bizItemType}, '%')</if>
        <if test="isStaffPayment != null">and IS_STAFF_PAYMENT like concat('%', #{isStaffPayment}, '%')</if>
        <if test="isCarryover != null">and IS_CARRYOVER like concat('%', #{isCarryover}, '%')</if>
        <if test="timestamp != null">and TIMESTAMP like concat('%', #{timestamp}, '%')</if>
        <if test="taxFeeMainId != null">and TAX_FEE_MAIN_ID like concat('%', #{taxFeeMainId}, '%')</if>
        <if test="riskLevelFlag != null">and RISK_LEVEL_FLAG like concat('%', #{riskLevelFlag}, '%')</if>
        <if test="isContractFirstPay != null">and IS_CONTRACT_FIRST_PAY like concat('%', #{isContractFirstPay}, '%')
        </if>
        <if test="otherSysMainId != null">and OTHER_SYS_MAIN_ID like concat('%', #{otherSysMainId}, '%')</if>
        <if test="proxyTaxAmount != null">and PROXY_TAX_AMOUNT like concat('%', #{proxyTaxAmount}, '%')</if>
        <if test="invoiceType != null">and INVOICE_TYPE like concat('%', #{invoiceType}, '%')</if>
        <if test="isExistKindGift != null">and IS_EXIST_KIND_GIFT like concat('%', #{isExistKindGift}, '%')</if>
        <if test="kindGiftSum != null">and KIND_GIFT_SUM like concat('%', #{kindGiftSum}, '%')</if>
        <if test="kindGiftTaxSum != null">and KIND_GIFT_TAX_SUM like concat('%', #{kindGiftTaxSum}, '%')</if>
        <if test="isInputTax != null">and IS_INPUT_TAX like concat('%', #{isInputTax}, '%')</if>
        <if test="inputTaxSum != null">and INPUT_TAX_SUM like concat('%', #{inputTaxSum}, '%')</if>
        <if test="isVatAuthenticated != null">and IS_VAT_AUTHENTICATED like concat('%', #{isVatAuthenticated}, '%')</if>
        <if test="inputTaxTurnSum != null">and INPUT_TAX_TURN_SUM like concat('%', #{inputTaxTurnSum}, '%')</if>
        <if test="inputTaxTurnBizType != null">and INPUT_TAX_TURN_BIZ_TYPE like concat('%', #{inputTaxTurnBizType},
            '%')
        </if>
        <if test="isExcpayment != null">and IS_EXCPAYMENT like concat('%', #{isExcpayment}, '%')</if>
        <if test="contractUsedTimes != null">and CONTRACT_USED_TIMES like concat('%', #{contractUsedTimes}, '%')</if>
        <if test="isGeneralPayer != null">and IS_GENERAL_PAYER like concat('%', #{isGeneralPayer}, '%')</if>
        <if test="isNeedImage != null">and IS_NEED_IMAGE like concat('%', #{isNeedImage}, '%')</if>
        <if test="showDataUrl != null">and SHOW_DATA_URL like concat('%', #{showDataUrl}, '%')</if>
        <if test="writeoffArchiveId != null">and WRITEOFF_ARCHIVE_ID like concat('%', #{writeoffArchiveId}, '%')</if>
        <if test="writeoffInstanceCode != null">and WRITEOFF_INSTANCE_CODE like concat('%', #{writeoffInstanceCode},
            '%')
        </if>
        <if test="formTypeCode != null">and FORM_TYPE_CODE like concat('%', #{formTypeCode}, '%')</if>
        <if test="sum != null">and SUM like concat('%', #{sum}, '%')</if>
        <if test="auditSum != null">and AUDIT_SUM like concat('%', #{auditSum}, '%')</if>
        <if test="payoffSum != null">and PAYOFF_SUM like concat('%', #{payoffSum}, '%')</if>
        <if test="auditPayoffSum != null">and AUDIT_PAYOFF_SUM like concat('%', #{auditPayoffSum}, '%')</if>
        <if test="fillInAccount != null">and FILL_IN_ACCOUNT like concat('%', #{fillInAccount}, '%')</if>
        <if test="fillInName != null">and FILL_IN_NAME like concat('%', #{fillInName}, '%')</if>
        <if test="fillInDep != null">and FILL_IN_DEP like concat('%', #{fillInDep}, '%')</if>
        <if test="formAmount != null">and FORM_AMOUNT like concat('%', #{formAmount}, '%')</if>
        <if test="paytaxattr != null">and PAYTAXATTR like concat('%', #{paytaxattr}, '%')</if>
        <if test="busihappendtimeflag != null">and BUSIHAPPENDTIMEFLAG like concat('%', #{busihappendtimeflag}, '%')
        </if>
        <if test="operatorid != null">and OPERATORID like concat('%', #{operatorid}, '%')</if>
        <if test="processinstid != null">and PROCESSINSTID like concat('%', #{processinstid}, '%')</if>
        <if test="selectpreid != null">and SELECTPREID like concat('%', #{selectpreid}, '%')</if>
        <if test="guid != null">and GUID like concat('%', #{guid}, '%')</if>
        <if test="parentid != null">and PARENTID like concat('%', #{parentid}, '%')</if>
        <if test="exceptiontype != null">and EXCEPTIONTYPE like concat('%', #{exceptiontype}, '%')</if>
        <if test="sapcreator != null">and SAPCREATOR like concat('%', #{sapcreator}, '%')</if>
        <if test="sapcompanycode != null">and SAPCOMPANYCODE like concat('%', #{sapcompanycode}, '%')</if>
        <if test="year != null">and YEAR like concat('%', #{year}, '%')</if>
        <if test="billtype != null">and BILLTYPE like concat('%', #{billtype}, '%')</if>
        <if test="iresult != null">and IRESULT like concat('%', #{iresult}, '%')</if>
        <if test="suppliertype != null">and SUPPLIERTYPE like concat('%', #{suppliertype}, '%')</if>
        <if test="orgid != null">and ORGID like concat('%', #{orgid}, '%')</if>
        <if test="energytype != null">and ENERGYTYPE like concat('%', #{energytype}, '%')</if>
    </sql>

    <select id="selectList" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        <where>
            <include refid="other-condition"/>
        </where>
    </select>

    <select id="selectByLike" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        <where>
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        <where>
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        where ID = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </select>

    <select id="count" parameterType="MssAccountbill" resultType="Integer">
        select count(*) from MSS_ACCOUNTBILL
        <where>
            <include refid="other-condition"/>
        </where>
    </select>

    <insert id="insert" parameterType="MssAccountbill" useGeneratedKeys="false" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into MSS_ACCOUNTBILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            PAYMENT_TYPE,STATUS,CLIENT_NAME,CLIENT_CODE,SUPPLIER_NAME,SUPPLIER_CODE,SUPPLIER_BANK,SUPPLIER_ACCOUNT,BUDGET_ITEM_GROUP_ID,PACKUP_ARCHIVE_ID,BIZ_TYPE,IS_PAY,CON_TRANS,IS_TO_PACK,BUDGET_CODE,BUDGET_NAME,BUDGET_ITEM_CODE,BUDGET_ITEM_NAME,CITY_ID,CITY_NAME,PROFIT_CENTER_CODE,PROFIT_CENTER_NAME,COST_CENTER_ID,COST_CENTER_NAME,CREATE_DATE,COMPANY_CODE,IS_CATEGORY,CREDIT_ACCOUNT_CODE,CREDIT_ACCOUNT_NAME,ID,IS_PREPAY,TAX_ADJUST_TYPE,TAX_ADJUST_SUM,TAX_ADJUST_COMMENTS,ASSESS_DATA_URL,PROVINCE_CODE,PICKING_MODE,BUDGET_GROUP_ITEM_CODE,BUDGET_GROUP_ITEM_NAME,RESPONSE_CENTER_CODE,RESPONSE_CENTER_NAME,ATTCHMENTG_ADD,IS_TAX_ADJUST,ABSTRACT,FILL_IN_PROFIT_CENTER_ID,FILL_IN_COST_CENTER_ID,FILL_IN_PROFIT_CENTER_NAME,FILL_IN_COST_CENTER_NAME,REPLENISH_FILL_IN,FILL_IN_ROLE_ID,BE_AFTER_OPERATE_SUM,FEE_TYPE,HAPPEN_DATE,SAP_REMARK_DATE,CLEAR_SUM,PREUSED_AND_SHARE_LABEL,AUDIT_SHARE_SUM,FIRST_SHARE_SUM,SHARE_ACCOUNT_CODE,SHARE_ACCOUNT_NAME,CONTRACTNO,MOVE_TYPE_CODE,MOVE_TYPE_NAME,USAGEDETAIL,BUDGETSETNAME,DISPOSEMODE,ISCANUPDATE,USAGEDETAIL_CODE,ARCHIVE_ENTRY_TYPE,BUDGETSETID,CASH_BUDGET_INSTANCE_CODE,CASH_BUDGET_INSTANCE_NAME,LOAN_BUDGET_INSTANCE_CODE,LOAN_BUDGET_INSTANCE_NAME,TELEPHONE,ONCE_SUPPLIER_CITY,ONCE_SUPPLIER_NAME,IS_ONCE_SUPPLIER,IS_UNIFY_PAY,CREDIT_ACCOUNT_COMPANY_CODE,SALARY_CODE,IS_PROVINCE_PAY,CONTRACT_NAME,CONTRACT_SUM,BIZ_TYPE_CODE,BIZ_ENTRY_CODE,CONTRACT_DESCRIPTION,PAYOFF_DESCRIPTION,PROJECT_NAME,SUPPLIER_COMPANY_CODE,BANK_CODE,IS_SPLIT_CREDIT,UN_DISPLAY_CODE,BUILD_FEE,WRITEOFF_CHECK_INFO_ID,IS_IMAGE_AUDITED,CLEAR_ACCOUNT,EXIST_CLEAR_ACCOUNT,CARRYOVER_INSTANCEID,AUDIT_REASON,CONTRACT_INVOICE_SUM,SAP_CERTIFICATE_CODE,ADJUST_TYPE,IS_PAYMENT,IS_EMERGENCY,SHARE_BEGIN_YEAR,SHARE_BEGIN_MONTH,SHARE_END_YEAR,SHARE_END_MONTH,BUSINESS_TYPE,LOCATION,AUDIT_HAPPEN_DATE,COMPANY_NAME_TXT,IS_GDTEL_INVOICE,RELATIVE_TYPE_CODE,RELATIVE_TYPE_NAME,IS_NEW_CONTRACT,OUT_GOODS_SUM,PAYABLE_TAXFEE_SUM,BIZ_ENTRY_NAME,TRADE_TYPE,CURRENCY,IS_SAP_FLAG,CASH_ITEM_NAME,WITHHOLDING_CITY_ID,WITHHOLDING_CITY_NAME,BIZ_ITEM_INST_ID,ACCOUNT_CODE,ACCOUNT_NAME,ACCOUNT_COMPANY_CODE,IS_DEBIT_CREDIT,BIZ_ITEM_TYPE,IS_STAFF_PAYMENT,IS_CARRYOVER,TIMESTAMP,TAX_FEE_MAIN_ID,RISK_LEVEL_FLAG,IS_CONTRACT_FIRST_PAY,OTHER_SYS_MAIN_ID,PROXY_TAX_AMOUNT,INVOICE_TYPE,IS_EXIST_KIND_GIFT,KIND_GIFT_SUM,KIND_GIFT_TAX_SUM,IS_INPUT_TAX,INPUT_TAX_SUM,IS_VAT_AUTHENTICATED,INPUT_TAX_TURN_SUM,INPUT_TAX_TURN_BIZ_TYPE,IS_EXCPAYMENT,CONTRACT_USED_TIMES,IS_GENERAL_PAYER,IS_NEED_IMAGE,SHOW_DATA_URL,WRITEOFF_ARCHIVE_ID,WRITEOFF_INSTANCE_CODE,FORM_TYPE_CODE,SUM,AUDIT_SUM,PAYOFF_SUM,AUDIT_PAYOFF_SUM,FILL_IN_ACCOUNT,FILL_IN_NAME,FILL_IN_DEP,FORM_AMOUNT,PAYTAXATTR,BUSIHAPPENDTIMEFLAG,OPERATORID,PROCESSINSTID,SELECTPREID,GUID,PARENTID,EXCEPTIONTYPE,SAPCREATOR,SAPCOMPANYCODE,YEAR,BILLTYPE,IRESULT,SUPPLIERTYPE,ORGID,ENERGYTYPE,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{paymentType}, #{status}, #{clientName}, #{clientCode}, #{supplierName}, #{supplierCode}, #{supplierBank},
            #{supplierAccount}, #{budgetItemGroupId}, #{packupArchiveId}, #{bizType}, #{isPay}, #{conTrans},
            #{isToPack}, #{budgetCode}, #{budgetName}, #{budgetItemCode}, #{budgetItemName}, #{cityId}, #{cityName},
            #{profitCenterCode}, #{profitCenterName}, #{costCenterId}, #{costCenterName}, #{createDate}, #{companyCode},
            #{isCategory}, #{creditAccountCode}, #{creditAccountName}, #{id}, #{isPrepay}, #{taxAdjustType},
            #{taxAdjustSum}, #{taxAdjustComments}, #{assessDataUrl}, #{provinceCode}, #{pickingMode},
            #{budgetGroupItemCode}, #{budgetGroupItemName}, #{responseCenterCode}, #{responseCenterName},
            #{attchmentgAdd}, #{isTaxAdjust}, #{abstractValue}, #{fillInProfitCenterId}, #{fillInCostCenterId},
            #{fillInProfitCenterName}, #{fillInCostCenterName}, #{replenishFillIn}, #{fillInRoleId},
            #{beAfterOperateSum}, #{feeType}, #{happenDate}, #{sapRemarkDate}, #{clearSum}, #{preusedAndShareLabel},
            #{auditShareSum}, #{firstShareSum}, #{shareAccountCode}, #{shareAccountName}, #{contractno},
            #{moveTypeCode}, #{moveTypeName},
            <!-- #{usage}, -->
            #{usagedetail}, #{budgetsetname}, #{disposemode}, #{iscanupdate}, #{usagedetailCode}, #{archiveEntryType},
            #{budgetsetid}, #{cashBudgetInstanceCode}, #{cashBudgetInstanceName}, #{loanBudgetInstanceCode},
            #{loanBudgetInstanceName}, #{telephone}, #{onceSupplierCity}, #{onceSupplierName}, #{isOnceSupplier},
            #{isUnifyPay}, #{creditAccountCompanyCode}, #{salaryCode}, #{isProvincePay}, #{contractName},
            #{contractSum}, #{bizTypeCode}, #{bizEntryCode}, #{contractDescription}, #{payoffDescription},
            #{projectName}, #{supplierCompanyCode}, #{bankCode}, #{isSplitCredit}, #{unDisplayCode}, #{buildFee},
            #{writeoffCheckInfoId}, #{isImageAudited}, #{clearAccount}, #{existClearAccount}, #{carryoverInstanceid},
            #{auditReason}, #{contractInvoiceSum}, #{sapCertificateCode}, #{adjustType}, #{isPayment}, #{isEmergency},
            #{shareBeginYear}, #{shareBeginMonth}, #{shareEndYear}, #{shareEndMonth}, #{businessType}, #{location},
            #{auditHappenDate}, #{companyNameTxt}, #{isGdtelInvoice}, #{relativeTypeCode}, #{relativeTypeName},
            #{isNewContract}, #{outGoodsSum}, #{payableTaxfeeSum}, #{bizEntryName}, #{tradeType}, #{currency},
            #{isSapFlag}, #{cashItemName}, #{withholdingCityId}, #{withholdingCityName}, #{bizItemInstId},
            #{accountCode}, #{accountName}, #{accountCompanyCode}, #{isDebitCredit}, #{bizItemType}, #{isStaffPayment},
            #{isCarryover}, #{timestamp}, #{taxFeeMainId}, #{riskLevelFlag}, #{isContractFirstPay}, #{otherSysMainId},
            #{proxyTaxAmount}, #{invoiceType}, #{isExistKindGift}, #{kindGiftSum}, #{kindGiftTaxSum}, #{isInputTax},
            #{inputTaxSum}, #{isVatAuthenticated}, #{inputTaxTurnSum}, #{inputTaxTurnBizType}, #{isExcpayment},
            #{contractUsedTimes}, #{isGeneralPayer}, #{isNeedImage}, #{showDataUrl}, #{writeoffArchiveId},
            #{writeoffInstanceCode}, #{formTypeCode}, #{sum}, #{auditSum}, #{payoffSum}, #{auditPayoffSum},
            #{fillInAccount}, #{fillInName}, #{fillInDep}, #{formAmount}, #{paytaxattr}, #{busihappendtimeflag},
            #{operatorid}, #{processinstid}, #{selectpreid}, #{guid}, #{parentid}, #{exceptiontype}, #{sapcreator},
            #{sapcompanycode}, #{year}, #{billtype}, #{iresult}, #{suppliertype}, #{orgid}, #{energytype},
        </trim>
    </insert>

    <insert id="insertWithoutAutoId" parameterType="com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill">
        insert into MSS_ACCOUNTBILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            PAYMENT_TYPE,STATUS,CLIENT_NAME,CLIENT_CODE,SUPPLIER_NAME,SUPPLIER_CODE,SUPPLIER_BANK,SUPPLIER_ACCOUNT,BUDGET_ITEM_GROUP_ID,PACKUP_ARCHIVE_ID,BIZ_TYPE,IS_PAY,CON_TRANS,IS_TO_PACK,BUDGET_CODE,BUDGET_NAME,BUDGET_ITEM_CODE,BUDGET_ITEM_NAME,CITY_ID,CITY_NAME,PROFIT_CENTER_CODE,PROFIT_CENTER_NAME,COST_CENTER_ID,COST_CENTER_NAME,CREATE_DATE,COMPANY_CODE,IS_CATEGORY,CREDIT_ACCOUNT_CODE,CREDIT_ACCOUNT_NAME,ID,IS_PREPAY,TAX_ADJUST_TYPE,TAX_ADJUST_SUM,TAX_ADJUST_COMMENTS,ASSESS_DATA_URL,PROVINCE_CODE,PICKING_MODE,BUDGET_GROUP_ITEM_CODE,BUDGET_GROUP_ITEM_NAME,RESPONSE_CENTER_CODE,RESPONSE_CENTER_NAME,ATTCHMENTG_ADD,IS_TAX_ADJUST,ABSTRACT,FILL_IN_PROFIT_CENTER_ID,FILL_IN_COST_CENTER_ID,FILL_IN_PROFIT_CENTER_NAME,FILL_IN_COST_CENTER_NAME,REPLENISH_FILL_IN,FILL_IN_ROLE_ID,BE_AFTER_OPERATE_SUM,FEE_TYPE,HAPPEN_DATE,SAP_REMARK_DATE,CLEAR_SUM,PREUSED_AND_SHARE_LABEL,AUDIT_SHARE_SUM,FIRST_SHARE_SUM,SHARE_ACCOUNT_CODE,SHARE_ACCOUNT_NAME,CONTRACTNO,MOVE_TYPE_CODE,MOVE_TYPE_NAME,USAGEDETAIL,BUDGETSETNAME,DISPOSEMODE,ISCANUPDATE,USAGEDETAIL_CODE,ARCHIVE_ENTRY_TYPE,BUDGETSETID,CASH_BUDGET_INSTANCE_CODE,CASH_BUDGET_INSTANCE_NAME,LOAN_BUDGET_INSTANCE_CODE,LOAN_BUDGET_INSTANCE_NAME,TELEPHONE,ONCE_SUPPLIER_CITY,ONCE_SUPPLIER_NAME,IS_ONCE_SUPPLIER,IS_UNIFY_PAY,CREDIT_ACCOUNT_COMPANY_CODE,SALARY_CODE,IS_PROVINCE_PAY,CONTRACT_NAME,CONTRACT_SUM,BIZ_TYPE_CODE,BIZ_ENTRY_CODE,CONTRACT_DESCRIPTION,PAYOFF_DESCRIPTION,PROJECT_NAME,SUPPLIER_COMPANY_CODE,BANK_CODE,IS_SPLIT_CREDIT,UN_DISPLAY_CODE,BUILD_FEE,WRITEOFF_CHECK_INFO_ID,IS_IMAGE_AUDITED,CLEAR_ACCOUNT,EXIST_CLEAR_ACCOUNT,CARRYOVER_INSTANCEID,AUDIT_REASON,CONTRACT_INVOICE_SUM,SAP_CERTIFICATE_CODE,ADJUST_TYPE,IS_PAYMENT,IS_EMERGENCY,SHARE_BEGIN_YEAR,SHARE_BEGIN_MONTH,SHARE_END_YEAR,SHARE_END_MONTH,BUSINESS_TYPE,LOCATION,AUDIT_HAPPEN_DATE,COMPANY_NAME_TXT,IS_GDTEL_INVOICE,RELATIVE_TYPE_CODE,RELATIVE_TYPE_NAME,IS_NEW_CONTRACT,OUT_GOODS_SUM,PAYABLE_TAXFEE_SUM,BIZ_ENTRY_NAME,TRADE_TYPE,CURRENCY,IS_SAP_FLAG,CASH_ITEM_NAME,WITHHOLDING_CITY_ID,WITHHOLDING_CITY_NAME,BIZ_ITEM_INST_ID,ACCOUNT_CODE,ACCOUNT_NAME,ACCOUNT_COMPANY_CODE,IS_DEBIT_CREDIT,BIZ_ITEM_TYPE,IS_STAFF_PAYMENT,IS_CARRYOVER,TIMESTAMP,TAX_FEE_MAIN_ID,RISK_LEVEL_FLAG,IS_CONTRACT_FIRST_PAY,OTHER_SYS_MAIN_ID,PROXY_TAX_AMOUNT,INVOICE_TYPE,IS_EXIST_KIND_GIFT,KIND_GIFT_SUM,KIND_GIFT_TAX_SUM,IS_INPUT_TAX,INPUT_TAX_SUM,IS_VAT_AUTHENTICATED,INPUT_TAX_TURN_SUM,INPUT_TAX_TURN_BIZ_TYPE,IS_EXCPAYMENT,CONTRACT_USED_TIMES,IS_GENERAL_PAYER,IS_NEED_IMAGE,SHOW_DATA_URL,WRITEOFF_ARCHIVE_ID,WRITEOFF_INSTANCE_CODE,FORM_TYPE_CODE,SUM,AUDIT_SUM,PAYOFF_SUM,AUDIT_PAYOFF_SUM,FILL_IN_ACCOUNT,FILL_IN_NAME,FILL_IN_DEP,FORM_AMOUNT,PAYTAXATTR,BUSIHAPPENDTIMEFLAG,OPERATORID,PROCESSINSTID,SELECTPREID,GUID,PARENTID,EXCEPTIONTYPE,SAPCREATOR,SAPCOMPANYCODE,YEAR,BILLTYPE,IRESULT,SUPPLIERTYPE,ORGID,ENERGYTYPE,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{paymentType}, #{status}, #{clientName}, #{clientCode}, #{supplierName}, #{supplierCode}, #{supplierBank},
            #{supplierAccount}, #{budgetItemGroupId}, #{packupArchiveId}, #{bizType}, #{isPay}, #{conTrans},
            #{isToPack}, #{budgetCode}, #{budgetName}, #{budgetItemCode}, #{budgetItemName}, #{cityId}, #{cityName},
            #{profitCenterCode}, #{profitCenterName}, #{costCenterId}, #{costCenterName}, #{createDate}, #{companyCode},
            #{isCategory}, #{creditAccountCode}, #{creditAccountName}, #{id}, #{isPrepay}, #{taxAdjustType},
            #{taxAdjustSum}, #{taxAdjustComments}, #{assessDataUrl}, #{provinceCode}, #{pickingMode},
            #{budgetGroupItemCode}, #{budgetGroupItemName}, #{responseCenterCode}, #{responseCenterName},
            #{attchmentgAdd}, #{isTaxAdjust}, #{abstractValue}, #{fillInProfitCenterId}, #{fillInCostCenterId},
            #{fillInProfitCenterName}, #{fillInCostCenterName}, #{replenishFillIn}, #{fillInRoleId},
            #{beAfterOperateSum}, #{feeType}, #{happenDate}, #{sapRemarkDate}, #{clearSum}, #{preusedAndShareLabel},
            #{auditShareSum}, #{firstShareSum}, #{shareAccountCode}, #{shareAccountName}, #{contractno},
            #{moveTypeCode}, #{moveTypeName},
            <!-- #{usage}, -->
            #{usagedetail}, #{budgetsetname}, #{disposemode}, #{iscanupdate}, #{usagedetailCode}, #{archiveEntryType},
            #{budgetsetid}, #{cashBudgetInstanceCode}, #{cashBudgetInstanceName}, #{loanBudgetInstanceCode},
            #{loanBudgetInstanceName}, #{telephone}, #{onceSupplierCity}, #{onceSupplierName}, #{isOnceSupplier},
            #{isUnifyPay}, #{creditAccountCompanyCode}, #{salaryCode}, #{isProvincePay}, #{contractName},
            #{contractSum}, #{bizTypeCode}, #{bizEntryCode}, #{contractDescription}, #{payoffDescription},
            #{projectName}, #{supplierCompanyCode}, #{bankCode}, #{isSplitCredit}, #{unDisplayCode}, #{buildFee},
            #{writeoffCheckInfoId}, #{isImageAudited}, #{clearAccount}, #{existClearAccount}, #{carryoverInstanceid},
            #{auditReason}, #{contractInvoiceSum}, #{sapCertificateCode}, #{adjustType}, #{isPayment}, #{isEmergency},
            #{shareBeginYear}, #{shareBeginMonth}, #{shareEndYear}, #{shareEndMonth}, #{businessType}, #{location},
            #{auditHappenDate}, #{companyNameTxt}, #{isGdtelInvoice}, #{relativeTypeCode}, #{relativeTypeName},
            #{isNewContract}, #{outGoodsSum}, #{payableTaxfeeSum}, #{bizEntryName}, #{tradeType}, #{currency},
            #{isSapFlag}, #{cashItemName}, #{withholdingCityId}, #{withholdingCityName}, #{bizItemInstId},
            #{accountCode}, #{accountName}, #{accountCompanyCode}, #{isDebitCredit}, #{bizItemType}, #{isStaffPayment},
            #{isCarryover}, #{timestamp}, #{taxFeeMainId}, #{riskLevelFlag}, #{isContractFirstPay}, #{otherSysMainId},
            #{proxyTaxAmount}, #{invoiceType}, #{isExistKindGift}, #{kindGiftSum}, #{kindGiftTaxSum}, #{isInputTax},
            #{inputTaxSum}, #{isVatAuthenticated}, #{inputTaxTurnSum}, #{inputTaxTurnBizType}, #{isExcpayment},
            #{contractUsedTimes}, #{isGeneralPayer}, #{isNeedImage}, #{showDataUrl}, #{writeoffArchiveId},
            #{writeoffInstanceCode}, #{formTypeCode}, #{sum}, #{auditSum}, #{payoffSum}, #{auditPayoffSum},
            #{fillInAccount}, #{fillInName}, #{fillInDep}, #{formAmount}, #{paytaxattr}, #{busihappendtimeflag},
            #{operatorid}, #{processinstid}, #{selectpreid}, #{guid}, #{parentid}, #{exceptiontype}, #{sapcreator},
            #{sapcompanycode}, #{year}, #{billtype}, #{iresult}, #{suppliertype}, #{orgid}, #{energytype},
        </trim>
    </insert>

    <!-- 批量插入 -->
    <!--    TODO:sql 修改过-->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into MSS_ACCOUNTBILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            PAYMENT_TYPE,
            STATUS,
            CLIENT_NAME,
            CLIENT_CODE,
            SUPPLIER_NAME,
            SUPPLIER_CODE,
            SUPPLIER_BANK,
            SUPPLIER_ACCOUNT,
            BUDGET_ITEM_GROUP_ID,
            PACKUP_ARCHIVE_ID,
            BIZ_TYPE,
            IS_PAY,
            CON_TRANS,
            IS_TO_PACK,
            BUDGET_CODE,
            BUDGET_NAME,
            BUDGET_ITEM_CODE,
            BUDGET_ITEM_NAME,
            CITY_ID,
            CITY_NAME,
            PROFIT_CENTER_CODE,
            PROFIT_CENTER_NAME,
            COST_CENTER_ID,
            COST_CENTER_NAME,
            CREATE_DATE,
            COMPANY_CODE,
            IS_CATEGORY,
            CREDIT_ACCOUNT_CODE,
            CREDIT_ACCOUNT_NAME,
            ID,
            IS_PREPAY,
            TAX_ADJUST_TYPE,
            TAX_ADJUST_SUM,
            TAX_ADJUST_COMMENTS,
            ASSESS_DATA_URL,
            PROVINCE_CODE,
            PICKING_MODE,
            BUDGET_GROUP_ITEM_CODE,
            BUDGET_GROUP_ITEM_NAME,
            RESPONSE_CENTER_CODE,
            RESPONSE_CENTER_NAME,
            ATTCHMENTG_ADD,
            IS_TAX_ADJUST,
            ABSTRACT,
            FILL_IN_PROFIT_CENTER_ID,
            FILL_IN_COST_CENTER_ID,
            FILL_IN_PROFIT_CENTER_NAME,
            FILL_IN_COST_CENTER_NAME,
            REPLENISH_FILL_IN,
            FILL_IN_ROLE_ID,
            BE_AFTER_OPERATE_SUM,
            FEE_TYPE,
            HAPPEN_DATE,
            SAP_REMARK_DATE,
            CLEAR_SUM,
            PREUSED_AND_SHARE_LABEL,
            AUDIT_SHARE_SUM,
            FIRST_SHARE_SUM,
            SHARE_ACCOUNT_CODE,
            SHARE_ACCOUNT_NAME,
            CONTRACTNO,
            MOVE_TYPE_CODE,
            MOVE_TYPE_NAME,
            <!-- USAGE, -->
            USAGEDETAIL,
            BUDGETSETNAME,
            DISPOSEMODE,
            ISCANUPDATE,
            USAGEDETAIL_CODE,
            ARCHIVE_ENTRY_TYPE,
            BUDGETSETID,
            CASH_BUDGET_INSTANCE_CODE,
            CASH_BUDGET_INSTANCE_NAME,
            LOAN_BUDGET_INSTANCE_CODE,
            LOAN_BUDGET_INSTANCE_NAME,
            TELEPHONE,
            ONCE_SUPPLIER_CITY,
            ONCE_SUPPLIER_NAME,
            IS_ONCE_SUPPLIER,
            IS_UNIFY_PAY,
            CREDIT_ACCOUNT_COMPANY_CODE,
            SALARY_CODE,
            IS_PROVINCE_PAY,
            CONTRACT_NAME,
            CONTRACT_SUM,
            BIZ_TYPE_CODE,
            BIZ_ENTRY_CODE,
            CONTRACT_DESCRIPTION,
            PAYOFF_DESCRIPTION,
            PROJECT_NAME,
            SUPPLIER_COMPANY_CODE,
            BANK_CODE,
            IS_SPLIT_CREDIT,
            UN_DISPLAY_CODE,
            BUILD_FEE,
            WRITEOFF_CHECK_INFO_ID,
            IS_IMAGE_AUDITED,
            CLEAR_ACCOUNT,
            EXIST_CLEAR_ACCOUNT,
            CARRYOVER_INSTANCEID,
            AUDIT_REASON,
            CONTRACT_INVOICE_SUM,
            SAP_CERTIFICATE_CODE,
            ADJUST_TYPE,
            IS_PAYMENT,
            IS_EMERGENCY,
            SHARE_BEGIN_YEAR,
            SHARE_BEGIN_MONTH,
            SHARE_END_YEAR,
            SHARE_END_MONTH,
            BUSINESS_TYPE,
            LOCATION,
            AUDIT_HAPPEN_DATE,
            COMPANY_NAME_TXT,
            IS_GDTEL_INVOICE,
            RELATIVE_TYPE_CODE,
            RELATIVE_TYPE_NAME,
            IS_NEW_CONTRACT,
            OUT_GOODS_SUM,
            PAYABLE_TAXFEE_SUM,
            BIZ_ENTRY_NAME,
            TRADE_TYPE,
            CURRENCY,
            IS_SAP_FLAG,
            CASH_ITEM_NAME,
            WITHHOLDING_CITY_ID,
            WITHHOLDING_CITY_NAME,
            BIZ_ITEM_INST_ID,
            ACCOUNT_CODE,
            ACCOUNT_NAME,
            ACCOUNT_COMPANY_CODE,
            IS_DEBIT_CREDIT,
            BIZ_ITEM_TYPE,
            IS_STAFF_PAYMENT,
            IS_CARRYOVER,
            TIMESTAMP,
            TAX_FEE_MAIN_ID,
            RISK_LEVEL_FLAG,
            IS_CONTRACT_FIRST_PAY,
            OTHER_SYS_MAIN_ID,
            PROXY_TAX_AMOUNT,
            INVOICE_TYPE,
            IS_EXIST_KIND_GIFT,
            KIND_GIFT_SUM,
            KIND_GIFT_TAX_SUM,
            IS_INPUT_TAX,
            INPUT_TAX_SUM,
            IS_VAT_AUTHENTICATED,
            INPUT_TAX_TURN_SUM,
            INPUT_TAX_TURN_BIZ_TYPE,
            IS_EXCPAYMENT,
            CONTRACT_USED_TIMES,
            IS_GENERAL_PAYER,
            IS_NEED_IMAGE,
            SHOW_DATA_URL,
            WRITEOFF_ARCHIVE_ID,
            WRITEOFF_INSTANCE_CODE,
            FORM_TYPE_CODE,
            SUM,
            AUDIT_SUM,
            PAYOFF_SUM,
            AUDIT_PAYOFF_SUM,
            FILL_IN_ACCOUNT,
            FILL_IN_NAME,
            FILL_IN_DEP,
            FORM_AMOUNT,
            PAYTAXATTR,
            BUSIHAPPENDTIMEFLAG,
            OPERATORID,
            PROCESSINSTID,
            SELECTPREID,
            GUID,
            PARENTID,
            EXCEPTIONTYPE,
            SAPCREATOR,
            SAPCOMPANYCODE,
            YEAR,
            BILLTYPE,
            IRESULT,
            SUPPLIERTYPE,
            ORGID,
            ENERGYTYPE,
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.paymentType},
                #{item.status},
                #{item.clientName},
                #{item.clientCode},
                #{item.supplierName},
                #{item.supplierCode},
                #{item.supplierBank},
                #{item.supplierAccount},
                #{item.budgetItemGroupId},
                #{item.packupArchiveId},
                #{item.bizType},
                #{item.isPay},
                #{item.conTrans},
                #{item.isToPack},
                #{item.budgetCode},
                #{item.budgetName},
                #{item.budgetItemCode},
                #{item.budgetItemName},
                #{item.cityId},
                #{item.cityName},
                #{item.profitCenterCode},
                #{item.profitCenterName},
                #{item.costCenterId},
                #{item.costCenterName},
                #{item.createDate},
                #{item.companyCode},
                #{item.isCategory},
                #{item.creditAccountCode},
                #{item.creditAccountName},
                #{item.id},
                #{item.isPrepay},
                #{item.taxAdjustType},
                #{item.taxAdjustSum},
                #{item.taxAdjustComments},
                #{item.assessDataUrl},
                #{item.provinceCode},
                #{item.pickingMode},
                #{item.budgetGroupItemCode},
                #{item.budgetGroupItemName},
                #{item.responseCenterCode},
                #{item.responseCenterName},
                #{item.attchmentgAdd},
                #{item.isTaxAdjust},
                #{item.abstractValue},
                #{item.fillInProfitCenterId},
                #{item.fillInCostCenterId},
                #{item.fillInProfitCenterName},
                #{item.fillInCostCenterName},
                #{item.replenishFillIn},
                #{item.fillInRoleId},
                #{item.beAfterOperateSum},
                #{item.feeType},
                #{item.happenDate},
                #{item.sapRemarkDate},
                #{item.clearSum},
                #{item.preusedAndShareLabel},
                #{item.auditShareSum},
                #{item.firstShareSum},
                #{item.shareAccountCode},
                #{item.shareAccountName},
                #{item.contractno},
                #{item.moveTypeCode},
                #{item.moveTypeName},
                <!-- #{item.usage}, -->
                #{item.usagedetail},
                #{item.budgetsetname},
                #{item.disposemode},
                #{item.iscanupdate},
                #{item.usagedetailCode},
                #{item.archiveEntryType},
                #{item.budgetsetid},
                #{item.cashBudgetInstanceCode},
                #{item.cashBudgetInstanceName},
                #{item.loanBudgetInstanceCode},
                #{item.loanBudgetInstanceName},
                #{item.telephone},
                #{item.onceSupplierCity},
                #{item.onceSupplierName},
                #{item.isOnceSupplier},
                #{item.isUnifyPay},
                #{item.creditAccountCompanyCode},
                #{item.salaryCode},
                #{item.isProvincePay},
                #{item.contractName},
                #{item.contractSum},
                #{item.bizTypeCode},
                #{item.bizEntryCode},
                #{item.contractDescription},
                #{item.payoffDescription},
                #{item.projectName},
                #{item.supplierCompanyCode},
                #{item.bankCode},
                #{item.isSplitCredit},
                #{item.unDisplayCode},
                #{item.buildFee},
                #{item.writeoffCheckInfoId},
                #{item.isImageAudited},
                #{item.clearAccount},
                #{item.existClearAccount},
                #{item.carryoverInstanceid},
                #{item.auditReason},
                #{item.contractInvoiceSum},
                #{item.sapCertificateCode},
                #{item.adjustType},
                #{item.isPayment},
                #{item.isEmergency},
                #{item.shareBeginYear},
                #{item.shareBeginMonth},
                #{item.shareEndYear},
                #{item.shareEndMonth},
                #{item.businessType},
                #{item.location},
                #{item.auditHappenDate},
                #{item.companyNameTxt},
                #{item.isGdtelInvoice},
                #{item.relativeTypeCode},
                #{item.relativeTypeName},
                #{item.isNewContract},
                #{item.outGoodsSum},
                #{item.payableTaxfeeSum},
                #{item.bizEntryName},
                #{item.tradeType},
                #{item.currency},
                #{item.isSapFlag},
                #{item.cashItemName},
                #{item.withholdingCityId},
                #{item.withholdingCityName},
                #{item.bizItemInstId},
                #{item.accountCode},
                #{item.accountName},
                #{item.accountCompanyCode},
                #{item.isDebitCredit},
                #{item.bizItemType},
                #{item.isStaffPayment},
                #{item.isCarryover},
                #{item.timestamp},
                #{item.taxFeeMainId},
                #{item.riskLevelFlag},
                #{item.isContractFirstPay},
                #{item.otherSysMainId},
                #{item.proxyTaxAmount},
                #{item.invoiceType},
                #{item.isExistKindGift},
                #{item.kindGiftSum},
                #{item.kindGiftTaxSum},
                #{item.isInputTax},
                #{item.inputTaxSum},
                #{item.isVatAuthenticated},
                #{item.inputTaxTurnSum},
                #{item.inputTaxTurnBizType},
                #{item.isExcpayment},
                #{item.contractUsedTimes},
                #{item.isGeneralPayer},
                #{item.isNeedImage},
                #{item.showDataUrl},
                #{item.writeoffArchiveId},
                #{item.writeoffInstanceCode},
                #{item.formTypeCode},
                #{item.sum},
                #{item.auditSum},
                #{item.payoffSum},
                #{item.auditPayoffSum},
                #{item.fillInAccount},
                #{item.fillInName},
                #{item.fillInDep},
                #{item.formAmount},
                #{item.paytaxattr},
                #{item.busihappendtimeflag},
                #{item.operatorid},
                #{item.processinstid},
                #{item.selectpreid},
                #{item.guid},
                #{item.parentid},
                #{item.exceptiontype},
                #{item.sapcreator},
                #{item.sapcompanycode},
                #{item.year},
                #{item.billtype},
                #{item.iresult},
                #{item.suppliertype},
                #{item.orgid},
                #{item.energytype},
            </trim>
        </foreach>
    </insert>

    <insert id="insertListDiffSA">
        insert into diffsa (billId, auditTime, ammeter, lastAmmeter, inputDate, del_flag)
        values
        <foreach collection="list" item="item" index="index" separator=",">

        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="MssAccountbill">
        update MSS_ACCOUNTBILL
        <trim prefix="SET" suffixOverrides=",">
            PAYMENT_TYPE = #{paymentType},
            STATUS = #{status},
            CLIENT_NAME = #{clientName},
            CLIENT_CODE = #{clientCode},
            SUPPLIER_NAME = #{supplierName},
            SUPPLIER_CODE = #{supplierCode},
            SUPPLIER_BANK = #{supplierBank},
            SUPPLIER_ACCOUNT = #{supplierAccount},
            BUDGET_ITEM_GROUP_ID = #{budgetItemGroupId},
            PACKUP_ARCHIVE_ID = #{packupArchiveId},
            BIZ_TYPE = #{bizType},
            IS_PAY = #{isPay},
            CON_TRANS = #{conTrans},
            IS_TO_PACK = #{isToPack},
            BUDGET_CODE = #{budgetCode},
            BUDGET_NAME = #{budgetName},
            BUDGET_ITEM_CODE = #{budgetItemCode},
            BUDGET_ITEM_NAME = #{budgetItemName},
            CITY_ID = #{cityId},
            CITY_NAME = #{cityName},
            PROFIT_CENTER_CODE = #{profitCenterCode},
            PROFIT_CENTER_NAME = #{profitCenterName},
            COST_CENTER_ID = #{costCenterId},
            COST_CENTER_NAME = #{costCenterName},
            CREATE_DATE = #{createDate},
            COMPANY_CODE = #{companyCode},
            IS_CATEGORY = #{isCategory},
            CREDIT_ACCOUNT_CODE = #{creditAccountCode},
            CREDIT_ACCOUNT_NAME = #{creditAccountName},
            IS_PREPAY = #{isPrepay},
            TAX_ADJUST_TYPE = #{taxAdjustType},
            TAX_ADJUST_SUM = #{taxAdjustSum},
            TAX_ADJUST_COMMENTS = #{taxAdjustComments},
            ASSESS_DATA_URL = #{assessDataUrl},
            PROVINCE_CODE = #{provinceCode},
            PICKING_MODE = #{pickingMode},
            BUDGET_GROUP_ITEM_CODE = #{budgetGroupItemCode},
            BUDGET_GROUP_ITEM_NAME = #{budgetGroupItemName},
            RESPONSE_CENTER_CODE = #{responseCenterCode},
            RESPONSE_CENTER_NAME = #{responseCenterName},
            ATTCHMENTG_ADD = #{attchmentgAdd},
            IS_TAX_ADJUST = #{isTaxAdjust},
            ABSTRACT = #{abstractValue},
            FILL_IN_PROFIT_CENTER_ID = #{fillInProfitCenterId},
            FILL_IN_COST_CENTER_ID = #{fillInCostCenterId},
            FILL_IN_PROFIT_CENTER_NAME = #{fillInProfitCenterName},
            FILL_IN_COST_CENTER_NAME = #{fillInCostCenterName},
            REPLENISH_FILL_IN = #{replenishFillIn},
            FILL_IN_ROLE_ID = #{fillInRoleId},
            BE_AFTER_OPERATE_SUM = #{beAfterOperateSum},
            FEE_TYPE = #{feeType},
            HAPPEN_DATE = #{happenDate},
            SAP_REMARK_DATE = #{sapRemarkDate},
            CLEAR_SUM = #{clearSum},
            PREUSED_AND_SHARE_LABEL = #{preusedAndShareLabel},
            AUDIT_SHARE_SUM = #{auditShareSum},
            FIRST_SHARE_SUM = #{firstShareSum},
            SHARE_ACCOUNT_CODE = #{shareAccountCode},
            SHARE_ACCOUNT_NAME = #{shareAccountName},
            CONTRACTNO = #{contractno},
            MOVE_TYPE_CODE = #{moveTypeCode},
            MOVE_TYPE_NAME = #{moveTypeName},
            <!-- USAGE = #{usage}, -->
            USAGEDETAIL = #{usagedetail},
            BUDGETSETNAME = #{budgetsetname},
            DISPOSEMODE = #{disposemode},
            ISCANUPDATE = #{iscanupdate},
            USAGEDETAIL_CODE = #{usagedetailCode},
            ARCHIVE_ENTRY_TYPE = #{archiveEntryType},
            BUDGETSETID = #{budgetsetid},
            CASH_BUDGET_INSTANCE_CODE = #{cashBudgetInstanceCode},
            CASH_BUDGET_INSTANCE_NAME = #{cashBudgetInstanceName},
            LOAN_BUDGET_INSTANCE_CODE = #{loanBudgetInstanceCode},
            LOAN_BUDGET_INSTANCE_NAME = #{loanBudgetInstanceName},
            TELEPHONE = #{telephone},
            ONCE_SUPPLIER_CITY = #{onceSupplierCity},
            ONCE_SUPPLIER_NAME = #{onceSupplierName},
            IS_ONCE_SUPPLIER = #{isOnceSupplier},
            IS_UNIFY_PAY = #{isUnifyPay},
            CREDIT_ACCOUNT_COMPANY_CODE = #{creditAccountCompanyCode},
            SALARY_CODE = #{salaryCode},
            IS_PROVINCE_PAY = #{isProvincePay},
            CONTRACT_NAME = #{contractName},
            CONTRACT_SUM = #{contractSum},
            BIZ_TYPE_CODE = #{bizTypeCode},
            BIZ_ENTRY_CODE = #{bizEntryCode},
            CONTRACT_DESCRIPTION = #{contractDescription},
            PAYOFF_DESCRIPTION = #{payoffDescription},
            PROJECT_NAME = #{projectName},
            SUPPLIER_COMPANY_CODE = #{supplierCompanyCode},
            BANK_CODE = #{bankCode},
            IS_SPLIT_CREDIT = #{isSplitCredit},
            UN_DISPLAY_CODE = #{unDisplayCode},
            BUILD_FEE = #{buildFee},
            WRITEOFF_CHECK_INFO_ID = #{writeoffCheckInfoId},
            IS_IMAGE_AUDITED = #{isImageAudited},
            CLEAR_ACCOUNT = #{clearAccount},
            EXIST_CLEAR_ACCOUNT = #{existClearAccount},
            CARRYOVER_INSTANCEID = #{carryoverInstanceid},
            AUDIT_REASON = #{auditReason},
            CONTRACT_INVOICE_SUM = #{contractInvoiceSum},
            SAP_CERTIFICATE_CODE = #{sapCertificateCode},
            ADJUST_TYPE = #{adjustType},
            IS_PAYMENT = #{isPayment},
            IS_EMERGENCY = #{isEmergency},
            SHARE_BEGIN_YEAR = #{shareBeginYear},
            SHARE_BEGIN_MONTH = #{shareBeginMonth},
            SHARE_END_YEAR = #{shareEndYear},
            SHARE_END_MONTH = #{shareEndMonth},
            BUSINESS_TYPE = #{businessType},
            LOCATION = #{location},
            AUDIT_HAPPEN_DATE = #{auditHappenDate},
            COMPANY_NAME_TXT = #{companyNameTxt},
            IS_GDTEL_INVOICE = #{isGdtelInvoice},
            RELATIVE_TYPE_CODE = #{relativeTypeCode},
            RELATIVE_TYPE_NAME = #{relativeTypeName},
            IS_NEW_CONTRACT = #{isNewContract},
            OUT_GOODS_SUM = #{outGoodsSum},
            PAYABLE_TAXFEE_SUM = #{payableTaxfeeSum},
            BIZ_ENTRY_NAME = #{bizEntryName},
            TRADE_TYPE = #{tradeType},
            CURRENCY = #{currency},
            IS_SAP_FLAG = #{isSapFlag},
            CASH_ITEM_NAME = #{cashItemName},
            WITHHOLDING_CITY_ID = #{withholdingCityId},
            WITHHOLDING_CITY_NAME = #{withholdingCityName},
            BIZ_ITEM_INST_ID = #{bizItemInstId},
            ACCOUNT_CODE = #{accountCode},
            ACCOUNT_NAME = #{accountName},
            ACCOUNT_COMPANY_CODE = #{accountCompanyCode},
            IS_DEBIT_CREDIT = #{isDebitCredit},
            BIZ_ITEM_TYPE = #{bizItemType},
            IS_STAFF_PAYMENT = #{isStaffPayment},
            IS_CARRYOVER = #{isCarryover},
            TIMESTAMP = #{timestamp},
            TAX_FEE_MAIN_ID = #{taxFeeMainId},
            RISK_LEVEL_FLAG = #{riskLevelFlag},
            IS_CONTRACT_FIRST_PAY = #{isContractFirstPay},
            OTHER_SYS_MAIN_ID = #{otherSysMainId},
            PROXY_TAX_AMOUNT = #{proxyTaxAmount},
            INVOICE_TYPE = #{invoiceType},
            IS_EXIST_KIND_GIFT = #{isExistKindGift},
            KIND_GIFT_SUM = #{kindGiftSum},
            KIND_GIFT_TAX_SUM = #{kindGiftTaxSum},
            IS_INPUT_TAX = #{isInputTax},
            INPUT_TAX_SUM = #{inputTaxSum},
            IS_VAT_AUTHENTICATED = #{isVatAuthenticated},
            INPUT_TAX_TURN_SUM = #{inputTaxTurnSum},
            INPUT_TAX_TURN_BIZ_TYPE = #{inputTaxTurnBizType},
            IS_EXCPAYMENT = #{isExcpayment},
            CONTRACT_USED_TIMES = #{contractUsedTimes},
            IS_GENERAL_PAYER = #{isGeneralPayer},
            IS_NEED_IMAGE = #{isNeedImage},
            SHOW_DATA_URL = #{showDataUrl},
            WRITEOFF_ARCHIVE_ID = #{writeoffArchiveId},
            WRITEOFF_INSTANCE_CODE = #{writeoffInstanceCode},
            FORM_TYPE_CODE = #{formTypeCode},
            SUM = #{sum},
            AUDIT_SUM = #{auditSum},
            PAYOFF_SUM = #{payoffSum},
            AUDIT_PAYOFF_SUM = #{auditPayoffSum},
            FILL_IN_ACCOUNT = #{fillInAccount},
            FILL_IN_NAME = #{fillInName},
            FILL_IN_DEP = #{fillInDep},
            FORM_AMOUNT = #{formAmount},
            PAYTAXATTR = #{paytaxattr},
            BUSIHAPPENDTIMEFLAG = #{busihappendtimeflag},
            OPERATORID = #{operatorid},
            PROCESSINSTID = #{processinstid},
            SELECTPREID = #{selectpreid},
            GUID = #{guid},
            PARENTID = #{parentid},
            EXCEPTIONTYPE = #{exceptiontype},
            SAPCREATOR = #{sapcreator},
            SAPCOMPANYCODE = #{sapcompanycode},
            YEAR = #{year},
            BILLTYPE = #{billtype},
            IRESULT = #{iresult},
            SUPPLIERTYPE = #{suppliertype},
            ORGID = #{orgid},
            ENERGYTYPE = #{energytype},
        </trim>
        where ID = #{id}
    </update>


    <update id="updateForModel" parameterType="MssAccountbill">
        update MSS_ACCOUNTBILL
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentType != null  ">PAYMENT_TYPE = #{paymentType},</if>
            <if test="status != null  ">STATUS = #{status},</if>
            <if test="clientName != null  and clientName != ''  ">CLIENT_NAME = #{clientName},</if>
            <if test="clientCode != null  and clientCode != ''  ">CLIENT_CODE = #{clientCode},</if>
            <if test="supplierName != null  and supplierName != ''  ">SUPPLIER_NAME = #{supplierName},</if>
            <if test="supplierCode != null  and supplierCode != ''  ">SUPPLIER_CODE = #{supplierCode},</if>
            <if test="supplierBank != null  and supplierBank != ''  ">SUPPLIER_BANK = #{supplierBank},</if>
            <if test="supplierAccount != null  and supplierAccount != ''  ">SUPPLIER_ACCOUNT = #{supplierAccount},</if>
            <if test="budgetItemGroupId != null  and budgetItemGroupId != ''  ">BUDGET_ITEM_GROUP_ID =
                #{budgetItemGroupId},
            </if>
            <if test="packupArchiveId != null  and packupArchiveId != ''  ">PACKUP_ARCHIVE_ID = #{packupArchiveId},</if>
            <if test="bizType != null  ">BIZ_TYPE = #{bizType},</if>
            <if test="isPay != null  ">IS_PAY = #{isPay},</if>
            <if test="conTrans != null  and conTrans != ''  ">CON_TRANS = #{conTrans},</if>
            <if test="isToPack != null  ">IS_TO_PACK = #{isToPack},</if>
            <if test="budgetCode != null  and budgetCode != ''  ">BUDGET_CODE = #{budgetCode},</if>
            <if test="budgetName != null  and budgetName != ''  ">BUDGET_NAME = #{budgetName},</if>
            <if test="budgetItemCode != null  and budgetItemCode != ''  ">BUDGET_ITEM_CODE = #{budgetItemCode},</if>
            <if test="budgetItemName != null  and budgetItemName != ''  ">BUDGET_ITEM_NAME = #{budgetItemName},</if>
            <if test="cityId != null  and cityId != ''  ">CITY_ID = #{cityId},</if>
            <if test="cityName != null  and cityName != ''  ">CITY_NAME = #{cityName},</if>
            <if test="profitCenterCode != null  and profitCenterCode != ''  ">PROFIT_CENTER_CODE =
                #{profitCenterCode},
            </if>
            <if test="profitCenterName != null  and profitCenterName != ''  ">PROFIT_CENTER_NAME =
                #{profitCenterName},
            </if>
            <if test="costCenterId != null  and costCenterId != ''  ">COST_CENTER_ID = #{costCenterId},</if>
            <if test="costCenterName != null  and costCenterName != ''  ">COST_CENTER_NAME = #{costCenterName},</if>
            <if test="createDate != null  ">CREATE_DATE = #{createDate},</if>
            <if test="companyCode != null  and companyCode != ''  ">COMPANY_CODE = #{companyCode},</if>
            <if test="isCategory != null  ">IS_CATEGORY = #{isCategory},</if>
            <if test="creditAccountCode != null  and creditAccountCode != ''  ">CREDIT_ACCOUNT_CODE =
                #{creditAccountCode},
            </if>
            <if test="creditAccountName != null  and creditAccountName != ''  ">CREDIT_ACCOUNT_NAME =
                #{creditAccountName},
            </if>
            <if test="isPrepay != null  and isPrepay != ''  ">IS_PREPAY = #{isPrepay},</if>
            <if test="taxAdjustType != null  and taxAdjustType != ''  ">TAX_ADJUST_TYPE = #{taxAdjustType},</if>
            <if test="taxAdjustSum != null  ">TAX_ADJUST_SUM = #{taxAdjustSum},</if>
            <if test="taxAdjustComments != null  and taxAdjustComments != ''  ">TAX_ADJUST_COMMENTS =
                #{taxAdjustComments},
            </if>
            <if test="assessDataUrl != null  and assessDataUrl != ''  ">ASSESS_DATA_URL = #{assessDataUrl},</if>
            <if test="provinceCode != null  and provinceCode != ''  ">PROVINCE_CODE = #{provinceCode},</if>
            <if test="pickingMode != null  and pickingMode != ''  ">PICKING_MODE = #{pickingMode},</if>
            <if test="budgetGroupItemCode != null  and budgetGroupItemCode != ''  ">BUDGET_GROUP_ITEM_CODE =
                #{budgetGroupItemCode},
            </if>
            <if test="budgetGroupItemName != null  and budgetGroupItemName != ''  ">BUDGET_GROUP_ITEM_NAME =
                #{budgetGroupItemName},
            </if>
            <if test="responseCenterCode != null  and responseCenterCode != ''  ">RESPONSE_CENTER_CODE =
                #{responseCenterCode},
            </if>
            <if test="responseCenterName != null  and responseCenterName != ''  ">RESPONSE_CENTER_NAME =
                #{responseCenterName},
            </if>
            <if test="attchmentgAdd != null  and attchmentgAdd != ''  ">ATTCHMENTG_ADD = #{attchmentgAdd},</if>
            <if test="isTaxAdjust != null  and isTaxAdjust != ''  ">IS_TAX_ADJUST = #{isTaxAdjust},</if>
            <if test="abstractValue != null  and abstractValue != ''  ">ABSTRACT = #{abstractValue},</if>
            <if test="fillInProfitCenterId != null  and fillInProfitCenterId != ''  ">FILL_IN_PROFIT_CENTER_ID =
                #{fillInProfitCenterId},
            </if>
            <if test="fillInCostCenterId != null  and fillInCostCenterId != ''  ">FILL_IN_COST_CENTER_ID =
                #{fillInCostCenterId},
            </if>
            <if test="fillInProfitCenterName != null  and fillInProfitCenterName != ''  ">FILL_IN_PROFIT_CENTER_NAME =
                #{fillInProfitCenterName},
            </if>
            <if test="fillInCostCenterName != null  and fillInCostCenterName != ''  ">FILL_IN_COST_CENTER_NAME =
                #{fillInCostCenterName},
            </if>
            <if test="replenishFillIn != null  ">REPLENISH_FILL_IN = #{replenishFillIn},</if>
            <if test="fillInRoleId != null  and fillInRoleId != ''  ">FILL_IN_ROLE_ID = #{fillInRoleId},</if>
            <if test="beAfterOperateSum != null  ">BE_AFTER_OPERATE_SUM = #{beAfterOperateSum},</if>
            <if test="feeType != null  and feeType != ''  ">FEE_TYPE = #{feeType},</if>
            <if test="happenDate != null  and happenDate != ''  ">HAPPEN_DATE = #{happenDate},</if>
            <if test="sapRemarkDate != null  ">SAP_REMARK_DATE = #{sapRemarkDate},</if>
            <if test="clearSum != null  ">CLEAR_SUM = #{clearSum},</if>
            <if test="preusedAndShareLabel != null  and preusedAndShareLabel != ''  ">PREUSED_AND_SHARE_LABEL =
                #{preusedAndShareLabel},
            </if>
            <if test="auditShareSum != null  ">AUDIT_SHARE_SUM = #{auditShareSum},</if>
            <if test="firstShareSum != null  ">FIRST_SHARE_SUM = #{firstShareSum},</if>
            <if test="shareAccountCode != null  and shareAccountCode != ''  ">SHARE_ACCOUNT_CODE =
                #{shareAccountCode},
            </if>
            <if test="shareAccountName != null  and shareAccountName != ''  ">SHARE_ACCOUNT_NAME =
                #{shareAccountName},
            </if>
            <if test="contractno != null  and contractno != ''  ">CONTRACTNO = #{contractno},</if>
            <if test="moveTypeCode != null  and moveTypeCode != ''  ">MOVE_TYPE_CODE = #{moveTypeCode},</if>
            <if test="moveTypeName != null  and moveTypeName != ''  ">MOVE_TYPE_NAME = #{moveTypeName},</if>
            <!--  <if test="usage != null  and usage != ''  ">USAGE = #{usage},</if> -->
            <if test="usagedetail != null  and usagedetail != ''  ">USAGEDETAIL = #{usagedetail},</if>
            <if test="budgetsetname != null  and budgetsetname != ''  ">BUDGETSETNAME = #{budgetsetname},</if>
            <if test="disposemode != null  and disposemode != ''  ">DISPOSEMODE = #{disposemode},</if>
            <if test="iscanupdate != null  and iscanupdate != ''  ">ISCANUPDATE = #{iscanupdate},</if>
            <if test="usagedetailCode != null  and usagedetailCode != ''  ">USAGEDETAIL_CODE = #{usagedetailCode},</if>
            <if test="archiveEntryType != null  ">ARCHIVE_ENTRY_TYPE = #{archiveEntryType},</if>
            <if test="budgetsetid != null  and budgetsetid != ''  ">BUDGETSETID = #{budgetsetid},</if>
            <if test="cashBudgetInstanceCode != null  and cashBudgetInstanceCode != ''  ">CASH_BUDGET_INSTANCE_CODE =
                #{cashBudgetInstanceCode},
            </if>
            <if test="cashBudgetInstanceName != null  and cashBudgetInstanceName != ''  ">CASH_BUDGET_INSTANCE_NAME =
                #{cashBudgetInstanceName},
            </if>
            <if test="loanBudgetInstanceCode != null  and loanBudgetInstanceCode != ''  ">LOAN_BUDGET_INSTANCE_CODE =
                #{loanBudgetInstanceCode},
            </if>
            <if test="loanBudgetInstanceName != null  and loanBudgetInstanceName != ''  ">LOAN_BUDGET_INSTANCE_NAME =
                #{loanBudgetInstanceName},
            </if>
            <if test="telephone != null  and telephone != ''  ">TELEPHONE = #{telephone},</if>
            <if test="onceSupplierCity != null  and onceSupplierCity != ''  ">ONCE_SUPPLIER_CITY =
                #{onceSupplierCity},
            </if>
            <if test="onceSupplierName != null  and onceSupplierName != ''  ">ONCE_SUPPLIER_NAME =
                #{onceSupplierName},
            </if>
            <if test="isOnceSupplier != null  and isOnceSupplier != ''  ">IS_ONCE_SUPPLIER = #{isOnceSupplier},</if>
            <if test="isUnifyPay != null  and isUnifyPay != ''  ">IS_UNIFY_PAY = #{isUnifyPay},</if>
            <if test="creditAccountCompanyCode != null  and creditAccountCompanyCode != ''  ">
                CREDIT_ACCOUNT_COMPANY_CODE = #{creditAccountCompanyCode},
            </if>
            <if test="salaryCode != null  and salaryCode != ''  ">SALARY_CODE = #{salaryCode},</if>
            <if test="isProvincePay != null  and isProvincePay != ''  ">IS_PROVINCE_PAY = #{isProvincePay},</if>
            <if test="contractName != null  and contractName != ''  ">CONTRACT_NAME = #{contractName},</if>
            <if test="contractSum != null  ">CONTRACT_SUM = #{contractSum},</if>
            <if test="bizTypeCode != null  and bizTypeCode != ''  ">BIZ_TYPE_CODE = #{bizTypeCode},</if>
            <if test="bizEntryCode != null  and bizEntryCode != ''  ">BIZ_ENTRY_CODE = #{bizEntryCode},</if>
            <if test="contractDescription != null  and contractDescription != ''  ">CONTRACT_DESCRIPTION =
                #{contractDescription},
            </if>
            <if test="payoffDescription != null  and payoffDescription != ''  ">PAYOFF_DESCRIPTION =
                #{payoffDescription},
            </if>
            <if test="projectName != null  and projectName != ''  ">PROJECT_NAME = #{projectName},</if>
            <if test="supplierCompanyCode != null  and supplierCompanyCode != ''  ">SUPPLIER_COMPANY_CODE =
                #{supplierCompanyCode},
            </if>
            <if test="bankCode != null  and bankCode != ''  ">BANK_CODE = #{bankCode},</if>
            <if test="isSplitCredit != null  and isSplitCredit != ''  ">IS_SPLIT_CREDIT = #{isSplitCredit},</if>
            <if test="unDisplayCode != null  and unDisplayCode != ''  ">UN_DISPLAY_CODE = #{unDisplayCode},</if>
            <if test="buildFee != null  and buildFee != ''  ">BUILD_FEE = #{buildFee},</if>
            <if test="writeoffCheckInfoId != null  and writeoffCheckInfoId != ''  ">WRITEOFF_CHECK_INFO_ID =
                #{writeoffCheckInfoId},
            </if>
            <if test="isImageAudited != null  and isImageAudited != ''  ">IS_IMAGE_AUDITED = #{isImageAudited},</if>
            <if test="clearAccount != null  and clearAccount != ''  ">CLEAR_ACCOUNT = #{clearAccount},</if>
            <if test="existClearAccount != null  and existClearAccount != ''  ">EXIST_CLEAR_ACCOUNT =
                #{existClearAccount},
            </if>
            <if test="carryoverInstanceid != null  and carryoverInstanceid != ''  ">CARRYOVER_INSTANCEID =
                #{carryoverInstanceid},
            </if>
            <if test="auditReason != null  and auditReason != ''  ">AUDIT_REASON = #{auditReason},</if>
            <if test="contractInvoiceSum != null  ">CONTRACT_INVOICE_SUM = #{contractInvoiceSum},</if>
            <if test="sapCertificateCode != null  and sapCertificateCode != ''  ">SAP_CERTIFICATE_CODE =
                #{sapCertificateCode},
            </if>
            <if test="adjustType != null  and adjustType != ''  ">ADJUST_TYPE = #{adjustType},</if>
            <if test="isPayment != null  and isPayment != ''  ">IS_PAYMENT = #{isPayment},</if>
            <if test="isEmergency != null  and isEmergency != ''  ">IS_EMERGENCY = #{isEmergency},</if>
            <if test="shareBeginYear != null  ">SHARE_BEGIN_YEAR = #{shareBeginYear},</if>
            <if test="shareBeginMonth != null  ">SHARE_BEGIN_MONTH = #{shareBeginMonth},</if>
            <if test="shareEndYear != null  ">SHARE_END_YEAR = #{shareEndYear},</if>
            <if test="shareEndMonth != null  ">SHARE_END_MONTH = #{shareEndMonth},</if>
            <if test="businessType != null  and businessType != ''  ">BUSINESS_TYPE = #{businessType},</if>
            <if test="location != null  and location != ''  ">LOCATION = #{location},</if>
            <if test="auditHappenDate != null  ">AUDIT_HAPPEN_DATE = #{auditHappenDate},</if>
            <if test="companyNameTxt != null  and companyNameTxt != ''  ">COMPANY_NAME_TXT = #{companyNameTxt},</if>
            <if test="isGdtelInvoice != null  and isGdtelInvoice != ''  ">IS_GDTEL_INVOICE = #{isGdtelInvoice},</if>
            <if test="relativeTypeCode != null  and relativeTypeCode != ''  ">RELATIVE_TYPE_CODE =
                #{relativeTypeCode},
            </if>
            <if test="relativeTypeName != null  and relativeTypeName != ''  ">RELATIVE_TYPE_NAME =
                #{relativeTypeName},
            </if>
            <if test="isNewContract != null  ">IS_NEW_CONTRACT = #{isNewContract},</if>
            <if test="outGoodsSum != null  ">OUT_GOODS_SUM = #{outGoodsSum},</if>
            <if test="payableTaxfeeSum != null  ">PAYABLE_TAXFEE_SUM = #{payableTaxfeeSum},</if>
            <if test="bizEntryName != null  and bizEntryName != ''  ">BIZ_ENTRY_NAME = #{bizEntryName},</if>
            <if test="tradeType != null  and tradeType != ''  ">TRADE_TYPE = #{tradeType},</if>
            <if test="currency != null  and currency != ''  ">CURRENCY = #{currency},</if>
            <if test="isSapFlag != null  ">IS_SAP_FLAG = #{isSapFlag},</if>
            <if test="cashItemName != null  and cashItemName != ''  ">CASH_ITEM_NAME = #{cashItemName},</if>
            <if test="withholdingCityId != null  and withholdingCityId != ''  ">WITHHOLDING_CITY_ID =
                #{withholdingCityId},
            </if>
            <if test="withholdingCityName != null  and withholdingCityName != ''  ">WITHHOLDING_CITY_NAME =
                #{withholdingCityName},
            </if>
            <if test="bizItemInstId != null  and bizItemInstId != ''  ">BIZ_ITEM_INST_ID = #{bizItemInstId},</if>
            <if test="accountCode != null  and accountCode != ''  ">ACCOUNT_CODE = #{accountCode},</if>
            <if test="accountName != null  and accountName != ''  ">ACCOUNT_NAME = #{accountName},</if>
            <if test="accountCompanyCode != null  and accountCompanyCode != ''  ">ACCOUNT_COMPANY_CODE =
                #{accountCompanyCode},
            </if>
            <if test="isDebitCredit != null  and isDebitCredit != ''  ">IS_DEBIT_CREDIT = #{isDebitCredit},</if>
            <if test="bizItemType != null  and bizItemType != ''  ">BIZ_ITEM_TYPE = #{bizItemType},</if>
            <if test="isStaffPayment != null  and isStaffPayment != ''  ">IS_STAFF_PAYMENT = #{isStaffPayment},</if>
            <if test="isCarryover != null  and isCarryover != ''  ">IS_CARRYOVER = #{isCarryover},</if>
            <if test="timestamp != null  ">TIMESTAMP = #{timestamp},</if>
            <if test="taxFeeMainId != null  and taxFeeMainId != ''  ">TAX_FEE_MAIN_ID = #{taxFeeMainId},</if>
            <if test="riskLevelFlag != null  and riskLevelFlag != ''  ">RISK_LEVEL_FLAG = #{riskLevelFlag},</if>
            <if test="isContractFirstPay != null  and isContractFirstPay != ''  ">IS_CONTRACT_FIRST_PAY =
                #{isContractFirstPay},
            </if>
            <if test="otherSysMainId != null  and otherSysMainId != ''  ">OTHER_SYS_MAIN_ID = #{otherSysMainId},</if>
            <if test="proxyTaxAmount != null  ">PROXY_TAX_AMOUNT = #{proxyTaxAmount},</if>
            <if test="invoiceType != null  and invoiceType != ''  ">INVOICE_TYPE = #{invoiceType},</if>
            <if test="isExistKindGift != null  and isExistKindGift != ''  ">IS_EXIST_KIND_GIFT = #{isExistKindGift},
            </if>
            <if test="kindGiftSum != null  ">KIND_GIFT_SUM = #{kindGiftSum},</if>
            <if test="kindGiftTaxSum != null  ">KIND_GIFT_TAX_SUM = #{kindGiftTaxSum},</if>
            <if test="isInputTax != null  and isInputTax != ''  ">IS_INPUT_TAX = #{isInputTax},</if>
            <if test="inputTaxSum != null  ">INPUT_TAX_SUM = #{inputTaxSum},</if>
            <if test="isVatAuthenticated != null  and isVatAuthenticated != ''  ">IS_VAT_AUTHENTICATED =
                #{isVatAuthenticated},
            </if>
            <if test="inputTaxTurnSum != null  ">INPUT_TAX_TURN_SUM = #{inputTaxTurnSum},</if>
            <if test="inputTaxTurnBizType != null  and inputTaxTurnBizType != ''  ">INPUT_TAX_TURN_BIZ_TYPE =
                #{inputTaxTurnBizType},
            </if>
            <if test="isExcpayment != null  and isExcpayment != ''  ">IS_EXCPAYMENT = #{isExcpayment},</if>
            <if test="contractUsedTimes != null  and contractUsedTimes != ''  ">CONTRACT_USED_TIMES =
                #{contractUsedTimes},
            </if>
            <if test="isGeneralPayer != null  and isGeneralPayer != ''  ">IS_GENERAL_PAYER = #{isGeneralPayer},</if>
            <if test="isNeedImage != null  ">IS_NEED_IMAGE = #{isNeedImage},</if>
            <if test="showDataUrl != null  and showDataUrl != ''  ">SHOW_DATA_URL = #{showDataUrl},</if>
            <if test="writeoffArchiveId != null  and writeoffArchiveId != ''  ">WRITEOFF_ARCHIVE_ID =
                #{writeoffArchiveId},
            </if>
            <if test="writeoffInstanceCode != null  and writeoffInstanceCode != ''  ">WRITEOFF_INSTANCE_CODE =
                #{writeoffInstanceCode},
            </if>
            <if test="formTypeCode != null  and formTypeCode != ''  ">FORM_TYPE_CODE = #{formTypeCode},</if>
            <if test="sum != null  ">SUM = #{sum},</if>
            <if test="auditSum != null  ">AUDIT_SUM = #{auditSum},</if>
            <if test="payoffSum != null  ">PAYOFF_SUM = #{payoffSum},</if>
            <if test="auditPayoffSum != null  ">AUDIT_PAYOFF_SUM = #{auditPayoffSum},</if>
            <if test="fillInAccount != null  and fillInAccount != ''  ">FILL_IN_ACCOUNT = #{fillInAccount},</if>
            <if test="fillInName != null  and fillInName != ''  ">FILL_IN_NAME = #{fillInName},</if>
            <if test="fillInDep != null  and fillInDep != ''  ">FILL_IN_DEP = #{fillInDep},</if>
            <if test="formAmount != null  ">FORM_AMOUNT = #{formAmount},</if>
            <if test="paytaxattr != null  and paytaxattr != ''  ">PAYTAXATTR = #{paytaxattr},</if>
            <if test="busihappendtimeflag != null  and busihappendtimeflag != ''  ">BUSIHAPPENDTIMEFLAG =
                #{busihappendtimeflag},
            </if>
            <if test="operatorid != null  ">OPERATORID = #{operatorid},</if>
            <if test="processinstid != null  ">PROCESSINSTID = #{processinstid},</if>
            <if test="selectpreid != null  ">SELECTPREID = #{selectpreid},</if>
            <if test="guid != null  and guid != ''  ">GUID = #{guid},</if>
            <if test="parentid != null  ">PARENTID = #{parentid},</if>
            <if test="exceptiontype != null  ">EXCEPTIONTYPE = #{exceptiontype},</if>
            <if test="sapcreator != null  and sapcreator != ''  ">SAPCREATOR = #{sapcreator},</if>
            <if test="sapcompanycode != null  and sapcompanycode != ''  ">SAPCOMPANYCODE = #{sapcompanycode},</if>
            <if test="year != null  and year != ''  ">YEAR = #{year},</if>
            <if test="billtype != null  ">BILLTYPE = #{billtype},</if>
            <if test="iresult != null  and iresult != ''  ">IRESULT = #{iresult},</if>
            <if test="suppliertype != null  and suppliertype != ''  ">SUPPLIERTYPE = #{suppliertype},</if>
            <if test="orgid != null  ">ORGID = #{orgid},</if>
            <if test="energytype != null  ">ENERGYTYPE = #{energytype},</if>
        </trim>
        where ID = #{id}
    </update>
    <update id="updatemeterdatesfortwoc">
        delete
        from meterdatesfortwoc
        where statisPeriod = replace(#{time}, '-', '');
    </update>
    <update id="updatemeterinfo">
        delete
        from meter_info_db
        where budget=#{budget};
    </update>
    <!-- 逻辑删除 -->
    <!-- 	<update id="deleteByPrimaryKey" parameterType="Map">
            UPDATE MSS_ACCOUNTBILL SET DEL_FLAG='1' where ID = #{id}
            <if test="shardKey != null and shardKey != ''"> and shardKey = #{shardKey}</if>
        </update>

        <update id="deleteByIds" parameterType="String">
            UPDATE MSS_ACCOUNTBILL SET DEL_FLAG='1' where ID in
            <foreach item="id" collection="array" open="(" separator="," close=")">
                #{id}
            </foreach>
        </update> -->

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from MSS_ACCOUNTBILL where ID = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from MSS_ACCOUNTBILL where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleJtForBillId">
        delete
        from power_station_info_r_jtlte
        where bill_id = #{billId}
        and stationid in (select si.id
        from power_account p,
        power_ammeterorprotocol pa,
        power_station_info si
        where p.ammeterid = pa.id
        and pa.stationcode = si.id
        and p.status in (2, 4)
        and pa.status = 1
        and p.pcid in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        );
    </delete>
    <delete id="deletePowerAuditByBillIds">
        delete
        from power_audit
        where mss_account_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <!--自定义查询条件-->
    <sql id="auto-condition">
        <if test="id != null">and ID = #{id}</if>
        <if test="status != null">and STATUS = #{status}</if>
        <if test="billtype != null">and BILLTYPE = #{billtype}</if>
        <if test="fillInDep != null">and FILL_IN_DEP like concat('%', #{fillInDep}, '%')</if>
        <if test="orgid != null">and orgid = #{orgid}</if>
        <if test="adjustType != null">and ADJUST_TYPE = #{adjustType}</if>
        <if test="fillInAccount != null">and FILL_IN_ACCOUNT = #{fillInAccount}</if>
        <if test="fillInName != null">and FILL_IN_NAME like concat('%', #{fillInName}, '%')</if>
        <if test="sum != null">and SUM like concat('%', #{sum}, '%')</if>
        <if test="createDateStart != null">and CREATE_DATE &gt;= #{createDateStart}</if>
        <if test="createDateEnd != null">and CREATE_DATE &lt; DATE_ADD(#{createDateEnd},INTERVAL 1 DAY)</if>
        <if test="supplierCode != null">and SUPPLIER_CODE = #{supplierCode}</if>
        <if test="supplierName != null">and SUPPLIER_NAME like concat('%', #{supplierName}, '%')</if>
        <if test="telephone != null">and TELEPHONE like concat('%', #{telephone}, '%')</if>
        <if test="busihappendtimeflag != null">and BUSIHAPPENDTIMEFLAG = #{busihappendtimeflag}</if>
        <if test="budgetsetname != null">and BUDGETSETNAME = #{budgetsetname}</if>
        <if test="happenDate != null">and HAPPEN_DATE = #{happenDate}</if>
        <if test="paymentType != null">and PAYMENT_TYPE = #{paymentType}</if>
        <if test="invoiceType != null">and INVOICE_TYPE = #{invoiceType}</if>
        <if test="bizTypeCode != null">and BIZ_TYPE_CODE = #{bizTypeCode}</if>
        <if test="pickingMode != null">and PICKING_MODE = #{pickingMode}</if>
        <if test="isStaffPayment != null">and IS_STAFF_PAYMENT = #{isStaffPayment}</if>
        <if test="isExistKindGift != null">and IS_EXIST_KIND_GIFT = #{isExistKindGift}</if>
        <if test="abstractValue != null">and ABSTRACT like concat('%', #{abstractValue}, '%')</if>
        <if test="writeoffInstanceCode != null">and WRITEOFF_INSTANCE_CODE like concat('%', #{writeoffInstanceCode},
            '%')
        </if>
        <if test="companyCode != null">and COMPANY_CODE = #{companyCode}</if>
    </sql>
    <select id="selectListByAuto" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        <where>
            <include refid="auto-condition"/>
            <include refid="countrys"/>
        </where>
        order by CREATE_DATE desc
    </select>

    <select id="getByOldOne" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        <where>
            id = (select max(m.id) from mss_accountbill m where m.FILL_IN_ACCOUNT =
            #{fillInAccount}
            )
        </where>
    </select>
    <select id="getmssbasecodebyPre" parameterType="string" resultType="string">
        SELECT (case
                    when v.type = '14' then 'A006'
                    when v.type = '11' then 'B006'
                    when v.type = '12' then 'B006'
                    else ''
            end)
                   as basecode
        FROM power_accountbillitempre p
                 LEFT JOIN power_account pa ON p.pcid = pa.pcid
                 LEFT JOIN power_ammeterorprotocol pam ON pa.ammeterid = pam.id
                 LEFT JOIN power_category_type pct ON pam.category = pct.type_code
            AND pct.type_category = 'ammeterCategory'
                 LEFT JOIN ecm.v_power_electric_classification v ON FIND_IN_SET(pam.electrotype, v.seq)
                 LEFT JOIN power_electric_classification ec ON ec.id = pam.electrotype
        WHERE p.parid = #{ID}
          AND pa.STATUS != 5
        limit 1
    </select>

    <sql id="check-condition">
        <if test="writeoffInstanceCode != null">and WRITEOFF_INSTANCE_CODE = #{writeoffInstanceCode}</if>
        <if test="id != null">and ID = #{id}</if>
        <if test="billtype != null">and BILLTYPE = #{billtype}</if>
        <if test="fillInDep != null">and FILL_IN_DEP like concat('%', #{fillInDep}, '%')</if>
        <if test="adjustType != null">and ADJUST_TYPE = #{adjustType}</if>
        <if test="fillInName != null">and FILL_IN_NAME like concat('%', #{fillInName}, '%')</if>
        <if test="sum != null">and SUM like concat('%', #{sum}, '%')</if>
        <if test="createDateStart != null">and CREATE_DATE &gt;= #{createDateStart}</if>
        <if test="createDateEnd != null">and CREATE_DATE &lt; DATE_ADD(#{createDateEnd},INTERVAL 1 DAY)</if>
        <if test="supplierCode != null">and SUPPLIER_CODE = #{supplierCode}</if>
        <if test="supplierName != null">and SUPPLIER_NAME like concat('%', #{supplierName}, '%')</if>
        <if test="telephone != null">and TELEPHONE like concat('%', #{telephone}, '%')</if>
        <if test="busihappendtimeflag != null">and BUSIHAPPENDTIMEFLAG = #{busihappendtimeflag}</if>
        <if test="budgetsetname != null">and BUDGETSETNAME = #{budgetsetname}</if>
        <if test="happenDate != null">and HAPPEN_DATE = #{happenDate}</if>
        <if test="paymentType != null">and PAYMENT_TYPE = #{paymentType}</if>
        <if test="invoiceType != null">and INVOICE_TYPE = #{invoiceType}</if>
        <if test="bizTypeCode != null">and BIZ_TYPE_CODE = #{bizTypeCode}</if>
        <if test="pickingMode != null">and PICKING_MODE = #{pickingMode}</if>
        <if test="isStaffPayment != null">and IS_STAFF_PAYMENT = #{isStaffPayment}</if>
        <if test="isExistKindGift != null">and IS_EXIST_KIND_GIFT = #{isExistKindGift}</if>
        <if test="abstractValue != null">and ABSTRACT like concat('%', #{abstractValue}, '%')</if>
        <if test="companyCode != null">and COMPANY_CODE = #{companyCode}</if>
        <if test="fillInCostCenterId != null">and FILL_IN_COST_CENTER_ID = #{fillInCostCenterId}</if>
    </sql>
    <select id="selectListByCheck" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        <where>
            STATUS in (4,7)
            <include refid="check-condition"/>
        </where>
        order by CREATE_DATE desc
    </select>
    <!--收款挑对查询-->
    <select id="selectListByCheckSK" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        SELECT
        m.BILLTYPE,
        i.id,
        m.WRITEOFF_INSTANCE_CODE,
        i.ABSTRACT,
        i.DEBIT_ACCOUNT_CODE SUPPLIER_CODE,
        i.DEBIT_ACCOUNT_NAME SUPPLIER_name,
        i.sum,
        i.TAX_ADJUST_SUM INPUT_TAX_SUM
        FROM
        mss_accountbillitem i
        LEFT JOIN mss_accountbill m on i.WRITEOFF_INSTANCE_ID = m.id
        <where>
            m.STATUS in (4,7)
            <if test="supplierCode != null">and i.DEBIT_ACCOUNT_CODE = #{supplierCode}</if>
            <if test="billtype != null">and m.billtype = #{billtype}</if>
            <if test="id != null">and m.id = #{id}</if>
            <if test="writeoffInstanceCode != null">and m.WRITEOFF_INSTANCE_CODE = #{writeoffInstanceCode}</if>
            <if test="abstractValue != null">and i.ABSTRACT like concat('%', #{abstractValue}, '%')</if>
            <if test="sum != null">and i.SUM like concat('%', #{sum}, '%')</if>
        </where>
        order by m.CREATE_DATE desc
    </select>

    <select id="selectListByIds" parameterType="String" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="statisticalAnalysis" parameterType="java.util.Map" resultType="Map">
        <if test="company == null">
            <if test="type == 0">
                select a.COMPANY_CODE unit,sum(case when a.PAYMENT_TYPE in (2,4) then a.sum+a.INPUT_TAX_SUM else 0 end)
                sum ,
                sum((select sum(sum) from mss_accountbillitem where BUDGET_TYPE=1 and WRITEOFF_INSTANCE_ID = a.id))
                cost,a.time
                from (
                select id,PAYMENT_TYPE,COMPANY_CODE, sum,IFNULL(INPUT_TAX_SUM,0) INPUT_TAX_SUM
                ,concat(`YEAR`,BIZ_ENTRY_CODE) time
                from mss_accountbill where `STATUS` in (4,7)
                and ((`YEAR`=#{startYear} and BIZ_ENTRY_CODE = #{startMonth}) or (`YEAR`=#{endYear} and BIZ_ENTRY_CODE =
                #{endMonth}))
                ) a
                group by unit,time order by unit
            </if>
            <if test="type == 1">
                select b.unit,sum(b.sum) sum, sum(cost) cost, time from (
                select a.COMPANY_CODE unit,sum(case when a.PAYMENT_TYPE in (2,4) then a.sum+a.INPUT_TAX_SUM else 0 end)
                sum ,
                sum((select sum(sum) from mss_accountbillitem where BUDGET_TYPE=1 and WRITEOFF_INSTANCE_ID = a.id))
                cost, time
                from (select id,PAYMENT_TYPE,COMPANY_CODE, sum,IFNULL(INPUT_TAX_SUM,0) INPUT_TAX_SUM ,`YEAR` time
                from mss_accountbill where `STATUS` in (4,7)
                and (`YEAR`=#{thisDate} or `YEAR`=#{lastDate})
                <include refid="months"></include>
                ) a
                group by unit,time order by unit) b group by unit,time order by unit
            </if>

        </if>
        <if test="company != null">
            <if test="type == 0">
                select a.ORGID unit,sum(case when a.PAYMENT_TYPE in (2,4) then a.sum+a.INPUT_TAX_SUM else 0 end) sum ,
                sum((select sum(sum) from mss_accountbillitem where BUDGET_TYPE=1 and WRITEOFF_INSTANCE_ID = a.id))
                cost,a.time
                from (select id,PAYMENT_TYPE,ORGID, sum,IFNULL(INPUT_TAX_SUM,0) INPUT_TAX_SUM
                ,concat(`YEAR`,BIZ_ENTRY_CODE) time
                from mss_accountbill where COMPANY_CODE=#{company}
                <include refid="countrys"></include>
                and `STATUS` in (4,7)
                and ((`YEAR`=#{startYear} and BIZ_ENTRY_CODE = #{startMonth}) or (`YEAR`=#{endYear} and BIZ_ENTRY_CODE =
                #{endMonth}))
                ) a
                group by unit,time order by unit
            </if>
            <if test="type == 1">
                select b.unit,sum(b.sum) sum, sum(cost) cost, time from (
                select a.ORGID unit,sum(case when a.PAYMENT_TYPE in (2,4) then a.sum+a.INPUT_TAX_SUM else 0 end) sum ,
                sum((select sum(sum) from mss_accountbillitem where BUDGET_TYPE=1 and WRITEOFF_INSTANCE_ID = a.id))
                cost,left(a.time,4) time
                from (select id,PAYMENT_TYPE,ORGID, sum,IFNULL(INPUT_TAX_SUM,0) INPUT_TAX_SUM ,`YEAR` time from
                mss_accountbill
                where COMPANY_CODE=#{company}
                <include refid="countrys"></include>
                and `STATUS` in (4,7)
                and (`YEAR`=#{thisDate} or `YEAR`=#{lastDate})
                <include refid="months"></include>
                ) a
                group by unit,time order by unit) b group by unit,time order by unit
            </if>
        </if>
    </select>
    <select id="selectListByIdsAndType"
            resultType="com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill">
        <include refid="selectVo"/>
        where ID in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and ENERGYTYPE = #{type}
    </select>

    <select id="getHistroy" resultType="com.sccl.modules.business.stationaudit.msshistory.MssHistory">
        select m.ID mssId
        from (select *
              from mss_accountbill) m
                 left join mss_r_billitem_account mrba on m.ID = mrba.bill_id
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id
        where pa.stationCode = #{stationCode}
          and (month(now()) - month(m.HAPPEN_DATE)) <![CDATA[<=]]> #{month};
    </select>

    <select id="getStationCode" resultType="java.lang.String">
        select pa.stationCode
        from (select *
              from mss_accountbill
              where ID = #{bill.id}) m
                 left join mss_r_billitem_account mrba on m.ID = mrba.bill_id
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id
        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
    </select>

    <select id="getHistroyForPower"
            resultType="com.sccl.modules.business.stationaudit.powerhistory.PowerHistory">
        select jt.jtlte_code                                                                   stationCode,
               ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                     ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2) power,
               p.inputdate                                                                     inputdate
        from (select *
              from mss_accountbill) m
                 left join mss_r_billitem_account mrba on m.ID = mrba.bill_id
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id
                 left join power_station_info_r_jtlte jt on jt.stationid = pa.stationcode
        where jtlte_code = #{stationCode}
          and (month(now()) - month(m.HAPPEN_DATE)) <![CDATA[ <=]]> #{month}
    </select>


    <select id="getLastBill" resultMap="MssAccountbillResult">
        select m.*
        from (select *
              from mss_accountbill) m
                 left join mss_r_billitem_account mrba on m.ID = mrba.bill_id
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id
                 left join power_station_info_r_jtlte jt on jt.stationid = pa.stationcode
        where jtlte_code = #{stationCode}
          and m.HAPPEN_DATE <![CDATA[<]]> #{happendate}
          and pa.electrotype in (1411, 1412)
        order by m.HAPPEN_DATE desc
        limit 1;
    </select>


    <select id="getPowerByMssId" resultType="java.math.BigDecimal">
        select SUM(ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                         ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2)) calcPower

        from mss_accountbill m
                 left join mss_r_billitem_account mrba on mrba.bill_id = m.ID
                 left join power_account p on p.pcid = mrba.account_id
        where m.ID = #{MsId}
        group by m.ID;
    </select>

    <select id="getPowerHistory"
            resultType="com.sccl.modules.business.noderesult.domain.NodeResult">
        select p.pcid                                                                          pcid,
               pa.id                                                                           ammeterid,
               pa.stationcode                                                                  stationcode,
               ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                     ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2) power,
               p.inputdate,
               datediff(p.enddate, p.startdate) + 1                                            diff,
               pa.projectname,
               pa.station_name                                                                 stationname,
               pa.stationaddress,
               mrba.money                                                                      accountmoney,
               pa.protocolsigneddate,
               pa.protocolterminatedate
        from (select *
              from mss_accountbill
              where ID = #{billId}
             ) m
                 left join mss_r_billitem_account mrba on m.ID = mrba.bill_id
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id
        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
    </select>
    <select id="getPowerHistoryOne" resultType="com.sccl.modules.business.noderesult.domain.NodeResult">
        select p.pcid                                                                          pcid,
               pa.id                                                                           ammeterid,
               mrba.bill_id                                                                    billid,
               pa.stationcode                                                                  stationcode,
               ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                     ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2) power,
               p.inputdate,
               datediff(p.enddate, p.startdate)                                                diff,
               pa.projectname,
               pa.station_name                                                                 stationname,
               pa.stationaddress
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and p.pcid = #{pcid};
    </select>

    <select id="getPowerHistoryLast" resultType="com.sccl.modules.business.noderesult.domain.NodeResult">
        select p.pcid                                                                          pcid,
               pa.id                                                                           ammeterid,
               mrba.bill_id                                                                    billid,
               pa.stationcode                                                                  stationcode,
               ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                     ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2) power,
               p.inputdate,
               datediff(p.enddate, p.startdate)                                                diff,
               pa.projectname,
               pa.station_name                                                                 stationname,
               pa.stationaddress,
               mrba.money                                                                      accountmoney
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and pa.stationcode = #{stationCode}
          and p.inputdate <![CDATA[<=]]> #{now}
          and p.del_flag = 0
        order by p.inputdate desc
        limit 1,1
    </select>

    <select id="getStationCodeLastSamemeter" resultType="java.lang.String">
        select group_concat(if(bb.stationcode != bb.rStationcode,
                               concat(bb.projectname, '-', bb.station_name, '->', bb.rprojectname, '-', bb.rstationname,
                                      '  创建时间:',
                                      bb.create_time),
                               null)) stationcodelast_samemeter
        from (select pa.id            ammeterId,
                     pa.stationcode   stationcode,
                     pa.projectname,
                     pa.station_name,
                     par.stationcode  rStationcode,
                     par.projectname  rprojectname,
                     par.station_name rstationname,
                     par.create_time
              from power_ammeterorprotocol pa
                       left join power_ammeterorprotocol_record par on pa.id = par.ammeter_protocol_id
              where pa.id = #{ammeterid}
                and year(pa.update_time) = year(par.create_time)) bb;
    </select>

    <select id="getMeterLastSameStationCode" resultType="java.lang.String">
        SELECT IF(dd.ids1 != dd.ids2,
                  CONCAT('当前站址电表:', dd.ids1, '->', '上期电表:', dd.ids2),
                  NULL) AS meterlast_samestationcode
        FROM (
                 SELECT *
                 FROM (
                       (
                           SELECT (CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END) AS ids1,
                                  pa.stationcode                                                           AS code1,
                                  p.startdate
                           FROM power_account p
                                    LEFT JOIN power_ammeterorprotocol pa ON p.ammeterid = pa.id
                           WHERE p.pcid = #{pcid}
                       ) aa
                          LEFT JOIN
                      (
                          SELECT (CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END) AS ids2,
                                 pa.stationcode                                                           AS code2,
                                 p.enddate
                          FROM power_account p
                                   LEFT JOIN power_ammeterorprotocol pa ON p.ammeterid = pa.id
                          WHERE pa.stationcode = #{stationcode}
                            AND pa.status = 1
                      ) bb
                      ON aa.code1 = bb.code2
                          AND DATE_ADD(aa.startdate, INTERVAL 0 DAY) = DATE_ADD(bb.enddate, INTERVAL 1 DAY)
                          )
             ) dd
        limit 1
    </select>
    <select id="getPowerHistoryOneNew"
            resultType="com.sccl.modules.business.stationaudit.pstationgrade.StationGradeExRefereeContent">
        select p.pcid                                                                          pcid,
               pa.id                                                                           ammeterid,
               mrba.bill_id                                                                    billid,
               pa.stationcode                                                                  stationcode,
               ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                     ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2) power,
               datediff(p.enddate, p.startdate)                                                diff,
               pa.projectname,
               pa.station_name                                                                 stationname,
               pa.stationaddress
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and p.pcid = #{pcid}
    </select>
    <select id="getPowerChagenNew"
            resultType="com.sccl.modules.business.stationaudit.pstationpowerchange.StationPowerChangeRefereeContent">
        select p.pcid                                                                   pcid,
               pa.id                                                                    ammeterid,
               (case when pa.category = 1 then pa.ammetername else pa.protocolname end) ammetername,
               mrba.bill_id                                                             billid,
               pa.stationcode                                                           stationcode,
               round(
                           ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                                 ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2)
                           /
                           (datediff(p.enddate, p.startdate) + 1), 2)
                                                                                        power,
               datediff(p.enddate, p.startdate) + 1                                     diff,
               pa.projectname,
               pa.station_name                                                          stationname,
               pa.stationaddress,
               p.startdate                                                              startdate
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and p.pcid = #{pcid}
    </select>
    <select id="getPowerChagenNewLast"
            resultType="com.sccl.modules.business.stationaudit.pstationpowerchange.StationPowerChangeRefereeContent">
        select p.pcid                                                                   pcid,
               pa.id                                                                    ammeterid,
               (case when pa.category = 1 then pa.ammetername else pa.protocolname end) ammetername,
               mrba.bill_id                                                             billid,
               pa.stationcode                                                           stationcode,
               round(
                           ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                                 ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2)
                           /
                           (datediff(p.enddate, p.startdate) + 1), 2)
                                                                                        power,
               p.startdate,
               p.enddate,
               datediff(p.enddate, p.startdate) + 1                                     diff,
               pa.projectname,
               pa.station_name                                                          stationname,
               pa.stationaddress
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and pa.stationcode = #{stationcode}
          and DATE_ADD(p.enddate, INTERVAL 1 DAY) = DATE_ADD(#{startdate}, INTERVAL 0 DAY)
          and p.del_flag = 0
        limit 1
    </select>

    <select id="getAccountChange"
            resultType="com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeRefereeContent">
        select p.pcid                                                                          pcid,
               pa.id                                                                           ammeterid,
               (case when pa.category = 1 then pa.ammetername else pa.protocolname end)        ammetername,
               mrba.bill_id                                                                    billid,
               pa.stationcode                                                                  stationcode,
               ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                     ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2) power,
               round(account_money / (datediff(p.enddate, p.startdate) + 1), 2)
                                                                                               accountmoney,
               datediff(p.enddate, p.startdate) + 1                                            diff,
               pa.projectname,
               pa.station_name                                                                 stationname,
               pa.stationaddress,
               p.startdate                                                                     startdate,
               p.enddate                                                                       enddate
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and p.pcid = #{pcid}

    </select>
    <select id="getAccountChangeLast"
            resultType="com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeRefereeContent">
        select p.pcid                                                                          pcid,
               pa.id                                                                           ammeterid,
               (case when pa.category = 1 then pa.ammetername else pa.protocolname end)        ammetername,
               mrba.bill_id                                                                    billid,
               pa.stationcode                                                                  stationcode,
               ROUND(ifnull(p.totalusedreadings, p.curusedreadings) *
                     ((IFNULL(mrba.money, 0) + IFNULL(mrba.taxmoney, 0)) / p.accountmoney), 2) power,
               p.startdate,
               p.enddate,
               datediff(p.enddate, p.startdate) + 1                                            diff,
               pa.projectname,
               pa.station_name                                                                 stationname,
               pa.stationaddress,
               round(mrba.account_money / (datediff(p.enddate, p.startdate) + 1), 2)
                                                                                               accountmoney
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and pa.stationcode = #{stationcode}
          and DATE_ADD(p.enddate, INTERVAL 1 DAY) = DATE_ADD(#{now}, INTERVAL 0 DAY)
          and p.del_flag = 0
        limit 1
    </select>

    <select id="getStationProtocol"
            resultType="com.sccl.modules.business.stationaudit.pstationprotocolexpired.StationProtocolExpiredRefereeContent">
        select p.pcid                                                                pcid,
               pa.id                                                                 ammeterid,
               (case when pa.category = 1 then pa.ammetername else protocolname end) ammetername,
               pa.stationcode                                                        stationcode,
               pa.projectname,
               pa.station_name                                                       stationname,
               pa.stationaddress,
               pa.protocolsigneddate,
               pa.protocolterminatedate
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id

        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and pa.directsupplyflag = 2
          and p.pcid = #{pcid}
    </select>
    <select id="getStationChangeSameMeter"
            resultType="com.sccl.modules.business.stationaudit.pstationchangesamemeter.StationChangeSameMeterRefereeContent">
        select p.pcid          pcid,
               pa.id           ammeterid,
               mrba.bill_id    billid,
               pa.stationcode  stationcode,
               pa.projectname,
               pa.station_name stationname,
               pa.stationaddress
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id
        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and p.pcid = #{pcid}
    </select>
    <select id="getMeterChangeSameCode"
            resultType="com.sccl.modules.business.stationaudit.pstationmeterchangesamecode.StationMeterChangeSameCodeRefereeContent">
        select p.pcid          pcid,
               pa.id           ammeterid,
               mrba.bill_id    billid,
               pa.stationcode  stationcode,
               pa.projectname,
               pa.station_name stationname,
               pa.stationaddress
        from mss_r_billitem_account mrba
                 left join power_account p on mrba.account_id = p.pcid
                 left join power_ammeterorprotocol pa on p.ammeterid = pa.id
        where pa.electrotype in (1411, 1412, 1421, 1422, 1431, 1432)
          and p.pcid = #{pcid}
    </select>

    <select id="getStationStop"
            resultType="com.sccl.modules.business.stationaudit.pstationstop.StaionStopRefreeContent">
        select p.pcid,
               (case when pa.category = 1 then pa.ammetername else pa.protocolname end) ammetername,
               pa.stationaddresscode                                                    stationCode,
               pa.stationaddress,
               pa.projectname,
               pa.station_name                                                          stationname,
               p.startdate,
               p
                   .enddate,
               ptit
                   .servestartdate,
               ptit
                   .serveenddate,
               case
                   when p.startdate = null then '台账起始时间为空'
                   when p.enddate = null then '台账结束时间为空'
                   when ptit.serveenddate = null then '服务起始时间为空'
                   when ptit.serveenddate = null then '服务结束时间为空'
                   when p.startdate >= ptit.servestartdate and p.enddate<![CDATA[ <=]]> ptit.serveenddate then '起租单正常'
                   when p.enddate >= ptit.servestartdate then '起租单失效'
                   end                                                                  exmsg
        from power_tower_info_two ptit,
             power_account p,
             power_ammeterorprotocol pa
        where p.ammeterid = pa.id
          and pa.stationaddresscode = ptit.stationaddr_code
          and stationaddr_code in
              (select aa.stationaddr_code
               from (select stationaddr_code, count(distinct orderno) anum
                     from power_tower_info_all
                     group by stationaddr_code) aa
                        left join
                    (select stationaddr_code, count(distinct orderno) bnum
                     from power_tower_info_two
                     where status = 1
                     group by stationaddr_code) bb on aa.stationaddr_code = bb.stationaddr_code
               where aa.anum <![CDATA[<]]> bb.bnum)
          and p.del_flag = 0
          and pa.del_flag = 0
          and ptit.status = 1
          and pa.electrotype in (1411, 1412)
          and p.startdate >= ptit.servestartdate
          and p.enddate<![CDATA[ <=]]> ptit.serveenddate
          and pcid = #{pcid}
        order by ptit.inputdate desc
        limit 1;
    </select>
    <select id="getStationStop2"
            resultType="com.sccl.modules.business.stationaudit.pstationstop.StaionStopRefreeContent">
        select p.pcid, pa.stationaddresscode, pa.projectname, pa.stationaddress, pa.projectname, p.startdate, p.enddate
        from power_account p
                 left join power_ammeterorprotocol pa
                           on p.ammeterid = pa.id
        where p.pcid = #{pcid}
          and p.del_flag = 0
          and pa.del_flag = 0;
    </select>
    <select id="selectQuery" resultMap="MssAccountbillResult">
        select ID
        from mss_accountbill
        where
          YEAR=substring_index(#{time},'-','1')
          and BIZ_ENTRY_CODE=substring_index(#{time},'-','-1')
          and STATUS = 7
          and PICKING_MODE in (1, 7, 9)
    </select>
    <select id="getCompareQuota"
            resultType="com.sccl.modules.business.stationaudit.pcomparequtoa.quotaCompareContent">
        select t.resstationcode,
               t.startdate,
               t.enddate,
               round(t.avg, 2)                                                        avgForBill,
               round(avg((psqs.station4gquantity + psqs.station5gquantity) * 1.6), 2) avgForGateway
        from (select psi.resstationcode,
                     avg(p.totalusedreadings / (datediff(p.enddate, p.startdate) + 1)) avg,
                     p.startdate,
                     p.enddate
              from mss_r_billitem_account r
                       left join power_account p on r.account_id = p.pcid
                       left join power_ammeterorprotocol pa on p.ammeterid = pa.id
                       left join power_station_info psi on pa.stationcode = psi.id
              where r.account_id = #{pcid}
                and pa.electrotype in (1411, 1412)
              group by psi.resstationcode) t
                 left join power_station_qua_sta psqs on psqs.resstationcode = t.resstationcode
            and psqs.currentdate between t.startdate and t.enddate
        group by t.resstationcode
    </select>
    <select id="getElecType" resultType="java.lang.String">
        select left(pa.electrotype, 3)
        from power_account p,
             power_ammeterorprotocol pa
        where p.ammeterid = pa.id
          and pa.status = 1
          and p.pcid = #{pcid}
    </select>
    <select id="getAvgPowerToolLow"
            resultType="com.sccl.modules.business.stationaudit.pavgpowertoolow.AvgPowerTooLowContent">
        select case when pam.category = 1 then pam.ammetername else pam.protocolname end ammetername,
               pa.startdate,
               pa.enddate,
               pa.totalusedreadings / (datediff(pa.enddate, pa.startdate) + 1)           avgpower
        from power_account pa
                 inner join mss_r_billitem_account r on pa.pcid = r.account_id and pa.status = 3 and pa.pcid = #{pcid}
                 inner join power_ammeterorprotocol pam on pam.id = pa.ammeterid
        where pa.totalusedreadings / (datediff(pa.enddate, pa.startdate) + 1) &lt; 0.5
          and (pa.totalusedreadings / (datediff(pa.enddate, pa.startdate) + 1)) &gt;= 0
    </select>
    <select id="selectSendFalg1" resultType="java.lang.Boolean">
        select count(*) = 0
        from meter_info_db
        where billid = #{billid}
          and sync_result = '成功'
    </select>
    <select id="selectSendFalg2" resultType="java.lang.Boolean">
        select count(*) = 0
        from writeoffinfodb
        where other_system_main_id = #{billid}
          and sync_result = '成功'
    </select>

    <sql id="countrys">
        <if test="countrys != null and countrys.size != 0">
            and ORGID in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="countrys" item="item" index="index" separator=",">
                    #{item.id}
                </foreach>
            </trim>
        </if>
        <if test="country != null">
            and ORGID =#{country}
        </if>
    </sql>
    <sql id="months">
        <if test="months != null and months.size != 0">
            and BIZ_ENTRY_CODE in
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="months" item="item" index="index" separator=",">
                    #{item}
                </foreach>
            </trim>
        </if>
    </sql>

    <select id="selectNonElectricListByAuto" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        <include refid="selectVo"/>
        <where>
            <include refid="auto-condition"/>
            <include refid="countrys"/>
            <if test="energytype != null">
                and energytype = #{energytype}
            </if>
        </where>
        order by CREATE_DATE desc
    </select>
    <select id="selectForSameCity"
            resultType="com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill">
        select FILL_IN_COST_CENTER_ID fillInCostCenterId,
               FILL_IN_COST_CENTER_NAME fillInCostCenterName
        from mss_accountbill
        where ORGID = (select o.id
                       from rmp.sys_user u,
                            rmp.sys_r_user_organizations r,
                            rmp.sys_organizations o
                       where u.id=r.user_id
                         and r.department_no=o.id
          and u.del_flag=0
          and u.login_id=#{fillInAccount} limit 1)
        order by ID desc limit 1
    </select>
    <select id="verdictJZ" resultType="java.lang.Boolean">
        select count(*)>0
        from power_account pa,
             power_ammeterorprotocol p
        where pa.ammeterid = p.id
          and pa.pcid in (select power_accountbillitempre.pcid
                          from power_accountbillitempre
                          where parid = #{parid})
          and left(p.electrotype, 2) = '14' and p.ammeteruse = 3
    </select>
    <select id="getAmmeUseForPcid" resultType="java.lang.String">
        select p.ammeteruse
        from power_account pa,
             power_ammeterorprotocol p
        where pa.ammeterid = p.id
          and pa.pcid =#{pcid}
    </select>
    <select id="getAccountListByBilId"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.TowerAccountVo">
        SELECT
            a.toweraccountid,
            c.towerid,
            b.company,
            b.country,
            b.stationaddresscode,
            b.supplybureauammetercode,
            a.startdate,
            a.enddate,
            a.prevtotalreadings,
            a.curtotalreadings,
            a.multtimes,
            a.totalusedreadings,
            a.accountmoney,
            a.percent,
            a.taxamount,
            a.accountmoney-taxamount as price,
            c.city as towerCity,
            c.district as towerDistrict,
            c.towersitecode,
            c.payaccountnum,
            c.usestarttime,
            c.useendtime,
            c.usestartdegree,
            c.useenddegree,
            c.magnification,
            c.energyused,
            c.actualpay,
            c.apportionmentratio,
            c.telecomtax,
            c.telecomprice,
            (select writeoff_instance_code from mss_accountbill where ID = #{billId})  paymentOrderNumber
        FROM
            `power_account` a
                LEFT JOIN power_ammeterorprotocol b on a.ammeterid = b.id
                left join toweraccount c on a.toweraccountid = c.id
        WHERE a.pcid in (
            SELECT account_id FROM  mss_r_billitem_account  WHERE bill_id = #{billId}
        )
        and b.electrotype in (1411,1412)
    </select>

    <select id="getMssAccountbillStatusById" resultType="java.lang.String">
        select
            ISCANUPDATE
        from mss_accountbill
        where ID = #{billId}
    </select>
    <update id="updateMssAccountbillStatusById">
        update mss_accountbill
        set ISCANUPDATE = #{status}
        where ID = #{billId}
    </update>
    <select id="selectListByBudgetSetName" parameterType="MssAccountbill" resultMap="MssAccountbillResult">
        SELECT DISTINCT
            ma.ID,
            ma.COMPANY_CODE,
            ma.BILLTYPE,
            pap.billtype preBillType,
            ma.BUDGETSETNAME,
            ma.`STATUS`
        FROM mss_accountbill ma
        LEFT JOIN power_accountbillpre pap on ma.ID = pap.pabid
        LEFT JOIN mss_accountbillitem msi on ma.ID = msi.WRITEOFF_INSTANCE_ID
        <where>
            ma.`STATUS`='7'
            AND msi.BUDGET_TYPE = 1
            <if test="budgetsetname != null and budgetsetname != ''">
                AND ma.BUDGETSETNAME = #{budgetsetname}
            </if>
        </where>
        ORDER BY COMPANY_CODE
    </select>

    <select id="statisticalAnalysisOfAccounting" resultType="com.sccl.modules.mssaccount.dataanalysis.vo.ElectricityBillVO">
        SELECT
        CONCAT(bill.`YEAR`, bill.BIZ_ENTRY_CODE) AS `index`
        ,bill.`YEAR` AS year
        ,bill.BIZ_ENTRY_CODE AS month
        ,ROUND(SUM(account.AMOUNT) / 1000, 2) AS kwh
        ,ROUND(AVG(account.PRICE), 2) AS unitPrice
        ,ROUND(SUM(account.SUM), 2) AS electricityCost
        <if test="orgId != null and orgId != ''">
            ,org.id AS orgId
            ,org.org_name AS orgName
        </if>
        FROM mss_accountbill bill
        LEFT JOIN mss_accountbillitem account ON bill.ID = account.WRITEOFF_INSTANCE_ID
        <if test="orgId != null and orgId != ''">
            LEFT JOIN rmp.sys_organizations org ON org.id = bill.COMPANY_CODE
        </if>
        WHERE bill.`YEAR` = #{year}
        AND bill.STATUS = 7
        AND BUDGET_TYPE = 1
        <if test="orgId != null and orgId != ''">
            AND org.id = #{orgId}
        </if>
        GROUP BY bill.`YEAR`, bill.BIZ_ENTRY_CODE
    </select>
    <select id="getAccountByMssBill" resultType="java.util.HashMap">
        SELECT
            ma.BUDGETSETNAME,
            pa.pcid,
            pa.accountno,
            pa.ammeterid,
            STR_TO_DATE( pa.startdate, '%Y%m%d' ) startdate,
            STR_TO_DATE( pa.enddate, '%Y%m%d' ) enddate,
            pa.curusedreadings total_data,
            1 + DATEDIFF(
                    STR_TO_DATE( pa.enddate, '%Y%m%d' ),
                    STR_TO_DATE( pa.startdate, '%Y%m%d' )) AS diff_days,
            ROUND( pa.curusedreadings / ( 1 + DATEDIFF( STR_TO_DATE( pa.enddate, '%Y%m%d' ), STR_TO_DATE( pa.startdate, '%Y%m%d' ))), 2 ) AS avg_data,
            pam.electrotype,
            (
                CASE
                    WHEN pam.electrotype IN ( 1411, 1412, 1421, 1422, 1431, 1432 ) THEN
                        psi.stationcodeintid
                    ELSE psi.resstationcode
                END
                ) stationCode,
            pa.unitpirce,
            pam.price,
            ( CASE WHEN pam.category = 1 THEN pam.ammetername ELSE pam.protocolname END ) ammeterCode
        FROM
            mss_accountbill ma
                LEFT JOIN mss_r_billitem_account mrba ON mrba.bill_id = ma.ID
                LEFT JOIN (
                SELECT
                    pcid,
                    accountno,
                    ammeterid,
                    curusedreadings,
                    startdate,
                    enddate,
                    unitpirce
                FROM
                    power_account UNION ALL
                SELECT
                    pcid,
                    accountno,
                    ammeterid,
                    curusedreadings,
                    startdate,
                    enddate,
                    unitpirce
                FROM
                    power_account_es
            ) pa ON pa.pcid = mrba.account_id
                LEFT JOIN power_ammeterorprotocol pam ON pa.ammeterid = pam.id
                LEFT JOIN power_station_info psi ON pam.stationcode = psi.id
        WHERE
            ma.ID = #{billId}
          AND pa.pcid IS NOT NULL
    </select>

    <select id="getBaseInfoByMssBill" resultType="java.util.HashMap">
        SELECT
            midb.province_code,
            midb.city_code,
            midb.city_name,
            midb.county_code,
            midb.county_name,
            midb.station_code,
            midb.station_name,
            midb.energy_meter_code,
            midb.energy_meter_name,
            midb.station_location,
            midb.`status`,
            midb.usagecopy,
            midb.`type`,
            midb.station_status,
            midb.station_type,
            midb.large_industrial_electricity_flag,
            midb.energy_supply_way,
            midb.power_grid_energy_meter_code,
            pac.startdate,
            pac.enddate,
            pac.total_data,
            pac.avg_data,
            pac.price,
            pac.unitpirce,
            pac.diff_days,
            pac.BUDGETSETNAME,
            pac.ammeterid
        FROM
            meter_info_db_bases midb
                LEFT JOIN (
                SELECT DISTINCT
                    ma.BUDGETSETNAME,
                    STR_TO_DATE( pa.startdate, '%Y%m%d' ) startdate,
                    STR_TO_DATE( pa.enddate, '%Y%m%d' ) enddate,
                    pa.curusedreadings total_data,
                    1 + DATEDIFF(
                            STR_TO_DATE( pa.enddate, '%Y%m%d' ),
                            STR_TO_DATE( pa.startdate, '%Y%m%d' )) AS diff_days,
                    ROUND( pa.curusedreadings / ( 1 + DATEDIFF( STR_TO_DATE( pa.enddate, '%Y%m%d' ), STR_TO_DATE( pa.startdate, '%Y%m%d' ))), 2 ) AS avg_data,
                    (
                        CASE

                            WHEN pam.electrotype IN ( 1411, 1412, 1421, 1422, 1431, 1432 ) THEN
                                psi.stationcodeintid ELSE psi.resstationcode
                            END
                        ) stationCode,
                    pa.unitpirce,
                    pam.price,
                    pa.ammeterid,
                    ( CASE WHEN pam.category = 1 THEN pam.ammetername ELSE pam.protocolname END ) ammeterCode
                FROM
                    mss_accountbill ma
                        LEFT JOIN mss_r_billitem_account mrba ON mrba.bill_id = ma.ID
                        LEFT JOIN (
                        SELECT
                            pcid,
                            accountno,
                            ammeterid,
                            curusedreadings,
                            startdate,
                            enddate,
                            unitpirce
                        FROM
                            power_account
                        WHERE
                            pcid IN (
                                SELECT DISTINCT
                                    account_id
                                FROM
                                    mss_r_billitem_account
                                WHERE
                                    bill_id = #{billId}
                            )
                          AND effective = 1
                        UNION ALL
                        SELECT
                            pcid,
                            accountno,
                            ammeterid,
                            curusedreadings,
                            startdate,
                            enddate,
                            unitpirce
                        FROM
                            power_account_es
                        WHERE
                            pcid IN (
                                SELECT DISTINCT
                                    account_id
                                FROM
                                    mss_r_billitem_account
                                WHERE
                                    bill_id = #{billId}
                            )
                          AND effective = 1
                    ) pa ON pa.pcid = mrba.account_id
                        LEFT JOIN power_ammeterorprotocol pam ON pa.ammeterid = pam.id
                        LEFT JOIN power_station_info psi ON pam.stationcode = psi.id
                WHERE
                    ma.ID = #{billId}
                  AND pa.pcid IS NOT NULL
            ) pac ON pac.stationCode = midb.station_code
                AND pac.ammeterCode = midb.energy_meter_code
        WHERE
            midb.billid = #{billId}
          AND midb.create_time = ( SELECT MAX(create_time) FROM meter_info_db_bases WHERE billid = #{billId} AND sync_result = '成功' )
          AND midb.sync_result = '成功'
    </select>
    <select id="getAccountPrice" resultType="java.math.BigDecimal">
        SELECT
            ROUND( IFNULL( AVG( t.unitpirce ), 0 ), 2 )
        FROM
            (
                ( SELECT pcid, ammeterid, unitpirce, enddate FROM power_account WHERE ammeterid = #{ammeterId} AND effective = 1 ORDER BY enddate DESC LIMIT 3 ) UNION ALL
                ( SELECT pcid, ammeterid, unitpirce, enddate FROM power_account_es WHERE ammeterid = #{ammeterId} AND effective = 1 ORDER BY enddate DESC LIMIT 3 )
            ) t
    </select>
    <select id="getStationListByBillId" resultType="java.util.HashMap">
        SELECT DISTINCT
            ma.BUDGETSETNAME,
            ( SELECT max( v_org_code ) FROM power_city_organization WHERE org_code = psi.company) cityCode,
            ( SELECT max( org_name ) FROM rmp.sys_organizations WHERE id = psi.company ) cityName,
            ma.FILL_IN_COST_CENTER_ID countyCode,
            ma.FILL_IN_COST_CENTER_NAME countyName,
            ( CASE WHEN pam.category = 1 THEN pam.ammetername ELSE pam.protocolname END ) energyMeterCode,
            pam.projectname energyMeterName,
            pa.ammeterid,
            CASE
                WHEN CASE
                         WHEN LENGTH(pam.electrotype) = 4 THEN pam.electrotype
                         WHEN LENGTH(pam.electrotype) = 3 THEN concat(pam.electrotype, '0')
                         WHEN LENGTH(pam.electrotype) = 2 THEN concat(pam.electrotype, '00')
                         WHEN LENGTH(pam.electrotype) = 1 THEN concat(pam.electrotype, '000')
                         END LIKE '14%' THEN psi.stationcodeintid
                ELSE psi.resstationcode
            END stationCode,
            psi.resstationname stationName,
            1 stationStatus,
            1 status,
            IFNULL( psi.address, psi.stationname ) stationLocation,
            CASE
                WHEN LENGTH( pam.electrotype )= 4 THEN
                    pam.electrotype
                WHEN LENGTH( pam.electrotype )= 3 THEN
                    concat( pam.electrotype, '0' )
                WHEN LENGTH( pam.electrotype )= 2 THEN
                    concat( pam.electrotype, '00' )
                WHEN LENGTH( pam.electrotype )= 1 THEN
                    concat( pam.electrotype, '000' )
                END stationType,
            CASE
                WHEN psi.isbigfactories = 1 THEN
                    1 ELSE 0
                END largeIndustrialElectricityFlag,
            pam.directsupplyflag energySupplyWay,
            ifnull(pam.supplybureauammetercode,' ') AS powerGridEnergyMeterCode,
            ( CASE pam.isentityammeter WHEN 0 THEN 2 ELSE 1 END ) typeStationCode,
            CASE
                WHEN pam.directsupplyflag = 1 THEN
                     round(ifnull(pa.unitpirce,0),2)   ELSE round(ifnull(pam.price,0),2)
                END contractPrice,
            ( SELECT type_code FROM power_category_type WHERE type_category = 'ammeterUse' AND type_code = pam.ammeteruse ) `usageCopy`
        FROM
            mss_accountbill ma
                LEFT JOIN mss_r_billitem_account mrba ON mrba.bill_id = ma.ID
                LEFT JOIN (
                SELECT
                    pcid,
                    accountno,
                    ammeterid,
                    curusedreadings,
                    startdate,
                    enddate,
                    unitpirce
                FROM
                    power_account
                WHERE
                        pcid IN ( SELECT DISTINCT account_id FROM mss_r_billitem_account WHERE bill_id = #{billId} )
                  AND effective = 1 UNION ALL
                SELECT
                    pcid,
                    accountno,
                    ammeterid,
                    curusedreadings,
                    startdate,
                    enddate,
                    unitpirce
                FROM
                    power_account_es
                WHERE
                        pcid IN ( SELECT DISTINCT account_id FROM mss_r_billitem_account WHERE bill_id = #{billId} )
                  AND effective = 1
            ) pa ON pa.pcid = mrba.account_id
                LEFT JOIN power_ammeterorprotocol pam ON pa.ammeterid = pam.id
                LEFT JOIN power_station_info psi ON pam.stationcode = psi.id
                LEFT JOIN power_city_organization pco ON psi.company = pco.org_code
        WHERE
            ma.ID = #{billId}
          AND ma.BILLTYPE != 4
          AND pa.pcid IS NOT NULL
          AND pam.electrotype  IN (111,112,113,121,131,132,133,1411,1412,1421,1422,1431,1432)

    </select>

    <select id="getStationMssBuss" resultType="com.sccl.modules.mssaccount.mssinterface.vo.StationMssBussElectVo">
        select t.citycode,t.cityname,t.countycode,t.countyname,t.stationcode,t.stationName,t.stationtype,
            t.startdate,t.enddate,t.totalusedreadings,t.mssdata,t.bussData,
            (case when t.bussData = 0 then 100 else abs(round((t.mssdata-t.bussData)*100/t.bussData,2)) end) rate
        from (
        select a.citycode,a.cityname,a.countycode,a.countyname,a.stationcode,a.stationName,a.stationtype,
            a.startdate,a.enddate,
            round(a.totalusedreadings) totalusedreadings,
            ifnull(round(a.mssdata,4),0) mssdata,
            ifnull(round(sum(c.energyData)/(DATEDIFF(a.enddate,a.startdate)+1),4),0) bussData
        from (
            select a.citycode,a.cityname,a.countycode,a.countyname,a.stationcode,a.stationName,a.stationtype,
                MIN(a.startdate) startdate,
                MAX(a.enddate) enddate,
                sum(a.totalusedreadings) totalusedreadings,
                round(sum(a.totalusedreadings)/(datediff(MAX(a.enddate),MIN(a.startdate))+1),4) mssdata
            from(
                select (select max( v_org_code ) from power_city_organization where org_code = psi.company) citycode,
                    (select max( org_name ) from rmp.sys_organizations where id = psi.company ) cityname,
                    ma.fill_in_cost_center_id countycode,
                    ma.fill_in_cost_center_name countyname,
                    (case when rpad(pam.electrotype, 4, '0') like '14%' then psi.stationcodeintid
                          else psi.resstationcode end) stationcode,
                    psi.resstationname stationName,
                    rpad(pam.electrotype, 4, '0') stationtype,
                    pa.startdate,
                    pa.enddate,
                    pa.totalusedreadings
                from mss_accountbill ma
                left join mss_r_billitem_account mrba on mrba.bill_id = ma.id
                left join (
                    select pcid,
                        ammeterid,
                        totalusedreadings,
                        startdate,
                        enddate
                    from power_account
                    where pcid in (select distinct account_id from mss_r_billitem_account where bill_id = #{billId} )
                    and effective = 1

                    union all

                    select pcid,
                        ammeterid,
                        totalusedreadings,
                        startdate,
                        enddate
                    from power_account_es
                    where pcid in ( select distinct account_id from mss_r_billitem_account where bill_id = #{billId} )
                    and effective = 1
                ) pa on pa.pcid = mrba.account_id
                left join power_ammeterorprotocol pam on pa.ammeterid = pam.id
                left join power_station_info psi on pam.stationcode = psi.id
                left join power_city_organization pco on psi.company = pco.org_code
                where ma.id = #{billId}
                and ma.billtype != 4
                and pa.pcid is not null
                and pam.electrotype  in (111,112,113,121,131,132,133,1411,1412,1421,1422,1431,1432)
            ) a group by a.citycode,a.countycode,a.stationcode
        ) a
        left join fail_sync_collectmeter c on c.del_Flag = 0 and c.syncFlag = 1
        and c.cityCode = a.citycode and c.countyCode = a.countycode
        and c.stationCode = a.stationcode and c.collectTime >= a.startdate and c.collectTime &lt;= a.enddate
        and not exists(select b.id from fail_sync_collectmeter b
                       where b.del_Flag = 0
                       and b.syncFlag = 1
                       and b.cityCode = c.cityCode
                       and b.countyCode = c.countyCode
                       and b.stationCode = c.stationCode
                       and b.collectTime = c.collectTime
                       and b.id > c.id)
        group by a.citycode,a.countycode,a.stationcode
        ) t
    </select>

    <select id="checkTower" resultType="com.sccl.modules.mssaccount.mssinterface.vo.CheckTowerVo">
        select rpad(pam.electrotype, 4, '0') stationtype,
            '转供电' directsupplyflag,
            (case when pam.category = 1 then pam.ammetername else pam.protocolname end ) ammeterName,
            pam.projectname,
            psi.resstationcode stationcode,
            ifnull(psi.resstationname,psi.stationname) stationName,
            psi.stationcodeintid stationcode5gr
        from mss_accountbill ma
        left join mss_r_billitem_account mrba on mrba.bill_id = ma.id
        left join (
            select pcid,
                ammeterid
            from power_account
            where pcid in (select distinct account_id from mss_r_billitem_account where bill_id = #{billId} )
            and effective = 1

            union all

            select pcid,
                ammeterid
            from power_account_es
            where pcid in ( select distinct account_id from mss_r_billitem_account where bill_id = #{billId} )
            and effective = 1
        ) pa on pa.pcid = mrba.account_id
        left join power_ammeterorprotocol pam on pa.ammeterid = pam.id
        left join power_station_info psi on pam.stationcode = psi.id
        left join power_city_organization pco on psi.company = pco.org_code
        where ma.id = #{billId}
        -- and ma.billtype != 4
        and pa.pcid is not null
        and pam.electrotype  in (1411,1412)
        and pam.directsupplyflag = 2
        and not exists (select 1 from tower_check_list a0
				        where a0.stationCode = psi.resstationcode
						and a0.ammetername = (case when pam.category = 1 then pam.ammetername else pam.protocolname end))
    </select>
</mapper>
