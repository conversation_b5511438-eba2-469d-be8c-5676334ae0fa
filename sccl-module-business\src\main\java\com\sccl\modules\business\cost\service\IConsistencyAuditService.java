package com.sccl.modules.business.cost.service;

import com.sccl.modules.business.cost.vo.*;

import java.util.List;

/**
 * 电量业财一致率稽核 服务层
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface IConsistencyAuditService
{
    /**
     * 电量业财一致率稽核
     * @param auditTime 稽核时间【yyyy-MM】
     * @param flag      统计标识 1 机楼 2基站
     * @param isSaveRecord 是否额外保存执行记录
     * @param inputDate 生成数据日期【yyyyMMdd】
     */
    void audit(String auditTime, String flag, boolean isSaveRecord, String inputDate);

    /**
     * 机楼电量业财一致率
     * @param searchVo
     * @return
     */
    ConsistencyAuditVo consistency(ConsistencyAuditSearchVo searchVo);

    /**
     * 机楼电量业财一致率 一览
     * @param searchVo
     * @return
     */
    List<ConsistencyAuditResultVo> list(ConsistencyAuditSearchVo searchVo);

    /**
     * 详情-机楼
     * @return
     */
    ConsistencyAuditXqVo details(ConsistencyAuditXqSearchVo searchVo);

    /**
     * 详情-详细信息列表
     * @param searchVo
     * @return
     */
    List<ConsistencyAuditXqResultVo> detailsList(ConsistencyAuditXqSearchVo searchVo);
}
