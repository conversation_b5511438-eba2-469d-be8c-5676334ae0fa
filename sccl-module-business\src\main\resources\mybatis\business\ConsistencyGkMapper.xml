<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.ConsistencyGkMapper">
    <select id="list" resultType="com.sccl.modules.business.cost.vo.ConsistencyYcResultVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyYcSearchVo">
        select a.id,
            a.station_id stationCode,
            a.stationName station,
            (case when pec.id is null then ec.type_name else CONCAT(pec.type_name,'/',ec.type_name) end) stationType,
            a.bzdbsl,
           (SELECT a0.org_name from rmp.sys_organizations a0 where a0.org_type = '1' and a0.id = a.company) companyName,
           (SELECT a0.org_name from rmp.sys_organizations a0 where a0.org_type = '2' and a0.id = a.country) countryName,
            DATE_FORMAT(STR_TO_DATE(CONCAT(a.audit_time,'-01'),'%Y-%m-%d'),'%Y.%m') auditTime,
            a.startdate,
            a.enddate,
            a.ywrjdl ywrjdl,
            a.cwrjdl cwrjdl,
            a.rateD rate
        from power_consistency_anomaly a
        left join power_electric_classification ec on ec.id = a.stationType
        left join power_electric_classification pec on pec.id = ec.parent_id
        where a.del_Flag = '0'
        and a.`status` = 9
        and a.data_type = #{flag}
        and a.audit_time = #{auditTime}
        <choose>
            <when test="flag == '2'.toString()">
                and (a.rateD &lt; 80 or a.rateD > 120)
            </when>
            <otherwise>
                and (a.rateD &lt; 90 or a.rateD > 110)
            </otherwise>
        </choose>
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="companys != null">
            and  a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
        <if test="stationType != null and stationType != ''">
            and a.stationType = #{stationType}
        </if>
        <if test="stationCode != null and stationCode != ''">
            and a.station_id like concat('%', #{stationCode}, '%')
        </if>
        <if test="station != null and station != ''">
           and a.stationName like concat('%', #{station}, '%')
        </if>
        order by a.rateD desc
    </select>

    <select id="getDispatch" resultType="com.sccl.modules.business.cost.vo.ConsistencyPdVo" >
        select a.id,
            a.station_id stationCode,
            a.stationName station,
            (case when pec.id is null then ec.type_name else CONCAT(pec.type_name,'/',ec.type_name) end) stationType,
            a.bzdbsl,
            a.company company,
            (SELECT a0.org_name from rmp.sys_organizations a0 where a0.org_type = '1' and a0.id = a.company) companyName,
            a.country country,
            (SELECT a0.org_name from rmp.sys_organizations a0 where a0.org_type = '2' and a0.id = a.country) countryName,
            a.ywrjdl ywrjdl,
            a.cwrjdl cwrjdl,
            a.rate rate,
            DATE_FORMAT(STR_TO_DATE(CONCAT(a.audit_time,'-01'),'%Y-%m-%d'),'%Y年%c月') auditTimeStr,
            a.audit_time auditTime,
            a.startdate,
            a.enddate
        from power_consistency_anomaly a
        left join power_electric_classification ec on ec.id = a.stationType
        left join power_electric_classification pec on pec.id = ec.parent_id
        where a.del_Flag = '0'
        and a.id = #{id}
    </select>

    <select id="dispatchList" resultType="com.sccl.modules.business.cost.vo.ConsistencyPdResultVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyPdSearchVo">
        select a.id,
             a.station_id stationCode,
             a.stationName station,
             (case when pec.id is null then ec.type_name else CONCAT(pec.type_name,'/',ec.type_name) end) stationType,
			 (select d0.org_name from rmp.sys_organizations d0 where d0.id = a.company and d0.org_type = '1' and d0.del_flag = '0') companyName,
			 (select d0.org_name from rmp.sys_organizations d0 where d0.id = a.country and d0.org_type = '2' and d0.del_flag = '0') countryName,
			 a.bzdbsl,
			 DATE_FORMAT(STR_TO_DATE(CONCAT(a.audit_time,'-01'),'%Y-%m-%d'),'%Y.%m') auditTime,
             a.startdate,
             a.enddate,
			 a.ywrjdl,
			 a.cwrjdl,
			 a.rateD rate,
			 a.`status`,
			 (SELECT d0.type_name from power_category_type d0 where d0.type_category = 'ycpd_audit_type' and d0.type_code = a.`status`) statusName,
			 DATE_FORMAT(a.dispatch_time,'%Y.%c.%e %H:%i:%s') dispatchTime,
			 a.dispatch_by dispatchBy,
             ifnull(DATE_FORMAT(d.complete_time,'%Y.%c.%e %H:%i:%s'),ifnull(DATE_FORMAT(a.do_time,'%Y.%c.%e %H:%i:%s'),'——')) doTime,
            ifnull(c.`name`,ifnull(d.owner_name,ifnull(a.do_by,'——'))) doBy,
            a.proCessId
        from power_consistency_anomaly a
        left join (select a0.proc_inst_id, b0.`name` from wf_task a0
                   left join rmp.sys_user b0 on b0.login_id = a0.assignee_id) c on c.proc_inst_id = a.proCessId
        left join (select complete_time,owner_name,proc_inst_id FROM wf_task_his
                   where id in(select SUBSTRING_INDEX(GROUP_CONCAT(id order by create_time desc),',',1)
                                  from wf_task_his group by proc_inst_id)
        ) d on d.proc_inst_id = a.proCessId
        left join power_electric_classification ec on ec.id = a.stationType
        left join power_electric_classification pec on pec.id = ec.parent_id
        where a.del_flag = '0'
        and a.`status` in (0,1,2,3,4)
        <if test="flag != null and flag != ''">
            and a.data_type = #{flag}
        </if>
        <if test="auditTime != null and auditTime != ''">
            and a.audit_time = #{auditTime}
        </if>
        <if test="stationType != null and stationType != ''">
            and a.stationType = #{stationType}
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
        <if test="companys != null">
            and a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="station != null and station != ''">
            and a.stationName like concat('%', #{station}, '%')
        </if>
        <if test="stationCode != null and stationCode != ''">
            and a.station_id like concat('%', #{stationCode}, '%')
        </if>
        order by a.dispatch_time desc
    </select>

    <select id="getDispatchXq" resultType="com.sccl.modules.business.cost.vo.ConsistencyPdXqVo">
        select a.id,
             a.station_id stationCode,
             a.stationName station,
			 (case when pec.id is null then ec.type_name else CONCAT(pec.type_name,'/',ec.type_name) end) stationType,
			 a.bzdbsl,
			 (select d0.org_name from rmp.sys_organizations d0 where d0.id = a.company and d0.org_type = '1' and d0.del_flag = '0') companyName,
			 (select d0.org_name from rmp.sys_organizations d0 where d0.id = a.country and d0.org_type = '2' and d0.del_flag = '0') countryName,
			 a.ywrjdl,
			 a.cwrjdl,
			 a.rate,
			 DATE_FORMAT(STR_TO_DATE(CONCAT(a.audit_time,'-01'),'%Y-%m-%d'),'%Y年%c月') auditTimeStr,
			 a.startdate,
             a.enddate,
			 a.dispatch_desc dispatchDesc,
			 a.dispatch_fj dispatchFj,
			 a.do_desc doDesc,
			 a.do_fj doFj,
			 a.`status`
        from power_consistency_anomaly a
        left join power_electric_classification ec on ec.id = a.stationType
        left join power_electric_classification pec on pec.id = ec.parent_id
        where a.id = #{id}
    </select>

    <select id="getAttachByFjids" resultType="com.sccl.modules.business.cost.vo.ConsistencyFjVo">
        select id, file_name fileName
        from attachments
        where del_flag = '0'
        and id in
        <foreach item="fjId" collection="fjIds" open="(" separator="," close=")">
            #{fjId}
        </foreach>
    </select>

    <select id="checkYpdByIds" resultType="int">
        select  count(1) from power_consistency_anomaly
        where del_flag = '0'
        and `status` in(1,2,3)
        and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>