package com.sccl.modules.business.stationmss.controller;

import com.sccl.common.lang.StringUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.business.stationmss.service.IStationMssMonthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 局站财务月日均电量统计
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/business/stationMssMonth")
@Slf4j
public class StationMssMonthController extends BaseController {

    @Autowired
    private IStationMssMonthService stationMssMonthService;

    /**
     * 统计局站财务月日均电量表数据 定时任务调用
     * @param tjsj 统计时间【yyyy-MM】
     * @return
     */
    @GetMapping("/audit")
    public AjaxResult audit(@RequestParam(required = false) String tjsj) {
        String msg = "统计局站财务月日均电量表数据完成";
        Integer cnt = 0;
        if (StringUtils.isBlank(tjsj)) {
            //统计当前月份
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate now = LocalDate.now();
            tjsj = dtf.format(now);
            cnt = stationMssMonthService.statisticsAuto(tjsj);
            msg += "，统计月份：" + tjsj;
        } else {
            cnt =stationMssMonthService.statisticsAuto(tjsj);
            msg += "，统计月份：" + tjsj;
        }
        msg += ", 成功记录数：" + cnt;
        return this.success(msg);
    }

    /**
     * 同步局站财务日均电量到采集表 定时任务调用
     * @param collectTime 采集时间【yyyyMMdd】
     * @return
     */
    @GetMapping("/doSyncCollectmeter")
    public AjaxResult doSyncCollectmeter(@RequestParam(required = false) String collectTime) {
        String msg = "同步局站财务日均电量到采集表完成";
        Integer cnt = 0;
        if (StringUtils.isBlank(collectTime)) {
            //统计当前月份
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate last = LocalDate
                    .now().minusDays(2L);
            collectTime = dtf.format(last);
            cnt = stationMssMonthService.doSyncCollectmeter(collectTime);
            msg += "，采集时间：" + collectTime;
        } else {
            cnt = stationMssMonthService.doSyncCollectmeter(collectTime);
            msg += "，采集时间：" + collectTime;
        }
        msg += ", 成功记录数：" + cnt;
        return this.success(msg);
    }
}
