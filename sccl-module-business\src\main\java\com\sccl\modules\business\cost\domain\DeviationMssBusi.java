package com.sccl.modules.business.cost.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业财偏差率表
 */
@Data
@EqualsAndHashCode
@TableName(value = "deviation_mss_busi")
public class DeviationMssBusi implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 稽核时间 yyyy-MM
	 */
	private String auditTime;

	/**
	 * 站址编码
	 */
	private String stationCode;

	/**
	 * 局站名称
	 */
	private String stationName;

	/**
	 * 局站类型
	 */
	private String stationType;

	/**
	 * 稽核年份 yyyy
	 */
	private String year;

	/**
	 * 稽核月份 MM
	 */
	private String month;

	/**
	 * 所属分公司
	 */
	private Long company;

	/**
	 * 所属部门
	 */
	private Long country;

	/**
	 * 市局组织编码
	 */
	protected String cityCode;

	/**
	 * 区县组织编码
	 */
	protected String countyCode;

	/**
	 * 财务电费(万元)
	 */
	private BigDecimal mssMoney;

	/**
	 * 直供电单价
	 */
	private BigDecimal directPrice;

	/**
	 * 直供电电量
	 */
	private BigDecimal directPower;

	/**
	 * 转供电单价
	 */
	private BigDecimal transPrice;

	/**
	 * 转供电电量
	 */
	private BigDecimal transPower;

	/**
	 * 业务电费(万元)
	 */
	private BigDecimal busMoney;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	/**
	 * 删除标志 0/1 正常/删除
	 */
	private String delFlag;

	/**
	 * 新增
	 *
	 * @param userName 当前登录用户
	 */
	public void initInsert(String userName) {
		delFlag = "0";
		createdBy = userName;
		createTime = new Date();
		updateTime = new Date();
	}

	/**
	 * 修改
	 */
	public void initUpdate() {
		updateTime = new Date();
	}
}
