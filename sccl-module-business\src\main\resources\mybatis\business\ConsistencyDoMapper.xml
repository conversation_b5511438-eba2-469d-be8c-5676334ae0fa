<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.ConsistencyDoMapper">

    <sql id="listComm">
        select a.id,
            a.station_id stationCode,
            a.stationName station,
            (case when pec.id is null then ec.type_name else CONCAT(pec.type_name,'/',ec.type_name) end) stationType,
            (select d0.org_name from rmp.sys_organizations d0 where d0.id = a.company and d0.org_type = '1' and d0.del_flag = '0') companyName,
            (select d0.org_name from rmp.sys_organizations d0 where d0.id = a.country and d0.org_type = '2' and d0.del_flag = '0') countryName,
            a.bzdbsl,
            DATE_FORMAT(STR_TO_DATE(CONCAT(a.audit_time,'-01'),'%Y-%m-%d'),'%Y.%m') auditTime,
            a.startdate,
            a.enddate,
            a.ywrjdl,
            a.cwrjdl,
            a.rate,
            a.`status`,
            (SELECT d0.type_name from power_category_type d0 where d0.type_category = 'ycpd_audit_type' and d0.type_code = a.`status`) statusName,
            DATE_FORMAT(a.dispatch_time,'%Y.%c.%e %H:%i:%s') dispatchTime,
            a.dispatch_by dispatchBy,
            ifnull(DATE_FORMAT(a.do_time,'%Y.%c.%e %H:%i:%s'),'——') doTime,
            a.proCessId
        from power_consistency_anomaly a
        left join power_electric_classification ec on ec.id = a.stationType
        left join power_electric_classification pec on pec.id = ec.parent_id
        where a.del_flag = '0'
        and a.`status` in (0,1,2,3,4)
        <if test="flag != null and flag != ''">
            and a.data_type = #{flag}
        </if>
        <if test="auditTime != null and auditTime != ''">
            and a.audit_time = #{auditTime}
        </if>
        <if test="stationType != null and stationType != ''">
            and a.stationType = #{stationType}
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
        <if test="companys != null">
            and a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="station != null and station != ''">
            and a.stationName like concat('%', #{station}, '%')
        </if>
        <if test="stationCode != null and stationCode != ''">
            and a.station_id like concat('%', #{stationCode}, '%')
        </if>
    </sql>
    <select id="list" resultType="com.sccl.modules.business.cost.vo.ConsistencyDoResultVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyDoSearchVo">
        <include refid="listComm" />
    </select>

    <select id="exportList" resultType="com.sccl.modules.business.cost.vo.ConsistencyDoExpVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyDoSearchVo">
        <include refid="listComm" />
    </select>

    <select id="statistics" resultType="com.sccl.modules.business.cost.vo.ConsistencyToDoStatisticsVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyDoSearchVo">
        select ifnull(sum(case a.data_type when '1' then 1 else 0 end),0) cnt,
            ifnull(sum(case a.data_type when '2' then 1 else 0 end),0) cntJz
        from power_consistency_anomaly a
        where a.del_flag = '0'
		and a.`status` in (0,2)
        <if test="auditTime != null and auditTime != ''">
            and a.audit_time = #{auditTime}
        </if>
        <if test="stationType != null and stationType != ''">
            and a.stationType = #{stationType}
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
        <if test="companys != null">
            and a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="station != null and station != ''">
            and a.stationName like concat('%', #{station}, '%')
        </if>
        <if test="stationCode != null and stationCode != ''">
            and a.station_id like concat('%', #{stationCode}, '%')
        </if>
    </select>
</mapper>