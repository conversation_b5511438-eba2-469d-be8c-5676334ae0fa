package com.sccl.modules.business.cost.service;

import com.sccl.common.lang.StringUtils;
import com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly;
import com.sccl.modules.business.cost.domain.PowerConsistencyAnomalyRecord;
import com.sccl.modules.business.cost.mapper.ConsistencyAuditMapper;
import com.sccl.modules.business.cost.mapper.PowerConsistencyAnomalyMapper;
import com.sccl.modules.business.cost.mapper.PowerConsistencyAnomalyRecordMapper;
import com.sccl.modules.business.cost.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * 电量业财一致率稽核
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
@Slf4j
public class ConsistencyAuditServiceImpl implements IConsistencyAuditService {

    @Autowired
    private ConsistencyAuditMapper consistencyAuditMapper;
    @Autowired
    private PowerConsistencyAnomalyMapper powerConsistencyAnomalyMapper;
    @Autowired
    private PowerConsistencyAnomalyRecordMapper consistencyAnomalyRecordMapper;

    /**
     * 电量业财一致率稽核
     * @param auditTime 稽核时间【yyyy-MM】
     * @param flag      统计标识 1 机楼 2基站
     * @param isSaveRecord 是否额外保存执行记录
     * @param inputDate 生成数据日期【yyyyMMdd】
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(String auditTime, String flag, boolean isSaveRecord, String inputDate) {

        //先根据统计时间及统计标识批量删除
        powerConsistencyAnomalyMapper.delByAuditTime(auditTime, flag);

        //开始稽核
        String year = StringUtils.left(auditTime, 4);
        String month = StringUtils.right(auditTime, 2);
        PowerConsistencyAnomaly entity = new PowerConsistencyAnomaly();
        entity.setAuditTime(auditTime);                       //稽核时间
        entity.setYear(year);                                 //稽核年份
        entity.setMonth(month);                               //稽核月份
        List<ConsistencyAuditTmpVo> list;
        if ("1".equals(flag)) {
            list = consistencyAuditMapper.audit(entity);
        } else {
            list = consistencyAuditMapper.auditJz(entity);
        }
        if (list != null && list.size() > 0) {
            List<PowerConsistencyAnomaly> saveData = new ArrayList<>();
            List<PowerConsistencyAnomalyRecord> records = new ArrayList<>();
            for (ConsistencyAuditTmpVo vo : list) {
                if (StringUtils.isBlank(vo.getStationId())) {
                    continue;
                }
                PowerConsistencyAnomaly consistencyAnomaly = new PowerConsistencyAnomaly();
                BeanUtils.copyProperties(vo, consistencyAnomaly);
                consistencyAnomaly.initInsert("1");
                saveData.add(consistencyAnomaly);
                if (saveData.size() == 1000) {
                    powerConsistencyAnomalyMapper.insertList(saveData);
                    saveData.clear();
                }

                //额外保存稽核记录
                if (isSaveRecord) {
                    PowerConsistencyAnomalyRecord record = new PowerConsistencyAnomalyRecord();
                    BeanUtils.copyProperties(vo, record);
                    record.initInsert("1");
                    record.setInputDate(inputDate);
                    records.add(record);
                    if (records.size() == 1000) {
                        consistencyAnomalyRecordMapper.insertList(records);
                        records.clear();
                    }
                }
            }
            if (saveData.size() > 0) {
                powerConsistencyAnomalyMapper.insertList(saveData);
            }
            saveData = null;

            //额外保存稽核记录
            if (isSaveRecord && records.size() > 0) {
                consistencyAnomalyRecordMapper.insertList(records);
            }
            records = null;
        }
        list = null;
    }

    /**
     * 机楼电量业财一致率
     *
     * @param searchVo
     * @return
     */
    @Override
    public ConsistencyAuditVo consistency(ConsistencyAuditSearchVo searchVo) {
        ConsistencyAuditVo vo = consistencyAuditMapper.consistency(searchVo);
        if (vo == null) {
            vo = new ConsistencyAuditVo();
        }
        return vo;
    }

    /**
     * 电量业财一致率 一览查询
     *
     * @param searchVo
     * @return
     */
    @Override
    public List<ConsistencyAuditResultVo> list(ConsistencyAuditSearchVo searchVo) {
        List<ConsistencyAuditResultVo> list;
        if (StringUtils.isBlank(searchVo.getCompany())) {
            list = consistencyAuditMapper.list(searchVo);
        } else {
            list = consistencyAuditMapper.listCountry(searchVo);
        }
        if (list != null && list.size() > 1) {
            int num = 1;
            for (int i = 1; i < list.size(); i++) {
                ConsistencyAuditResultVo vo = list.get(i);
                if (StringUtils.isNotBlank(vo.getCompany())) {
                    vo.setRank(String.valueOf(num));
                    num += 1;
                }
            }
        }
        return list;
    }

    /**
     * 详情-机楼
     *
     * @return
     */
    @Override
    public ConsistencyAuditXqVo details(ConsistencyAuditXqSearchVo searchVo) {
        ConsistencyAuditXqVo vo = consistencyAuditMapper.details(searchVo);
        if (vo == null) {
            vo = new ConsistencyAuditXqVo();
        }
        return vo;
    }

    /**
     * 详情-详细信息列表
     *
     * @param searchVo
     * @return
     */
    @Override
    public List<ConsistencyAuditXqResultVo> detailsList(ConsistencyAuditXqSearchVo searchVo) {
        return consistencyAuditMapper.detailsList(searchVo);
    }
}
