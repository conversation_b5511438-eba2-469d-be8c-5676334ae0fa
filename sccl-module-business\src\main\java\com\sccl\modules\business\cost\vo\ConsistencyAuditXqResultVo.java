package com.sccl.modules.business.cost.vo;

import com.sccl.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机楼电量业财一致率- 详情-详细信息列表 结果列表
 */
@Data
public class ConsistencyAuditXqResultVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 局站名称
     */
    @Excel(name = "局站名称")
    private String station;

    /**
     * 站址编码
     */
    @Excel(name = "站址编码")
    private String resstationCode;

    /**
     * 局站类型
     */
    @Excel(name = "局站类型")
    private String stationType;

    /**
     * 所属分公司
     */
    @Excel(name = "所属分公司")
    private String companyName;

    /**
     * 所属部门
     */
    @Excel(name = "所属部门")
    private String countryName;

    /**
     * 起始日期 yyyyMMdd
     */
    @Excel(name = "起始日期")
    private String startdate;

    /**
     * 截止日期 yyyyMMdd
     */
    @Excel(name = "截止日期")
    private String enddate;

    /**
     * 业务日均电量
     */
    @Excel(name = "业务日均电量")
    private BigDecimal ywrjdl;

    /**
     * 财务日均电量
     */
    @Excel(name = "财务日均电量")
    private BigDecimal cwrjdl;


    /**
     * 一致率
     */
    @Excel(name = "一致率")
    private String rate;
    private BigDecimal rateBd;

    /**
     * 是否整改
     */
    @Excel(name = "是否整改")
    private String rect;

    /**
     * 月末
     */
    @Excel(name = "月末")
    private String endMonth;

    /**
     * 次月
     */
    @Excel(name = "次月")
    private String nextMonth;
}
