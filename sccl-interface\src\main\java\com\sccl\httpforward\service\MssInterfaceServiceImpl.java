package com.sccl.httpforward.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sccl.httpforward.TowerAccountSendVo;
import com.sccl.httpforward.TowerSendModel;
import com.sccl.httpforward.mapper.BillMapper;
import com.sccl.modules.mssaccount.mssinterface.domain.TowerAccountVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * MSS财务接口 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class MssInterfaceServiceImpl implements IMssInterfaceService {

    private static final Logger logger = LoggerFactory.getLogger(MssInterfaceServiceImpl.class);

    @Autowired
    private BillMapper billMapper;

    /**
     * 发送报账单
     *
     * @param billId 报账单ID
     * @return
     */
    @Override
    public TowerSendModel sendAccount(Long billId) {
        logger.info("开始处理报账单同步，报账单ID: {}", billId);
        // 封装数据
        TowerSendModel towerSendModel = new TowerSendModel();

        try {
            // 根据报账单id查询对应的台账
            List<TowerAccountVo> accountVoList = billMapper.getAccountListByBilId(billId);
            if (CollUtil.isNotEmpty(accountVoList)) {
                List<TowerAccountSendVo> dataList = new ArrayList<>();
                accountVoList.forEach(node -> {
                    TowerAccountSendVo towerAccountSendVo = new TowerAccountSendVo();
                    towerAccountSendVo.setTowerid(node.getTowerid());
                    // 若是铁塔id不为空，则使用铁塔对账中的数据
                    towerAccountSendVo.setCity(StrUtil.isBlank(node.getTowerid()) ?
                            node.getCompany() : node.getTowerCity());
                    towerAccountSendVo.setCountry(StrUtil.isBlank(node.getTowerid()) ?
                            node.getCountry() : node.getTowerDistrict());
                    towerAccountSendVo.setTowerSiteCode(StrUtil.isBlank(node.getTowerid()) ?
                            node.getStationaddresscode() : node.getTowersitecode());
                    towerAccountSendVo.setPaymentHouseholdID(StrUtil.isBlank(node.getTowerid()) ?
                            node.getSupplybureauammetercode() : node.getPayaccountnum());
                    towerAccountSendVo.setPaymentOrderNumber(StrUtil.isBlank(node.getTowerid()) ?
                            billId.toString() : node.getPaymentOrderNumber());
                    towerAccountSendVo.setStartTime(StrUtil.isBlank(node.getTowerid()) ?
                            node.getStartdate() : node.getUsestarttime());
                    towerAccountSendVo.setEndTime(StrUtil.isBlank(node.getTowerid()) ?
                            node.getEnddate() : node.getUseendtime());
                    towerAccountSendVo.setStartDegree(StrUtil.isBlank(node.getTowerid()) ?
                            node.getPrevtotalreadings().toString() : node.getUsestartdegree());
                    towerAccountSendVo.setEndDegree(StrUtil.isBlank(node.getTowerid()) ?
                            node.getCurtotalreadings().toString() : node.getUseenddegree());
                    towerAccountSendVo.setMultiplier(StrUtil.isBlank(node.getTowerid()) ?
                            node.getMulttimes().toString() : node.getMagnification());
                    towerAccountSendVo.setPowerConsumption(StrUtil.isBlank(node.getTowerid()) ?
                            node.getTotalusedreadings().toString() : node.getEnergyused());
                    towerAccountSendVo.setElectricityBill(StrUtil.isBlank(node.getTowerid()) ?
                            node.getAccountmoney().toString() : node.getActualpay());
                    towerAccountSendVo.setTelecomAllocationRatio(StrUtil.isBlank(node.getTowerid()) ?
                            node.getPercent().toString() : node.getApportionmentratio());
                    towerAccountSendVo.setTaxation(StrUtil.isBlank(node.getTowerid()) ?
                            node.getTaxamount().toString() : node.getTelecomtax());
                    towerAccountSendVo.setPrice(StrUtil.isBlank(node.getTowerid()) ?
                            node.getPrice().toString() : node.getTelecomprice());
                    towerAccountSendVo.setProcessStatus("已报帐完成");
                    towerAccountSendVo.setNotes("已报帐完成");
                    dataList.add(towerAccountSendVo);
                });
                towerSendModel.setParams(dataList);
            } else {
                logger.warn("报账单ID: {} 没有找到对应的台账数据", billId);
            }

        } catch (Exception e) {
            logger.error("报账单同步失败，报账单ID: {}, 错误信息: {}", billId, e.getMessage(), e);
            throw new RuntimeException("报账单同步失败: " + e.getMessage(), e);
        }
        return towerSendModel;
    }
}
