package com.sccl.modules.business.ammeterorprotocol.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sccl.framework.aspectj.lang.annotation.Excel;
import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.modules.autojob.util.convert.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 基础数据-电管理（注意该中所有的引用类型，
 * 都是指的power_category_type里面的type_code而非
 * ID，关注每个字段的注释）表 power_ammeterorprotocol
 *
 * <AUTHOR>
 * @date 2019-04-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Ammeterorprotocol extends BaseEntity {
    private String exceptionFlag;

    public String getExceptionFlag() {
        return exceptionFlag;
    }

    public void setExceptionFlag(String exceptionFlag) {
        this.exceptionFlag = exceptionFlag;
    }

    private static final long serialVersionUID = 1L;
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    /**
     * 类型
     * 在power_category_type表中type_category="ammeterCategory"
     */
    private Integer category;
    /**
     * 电表户名
     */
    @Excel(name = "电表户名")
    private String ammetername;
    /**
     * 供电局电表编号
     */
    @Excel(name = "供电局编号")
    private String supplybureauammetercode;
    /**
     * 是否智能电表
     */
    private Integer issmartammeter;
    /**
     * 供电局名称
     */
    @Excel(name = "供电局名称")
    private String supplybureauname;
    /**
     * 生成占比(%)
     */
    private BigDecimal generationof;
    /**
     * 局(站)定额电量比例(%)
     */
    private Integer quotapowerratio;
    /**
     * 局(站)编码
     */
    @Excel(name = "局站编码")
    private String stationcode;
    /**
     * 局(站)状态
     */
    @Excel(name = "局站状态")
    private Integer stationstatus;
    /**
     * 局(站)类型
     */
    private Integer stationtype;
    /**
     * 局(站)地址
     */
    @Excel(name = "局站地址")
    private String stationaddress;
    /**
     * 站址编码
     */
    @Excel(name = "站址编码")
    private String stationaddresscode;
    /**
     * 是否有空调
     */
    private Integer isairconditioning;
    /**
     * 类型 0=电表 1=协议
     */
    private Integer type;
    /**
     * 核定电量
     */
    @Excel(name = "核定电量")
    private BigDecimal vouchelectricity;
    /**
     * 关联协议名称
     */
    @Excel(name = "关联协议名称")
    private String protocolname;
    /**
     * 责任中心
     */
    @Excel(name = "责任中心")
    private Long country;
    /**
     * 分公司
     */
    @Excel(name = "分公司")
    private Long company;
    /**
     * 所属分局或者支局
     */
    @Excel(name = "所属分局或支局")
    private String substation;
    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectname;
    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String address;
    /**
     * 缴费名称
     */
    @Excel(name = "缴费名称")
    private String payname;
    /**
     * 付费方式
     * 在power_category_type表中type_category="payType"
     */
    private Integer paytype;
    /**
     * 缴费周期
     * 在power_category_type表中type_category="payPeriod"
     */
    private Integer payperiod;
    /**
     * 缴费经办人
     */
    private String paymanager;
    /**
     * 管理负责人
     */
    private String ammetermanager;
    /**
     * 电表类型
     * 在power_category_type表中type_category="ammeterType"
     */
    private Integer ammetertype;
    /**
     * 电价性质
     * 在power_category_type表中type_category="electrovalenceNature"
     */
    private Integer electrovalencenature;
    /**
     * 用电性质
     * 在power_category_type表中type_category="electroNature"
     */
    private Integer electronature;
    /**
     * 用电类型
     */
    private Long electrotype;
    /**
     * 倍率
     */
    private BigDecimal magnification;
    /**
     * 是否标准基站\n 1:是，0:否\n当用电属性选择"铁塔公司基站，、第三方租赁基站，自有产权基站"时展示该字段
     */
    private Integer isStdStation;
    /**
     * 是否供电局直供，0：否，1：是  改成对外结算类型 直供电、转供电
     * 在power_category_type表中type_category="directSupplyFlag"
     */
    private Integer directsupplyflag;
    /**
     * 电表类型为转供电，签约合同编号
     */
    private String transferPowerSupplyContractCode;
    /**
     * 电表类型为转供电，对应合同单价
     */
    private String transferPowerSupplyContractPrice;


    public String getStationcode5gr() {
        return stationcode5gr;
    }

    public void setStationcode5gr(String stationcode5gr) {
        this.stationcode5gr = stationcode5gr;
    }


    /**
     * 单价
     */
    @Excel(name = "单价")
    private BigDecimal price;
    /**
     * 包干类型
     * 在power_category_type表中type_category="packageType"
     */
    private Integer packagetype;
    /**
     * 关联外用户
     */
    private String outeruser;
    /**
     * 对方单位
     */
    private String userunit;
    /**
     * 具体位置
     */
    private String location;
    /**
     * 联系人
     */
    private String contractname;
    /**
     * 办公电话
     */
    private String officephone;
    /**
     * 手机
     */
    private String telephone;
    /**
     * 收款账户名称
     */
    private String receiptaccountname;
    /**
     * 收款展开开户行
     */
    private String receiptaccountbank;
    /**
     * 收款账户帐号
     */
    private String receiptaccounts;
    /**
     * 协议扫描
     */
    private String protocolscan;
    /**
     * 协议签订时间
     */
    private Date protocolsigneddate;
    /**
     * 协议签订终止时间
     */
    private Date protocolterminatedate;
    /**
     * 状态
     * 在power_category_type表中type_category="status"
     */
    private Integer status;
    /**
     * 协议包干费用
     */
    private BigDecimal fee;
    /**
     * 是否变损:1表示是，0或者空表示否
     */
    private Integer wastageFlag;
    /**
     * 翻表度数,即电表的最大度数
     */
    private BigDecimal maxdegree;

    /**
     * 新表起度
     */
    private BigDecimal startDegree;
    /**
     * 原表止度
     */
    private BigDecimal oldDegree;
    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inputdate;
    /**
     * 录入人
     */
    private Long inputuser;
    /**
     * 备注
     */
    private String memo;
    /**
     * 支局类型
     */
    private BigDecimal substationid;
    /**
     *
     */
    private String mapname;
    /**
     * 协议总表方类型
     */
    private Integer sourcetype;
    /**
     * 协议总表方开具发票类型
     */
    private Integer sourceinvoicetype;
    /**
     * 协议实际供电方
     */
    private Integer supplytype;
    /**
     * 协议实际供电方开具发票类型
     */
    private Integer supplyinvoicetype;
    /**
     * 站址产权归属(1自留2铁塔)
     */
    private Integer property;
    /**
     *
     */
    private String paymanagerTele;
    /**
     * 分割比例
     */
    private BigDecimal percent;
    /**
     *
     */
    private BigDecimal iprocessinstid;
    /**
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;
    /**
     *
     */
    private BigDecimal finishFlag;
    /**
     * 物理站址编码
     */
    private String phyMac;
    /**
     * 物理站址名称
     */
    private String phyName;
    /**
     * 对应资源局站
     */
    private String jtStationid;
    /**
     * 对应资源设备
     */
    private String jtDeviceid;
    /**
     * 帐期YYYYMM
     */
    private BigDecimal jtMonth;
    /**
     *
     */
    private Integer jtType;
    /**
     * 对应房屋编码
     */
    private String jtHousecode;
    /**
     * 对应资源局站
     */
    private String jtStationname;
    /**
     * 对应资源设备
     */
    private String jtDevicename;
    /**
     *
     */
    private String jtHousename;
    /**
     *
     */
    private String jtPhyMac;
    /**
     * 合同对方
     */
    private String contractOthPart;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 网管C网编号
     */
    private String nmCcode;
    /**
     * 网管编号L2.1G
     */
    private String nmL2100;
    /**
     * 网管编号L1.8G
     */
    private String nmL1800;
    /**
     * 网管编号C+L800M
     */
    private String nmCl800m;
    /**
     * 是否铁塔包干
     */
    private Integer islumpsum;
    /**
     * 包干起时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lumpstartdate;
    /**
     * 包干止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lumpenddate;
    /**
     * rru个数
     */
    private BigDecimal lumprrunum;
    private Boolean _disabled;
    /**
     * 是否上传附件
     */
    private Integer isAttach;
    /**
     * 单据状态  0：草稿 1：流程中 2：申请流程归档完成
     */
    private Integer billStatus;
    /**
     * 流程id
     */
    private Long processinstId;
    /**
     * 电表用途
     */
    private Integer ammeteruse;
    /**
     * 电表编号
     */
    private String ammeterno;
    /**
     * 是否实体电表
     */
    private Integer isentityammeter;
    /**
     * 流程类型
     */
    private String busiAlias;
    private String ammetertypename;
    /**
     * 父电表
     */
    private Long parentId;
    /**
     * 父电表 名称
     */
    private String parentCode;
    /**
     * 收款客户名称
     */
    private String customerId;
    /**
     * 收款客户名称
     */
    private String customerName;
    /**
     * 原电表/协议编号
     */
    private Long oldAmmeterId;
    /**
     * 是否换表
     */
    private Integer ischangeammeter;
    /**
     * 原电表还需报账电量(度)
     */
    private BigDecimal oldBillPower;
    /**
     * 月包干电量
     */
    private BigDecimal ybgPower;
    /**
     * 电压等级old
     *
     * @return
     */
    private Integer voltageClass;//10kv  <10kv
    /**
     * 直购电标识
     */
    private Integer directFlag;    // 0 否   1 是
    /**
     * 办公标识
     */
    private Integer officeFlag;    // 0 否   1 是

    //    单价项目增加字段
    private Integer transdistricompany;//1 国电 2 地方电网
    /**
     * 被换表的电表/协议名称
     */
    private String oldAmmeterName;
    /**
     * 定额
     */
    private String quotaId;
    /**
     * 所属分公司名称
     */
    private String companyName;
    private List<Map<String, Object>> countrys;//责任中心 1001,1002,1003
    /**
     * 责任中心名称
     */
    private String countryName;
    /**
     * 协议类型名称
     */
    private String categoryname;
    private String ammeterusename;
    private String electrotypename;
    private String electronaturename;
    private String paytypename;
    private String payperiodname;
    private String packagetypename;
    private String protocolsigneddateymd;
    private String protocolterminatedateymd;
    private String enddate;//台账截至日期
    private String resstationcode;//资源/房屋/站址编码
    private String checkType;//稽核类型 多选
    private String checkName;//稽核类型 多选
    private String downStatus;//稽核类型 多选
    private String doStatus;//稽核类型 多选
    private Integer iszgz;
    private String oldammetername;

    /**
     * 集团5gr基站站址编码
     */
    private String stationcode5gr;
    /**
     * 集团5gr基站站址名称
     */
    private String stationname5gr;

    private String deployTo;
    /**
     * 集团站址编码（有效性站址编码不变的key)
     */
    private String stationcodeintid;

    /**
     * 电表来源，1-铁塔录入，其他-自己录入
     */
    private String inputSource;

    /**
     * 票据类型在power_category_type表中type_category=demandInvoiceType
     */
    private Integer invoiceType;

    /**
     * 用电需求id
     */
    private String demandId;

    /**
     * 电表交付id
     */
    private String deliverId;

    /**
     * 电表编号（电表ID）
     */
    private String ammeterId;

    /**
     * 所属类型 1-铁塔 2-自有
     */
    private int ownType;

    /** 供应商或债权人的帐号 */
    private String lifnr;


    public String getTransferPowerSupplyContractCode() {
        return transferPowerSupplyContractCode;
    }

    public void setTransferPowerSupplyContractCode(String transferPowerSupplyContractCode) {
        this.transferPowerSupplyContractCode = transferPowerSupplyContractCode;
    }

    public String getTransferPowerSupplyContractPrice() {
        return transferPowerSupplyContractPrice;
    }

    public void setTransferPowerSupplyContractPrice(String transferPowerSupplyContractPrice) {
        this.transferPowerSupplyContractPrice = transferPowerSupplyContractPrice;
    }

    public String getTransfercontractname() {
        return transfercontractname;
    }

    public void setTransfercontractname(String transfercontractname) {
        this.transfercontractname = transfercontractname;
    }

    private String transfercontractname;

    //合同ID
    private String contractId;

    /**
     * 单价类型 1-固定单价 2-浮动单价
     * 转供电时必填
     */
    private Integer priceType;

    /**
     * 单价
     * 转供电时必填，浮动单价类型时必填，保留4位小数，不能为负数
     */
    private BigDecimal unitPrice;

    /**
     * 浮动单价
     * 单价类型为浮动单价时必填，必须大于单价，保留4位小数，不能为负数
     */
    private BigDecimal floatingPrice;

    /**
     * 是否含税 0-否 1-是
     * 转供电时必填
     */
    private Integer includeTax;

    /**
     * 不支付电表id
     */
    private Long ammeterIdNoPay;

    public static String getStrContract(Ammeterorprotocol pa) {
        BigDecimal price = pa.getPrice();
        String contractPrice = pa.getTransferPowerSupplyContractPrice();
        String ammetername = pa.getAmmetername();
        if (price == null && StringUtils.isBlank(contractPrice)) {
            return String.format("%s对应电表单价及签约单价为空", ammetername);
        }
        if (price == null) {
            return String.format("%s对应电表单价为空,对应签约单价:%s", ammetername, contractPrice);
        }
        if (StringUtils.isBlank(contractPrice)) {
            return String.format("%s对应签约单价为空,对应电表单价%s", ammetername, price.toString());
        }
        return String.format("%s对应电表单价:%s 签约单价:%s正常", ammetername, price.toString(), contractPrice);
    }

    public Integer getTransdistricompany() {
        return transdistricompany;
    }

    public void setTransdistricompany(Integer transdistricompany) {
        this.transdistricompany = transdistricompany;
    }

    public Integer getVoltageClass() {
        return voltageClass;
    }

    public void setVoltageClass(Integer voltageClass) {
        this.voltageClass = voltageClass;
    }

    public Date getLumpenddate() {
        return lumpenddate;
    }

    public void setLumpenddate(Date lumpenddate) {
        this.lumpenddate = lumpenddate;
    }

    public Integer getDirectFlag() {
        return directFlag;
    }

    public void setDirectFlag(Integer directFlag) {
        this.directFlag = directFlag;
    }

    public Integer getOfficeFlag() {
        return officeFlag;
    }

    public void setOfficeFlag(Integer officeFlag) {
        this.officeFlag = officeFlag;
    }

    public BigDecimal getYbgPower() {
        return ybgPower;
    }

    public void setYbgPower(BigDecimal ybgPower) {
        this.ybgPower = ybgPower;
    }

    public Long getOldAmmeterId() {
        return oldAmmeterId;
    }

    public void setOldAmmeterId(Long oldAmmeterId) {
        this.oldAmmeterId = oldAmmeterId;
    }

    public Integer getIschangeammeter() {
        return ischangeammeter;
    }

    public void setIschangeammeter(Integer ischangeammeter) {
        this.ischangeammeter = ischangeammeter;
    }

    public BigDecimal getOldBillPower() {
        return oldBillPower;
    }

    public void setOldBillPower(BigDecimal oldBillPower) {
        this.oldBillPower = oldBillPower;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getBusiAlias() {
        return busiAlias;
    }

    public void setBusiAlias(String busiAlias) {
        this.busiAlias = busiAlias;
    }

    public Integer getAmmeteruse() {
        return ammeteruse;
    }

    public void setAmmeteruse(Integer ammeteruse) {
        this.ammeteruse = ammeteruse;
    }

    public String getAmmeterno() {
        return ammeterno;
    }

    public void setAmmeterno(String ammeterno) {
        this.ammeterno = ammeterno;
    }

    public Integer getIsentityammeter() {
        return isentityammeter;
    }

    public void setIsentityammeter(Integer isentityammeter) {
        this.isentityammeter = isentityammeter;
    }

    public Long getProcessinstId() {
        return processinstId;
    }

    public void setProcessinstId(Long processinstId) {
        this.processinstId = processinstId;
    }

    public Integer getIsAttach() {
        return isAttach;
    }

    public void setIsAttach(Integer isAttach) {
        this.isAttach = isAttach;
    }

    public Integer getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(Integer billStatus) {
        this.billStatus = billStatus;
    }

    public Boolean get_disabled() {
        return _disabled;
    }

    public void set_disabled(Boolean _disabled) {
        this._disabled = _disabled;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getAmmetername() {
        return ammetername;
    }

    public void setAmmetername(String ammetername) {
        this.ammetername = ammetername;
    }

    public String getProtocolname() {
        return protocolname;
    }

    public void setProtocolname(String protocolname) {
        this.protocolname = protocolname;
    }

    public Long getCountry() {
        return country;
    }

    public void setCountry(Long country) {
        this.country = country;
    }

    public Long getCompany() {
        return company;
    }

    public void setCompany(Long company) {
        this.company = company;
    }

    public String getSubstation() {
        return substation;
    }

    public void setSubstation(String substation) {
        this.substation = substation;
    }

    public String getProjectname() {
        return projectname;
    }

    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getVouchelectricity() {
        return vouchelectricity;
    }

    public void setVouchelectricity(BigDecimal vouchelectricity) {
        this.vouchelectricity = vouchelectricity;
    }

    public String getPayname() {
        return payname;
    }

    public void setPayname(String payname) {
        this.payname = payname;
    }

    public Integer getPaytype() {
        return paytype;
    }

    public void setPaytype(Integer paytype) {
        this.paytype = paytype;
    }

    public Integer getPayperiod() {
        return payperiod;
    }

    public void setPayperiod(Integer payperiod) {
        this.payperiod = payperiod;
    }

    public String getPaymanager() {
        return paymanager;
    }

    public void setPaymanager(String paymanager) {
        this.paymanager = paymanager;
    }

    public String getAmmetermanager() {
        return ammetermanager;
    }

    public void setAmmetermanager(String ammetermanager) {
        this.ammetermanager = ammetermanager;
    }

    public Integer getAmmetertype() {
        return ammetertype;
    }

    public void setAmmetertype(Integer ammetertype) {
        this.ammetertype = ammetertype;
    }

    public Integer getElectrovalencenature() {
        return electrovalencenature;
    }

    public void setElectrovalencenature(Integer electrovalencenature) {
        this.electrovalencenature = electrovalencenature;
    }

    public Integer getElectronature() {
        return electronature;
    }

    public void setElectronature(Integer electronature) {
        this.electronature = electronature;
    }

    public Long getElectrotype() {
        return electrotype;
    }

    public void setElectrotype(Long electrotype) {
        this.electrotype = electrotype;
    }

    public BigDecimal getMagnification() {
        return magnification;
    }

    public void setMagnification(BigDecimal magnification) {
        this.magnification = magnification;
    }

    public Integer getIsStdStation() {
        return isStdStation;
    }

    public void setIsStdStation(Integer isStdStation) {
        this.isStdStation = isStdStation;
    }

    public Integer getDirectsupplyflag() {
        return directsupplyflag;
    }

    public void setDirectsupplyflag(Integer directsupplyflag) {
        this.directsupplyflag = directsupplyflag;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getPackagetype() {
        return packagetype;
    }

    public void setPackagetype(Integer packagetype) {
        this.packagetype = packagetype;
    }

    public String getOuteruser() {
        return outeruser;
    }

    public void setOuteruser(String outeruser) {
        this.outeruser = outeruser;
    }

    public String getUserunit() {
        return userunit;
    }

    public void setUserunit(String userunit) {
        this.userunit = userunit;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getContractname() {
        return contractname;
    }

    public void setContractname(String contractname) {
        this.contractname = contractname;
    }

    public String getOfficephone() {
        return officephone;
    }

    public void setOfficephone(String officephone) {
        this.officephone = officephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getReceiptaccountname() {
        return receiptaccountname;
    }

    public void setReceiptaccountname(String receiptaccountname) {
        this.receiptaccountname = receiptaccountname;
    }

    public String getReceiptaccountbank() {
        return receiptaccountbank;
    }

    public void setReceiptaccountbank(String receiptaccountbank) {
        this.receiptaccountbank = receiptaccountbank;
    }

    public String getReceiptaccounts() {
        return receiptaccounts;
    }

    public void setReceiptaccounts(String receiptaccounts) {
        this.receiptaccounts = receiptaccounts;
    }

    public String getProtocolscan() {
        return protocolscan;
    }

    public void setProtocolscan(String protocolscan) {
        this.protocolscan = protocolscan;
    }

    public Date getProtocolsigneddate() {
        return protocolsigneddate;
    }

    public void setProtocolsigneddate(Date protocolsigneddate) {
        this.protocolsigneddate = protocolsigneddate;
    }

    public Date getProtocolterminatedate() {
        return protocolterminatedate;
    }

    public void setProtocolterminatedate(Date protocolterminatedate) {
        this.protocolterminatedate = protocolterminatedate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public Integer getWastageFlag() {
        return wastageFlag;
    }

    public void setWastageFlag(Integer wastageFlag) {
        this.wastageFlag = wastageFlag;
    }

    public BigDecimal getMaxdegree() {
        return maxdegree;
    }

    public void setMaxdegree(BigDecimal maxdegree) {
        this.maxdegree = maxdegree;
    }

    public BigDecimal getStartDegree() {
        return startDegree;
    }

    public void setStartDegree(BigDecimal startDegree) {
        this.startDegree = startDegree;
    }

    public BigDecimal getOldDegree() {
        return oldDegree;
    }

    public void setOldDegree(BigDecimal oldDegree) {
        this.oldDegree = oldDegree;
    }

    public Date getInputdate() {
        return inputdate;
    }

    public void setInputdate(Date inputdate) {
        this.inputdate = inputdate;
    }

    public Long getInputuser() {
        return inputuser;
    }

    public void setInputuser(Long inputuser) {
        this.inputuser = inputuser;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public BigDecimal getSubstationid() {
        return substationid;
    }

    public void setSubstationid(BigDecimal substationid) {
        this.substationid = substationid;
    }

    public String getMapname() {
        return mapname;
    }

    public void setMapname(String mapname) {
        this.mapname = mapname;
    }

    public Integer getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }

    public Integer getSourceinvoicetype() {
        return sourceinvoicetype;
    }

    public void setSourceinvoicetype(Integer sourceinvoicetype) {
        this.sourceinvoicetype = sourceinvoicetype;
    }

    public Integer getSupplytype() {
        return supplytype;
    }

    public void setSupplytype(Integer supplytype) {
        this.supplytype = supplytype;
    }

    public Integer getSupplyinvoicetype() {
        return supplyinvoicetype;
    }

    public void setSupplyinvoicetype(Integer supplyinvoicetype) {
        this.supplyinvoicetype = supplyinvoicetype;
    }

    public Integer getProperty() {
        return property;
    }

    public void setProperty(Integer property) {
        this.property = property;
    }

    public String getPaymanagerTele() {
        return paymanagerTele;
    }

    public void setPaymanagerTele(String paymanagerTele) {
        this.paymanagerTele = paymanagerTele;
    }

    public BigDecimal getPercent() {
        return percent;
    }

    public void setPercent(BigDecimal percent) {
        this.percent = percent;
    }

    public BigDecimal getIprocessinstid() {
        return iprocessinstid;
    }

    public void setIprocessinstid(BigDecimal iprocessinstid) {
        this.iprocessinstid = iprocessinstid;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public BigDecimal getFinishFlag() {
        return finishFlag;
    }

    public void setFinishFlag(BigDecimal finishFlag) {
        this.finishFlag = finishFlag;
    }

    public String getPhyMac() {
        return phyMac;
    }

    public void setPhyMac(String phyMac) {
        this.phyMac = phyMac;
    }

    public String getPhyName() {
        return phyName;
    }

    public void setPhyName(String phyName) {
        this.phyName = phyName;
    }

    public String getJtStationid() {
        return jtStationid;
    }

    public void setJtStationid(String jtStationid) {
        this.jtStationid = jtStationid;
    }

    public String getJtDeviceid() {
        return jtDeviceid;
    }

    public void setJtDeviceid(String jtDeviceid) {
        this.jtDeviceid = jtDeviceid;
    }

    public BigDecimal getJtMonth() {
        return jtMonth;
    }

    public void setJtMonth(BigDecimal jtMonth) {
        this.jtMonth = jtMonth;
    }

    public Integer getJtType() {
        return jtType;
    }

    public void setJtType(Integer jtType) {
        this.jtType = jtType;
    }

    public String getJtHousecode() {
        return jtHousecode;
    }

    public void setJtHousecode(String jtHousecode) {
        this.jtHousecode = jtHousecode;
    }

    public String getJtStationname() {
        return jtStationname;
    }

    public void setJtStationname(String jtStationname) {
        this.jtStationname = jtStationname;
    }

    public String getJtDevicename() {
        return jtDevicename;
    }

    public void setJtDevicename(String jtDevicename) {
        this.jtDevicename = jtDevicename;
    }

    public String getJtHousename() {
        return jtHousename;
    }

    public void setJtHousename(String jtHousename) {
        this.jtHousename = jtHousename;
    }

    public String getJtPhyMac() {
        return jtPhyMac;
    }

    public void setJtPhyMac(String jtPhyMac) {
        this.jtPhyMac = jtPhyMac;
    }

    public String getContractOthPart() {
        return contractOthPart;
    }

    public void setContractOthPart(String contractOthPart) {
        this.contractOthPart = contractOthPart;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getNmCcode() {
        return nmCcode;
    }

    public void setNmCcode(String nmCcode) {
        this.nmCcode = nmCcode;
    }

    public String getNmL2100() {
        return nmL2100;
    }

    public void setNmL2100(String nmL2100) {
        this.nmL2100 = nmL2100;
    }

    public String getNmL1800() {
        return nmL1800;
    }

    public void setNmL1800(String nmL1800) {
        this.nmL1800 = nmL1800;
    }

    public String getNmCl800m() {
        return nmCl800m;
    }

    public void setNmCl800m(String nmCl800m) {
        this.nmCl800m = nmCl800m;
    }

    public Integer getIslumpsum() {
        return islumpsum;
    }

    public void setIslumpsum(Integer islumpsum) {
        this.islumpsum = islumpsum;
    }

    public Date getLumpstartdate() {
        return lumpstartdate;
    }

    public void setLumpstartdate(Date lumpstartdate) {
        this.lumpstartdate = lumpstartdate;
    }

    public BigDecimal getLumprrunum() {
        return lumprrunum;
    }

    public void setLumprrunum(BigDecimal lumprrunum) {
        this.lumprrunum = lumprrunum;
    }

    public String getSupplybureauammetercode() {
        return supplybureauammetercode;
    }

    public void setSupplybureauammetercode(String supplybureauammetercode) {
        this.supplybureauammetercode = supplybureauammetercode;
    }

    public Integer getIssmartammeter() {
        return issmartammeter;
    }

    public void setIssmartammeter(Integer issmartammeter) {
        this.issmartammeter = issmartammeter;
    }

    public String getSupplybureauname() {
        return supplybureauname;
    }

    public void setSupplybureauname(String supplybureauname) {
        this.supplybureauname = supplybureauname;
    }

    public BigDecimal getGenerationof() {
        return generationof;
    }

    public void setGenerationof(BigDecimal generationof) {
        this.generationof = generationof;
    }

    public Integer getQuotapowerratio() {
        return quotapowerratio;
    }

    public void setQuotapowerratio(Integer quotapowerratio) {
        this.quotapowerratio = quotapowerratio;
    }

    public String getStationcode() {
        return stationcode;
    }

    public void setStationcode(String stationcode) {
        this.stationcode = stationcode;
    }

    public Integer getStationstatus() {
        return stationstatus;
    }

    public void setStationstatus(Integer stationstatus) {
        this.stationstatus = stationstatus;
    }

    public Integer getStationtype() {
        return stationtype;
    }

    public void setStationtype(Integer stationtype) {
        this.stationtype = stationtype;
    }

    public String getStationaddress() {
        return stationaddress;
    }

    public void setStationaddress(String stationaddress) {
        this.stationaddress = stationaddress;
    }

    public String getStationaddresscode() {
        return stationaddresscode;
    }

    public void setStationaddresscode(String stationaddresscode) {
        this.stationaddresscode = stationaddresscode;
    }

    public Integer getIsairconditioning() {
        return isairconditioning;
    }

    public void setIsairconditioning(Integer isairconditioning) {
        this.isairconditioning = isairconditioning;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("category", getCategory()).append("ammetername", getAmmetername()).append("protocolname", getProtocolname()).append("country", getCountry()).append("company", getCompany()).append("substation", getSubstation()).append("projectname", getProjectname()).append("address", getAddress()).append("payname", getPayname()).append("paytype", getPaytype()).append("payperiod", getPayperiod()).append("paymanager", getPaymanager()).append("ammetermanager", getAmmetermanager()).append("ammetertype", getAmmetertype()).append("electrovalencenature", getElectrovalencenature()).append("electronature", getElectronature()).append("electrotype", getElectrotype()).append("magnification", getMagnification()).append("isStdStation", getIsStdStation()).append("directsupplyflag", getDirectsupplyflag()).append("price", getPrice()).append("packagetype", getPackagetype()).append("outeruser", getOuteruser()).append("userunit", getUserunit()).append("location", getLocation()).append("contractname", getContractname()).append("officephone", getOfficephone()).append("telephone", getTelephone()).append("receiptaccountname", getReceiptaccountname()).append("receiptaccountbank", getReceiptaccountbank()).append("receiptaccounts", getReceiptaccounts()).append("protocolscan", getProtocolscan()).append("protocolsigneddate", getProtocolsigneddate()).append("protocolterminatedate", getProtocolterminatedate()).append("status", getStatus()).append("fee", getFee()).append("wastageFlag", getWastageFlag()).append("maxdegree", getMaxdegree()).append("inputdate", getInputdate()).append("inputuser", getInputuser()).append("memo", getMemo()).append("substationid", getSubstationid()).append("mapname", getMapname()).append("sourcetype", getSourcetype()).append("sourceinvoicetype", getSourceinvoicetype()).append("supplytype", getSupplytype()).append("supplyinvoicetype", getSupplyinvoicetype()).append("property", getProperty()).append("paymanagerTele", getPaymanagerTele()).append("percent", getPercent()).append("iprocessinstid", getIprocessinstid()).append("finishTime", getFinishTime()).append("finishFlag", getFinishFlag()).append("phyMac", getPhyMac()).append("phyName", getPhyName()).append("jtStationid", getJtStationid()).append("jtDeviceid", getJtDeviceid()).append("jtMonth", getJtMonth()).append("jtType", getJtType()).append("jtHousecode", getJtHousecode()).append("jtStationname", getJtStationname()).append("jtDevicename", getJtDevicename()).append("jtHousename", getJtHousename()).append("jtPhyMac", getJtPhyMac()).append("contractOthPart", getContractOthPart()).append("stationName", getStationName()).append("nmCcode", getNmCcode()).append("nmL2100", getNmL2100()).append("nmL1800", getNmL1800()).append("nmCl800m", getNmCl800m()).append("islumpsum", getIslumpsum()).append("lumpstartdate", getLumpstartdate()).append("lumprrunum", getLumprrunum()).append("supplybureauammetercode", getSupplybureauammetercode()).append("issmartammeter", getIssmartammeter()).append("supplybureauname", getSupplybureauname()).append("generationof", getGenerationof()).append("quotapowerratio", getQuotapowerratio()).append("stationcode", getStationcode()).append("stationstatus", getStationstatus()).append("stationtype", getStationtype()).append("stationaddress", getStationaddress()).append("stationaddresscode", getStationaddresscode()).append("isairconditioning", getIsairconditioning()).append("type", getType()).append("vouchelectricity", getVouchelectricity()).append("isAttach", getIsAttach()).append("processinstId", getProcessinstId()).append("ammeteruse", getAmmeteruse()).append("ammeterno", getAmmeterno()).append("isentityammeter", getIsentityammeter()).append("busiAlias", getBusiAlias()).append("delFlag", getDelFlag()).append("oldAmmeterId", getOldAmmeterId()).append("ischangeammeter", getIschangeammeter()).append("oldBillPower", getOldBillPower()).toString();
    }

    public String getOldAmmeterName() {
        return oldAmmeterName;
    }

    public void setOldAmmeterName(String oldAmmeterName) {
        this.oldAmmeterName = oldAmmeterName;
    }

    public String getAmmeterusename() {
        return ammeterusename;
    }

    public void setAmmeterusename(String ammeterusename) {
        this.ammeterusename = ammeterusename;
    }

    public String getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(String quotaId) {
        this.quotaId = quotaId;
    }

    public List<Map<String, Object>> getCountrys() {
        return countrys;
    }

    public void setCountrys(List<Map<String, Object>> countrys) {
        this.countrys = countrys;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getCategoryname() {
        return categoryname;
    }

    public void setCategoryname(String categoryname) {
        this.categoryname = categoryname;
    }

    public String getElectrotypename() {
        return electrotypename;
    }

    public void setElectrotypename(String electrotypename) {
        this.electrotypename = electrotypename;
    }

    public String getElectronaturename() {
        return electronaturename;
    }

    public void setElectronaturename(String electronaturename) {
        this.electronaturename = electronaturename;
    }

    public String getPaytypename() {
        return paytypename;
    }

    public void setPaytypename(String paytypename) {
        this.paytypename = paytypename;
    }

    public String getPayperiodname() {
        return payperiodname;
    }

    public void setPayperiodname(String payperiodname) {
        this.payperiodname = payperiodname;
    }

    public String getPackagetypename() {
        return packagetypename;
    }

    public void setPackagetypename(String packagetypename) {
        this.packagetypename = packagetypename;
    }

    public String getProtocolsigneddateymd() {
        if (this.protocolsigneddate != null && String.valueOf(this.protocolsigneddate).length() > 0) {
            return sdf.format(this.protocolsigneddate);
            //return String.valueOf(this.protocolsigneddate.getYear())+"-"+String.valueOf(this.protocolsigneddate.getMonth())+"-"+String.valueOf(this.protocolsigneddate.getDay());
        } else {
            return null;
        }
    }

    public void setProtocolsigneddateymd(String protocolsigneddateymd) {
        if (this.protocolsigneddate != null && String.valueOf(this.protocolsigneddate).length() > 0) {
            this.protocolsigneddateymd = sdf.format(this.protocolsigneddate);
            //String.valueOf(this.protocolsigneddate.getYear())+"-"+String.valueOf(this.protocolsigneddate.getMonth())+"-"+String.valueOf(this.protocolsigneddate.getDay());
        } else {
            this.protocolsigneddateymd = null;
        }
    }

    public String getProtocolterminatedateymd() {
        if (this.protocolterminatedate != null && String.valueOf(this.protocolterminatedate).length() > 0) {
            return sdf.format(this.protocolterminatedate);
            //return String.valueOf(this.protocolterminatedate.getYear())+"-"+String.valueOf(this.protocolterminatedate.getMonth())+"-"+String.valueOf(this.protocolterminatedate.getDay());
        } else {
            return null;
        }
    }

    public void setProtocolterminatedateymd(String protocolterminatedateymd) {
        if (this.protocolterminatedate != null && String.valueOf(this.protocolterminatedate).length() > 0) {
            this.protocolterminatedateymd = sdf.format(this.protocolterminatedate);
            //String.valueOf(this.protocolterminatedate.getYear())+"-"+String.valueOf(this.protocolterminatedate.getMonth())+"-"+String.valueOf(this.protocolterminatedate.getDay());
        } else {
            this.protocolterminatedateymd = null;
        }
    }

    public String getEnddate() {
        return enddate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public String getAmmetertypename() {
        return ammetertypename;
    }

    public void setAmmetertypename(String ammetertypename) {
        this.ammetertypename = ammetertypename;
    }

    public String getResstationcode() {
        return resstationcode;
    }

    public void setResstationcode(String resstationcode) {
        this.resstationcode = resstationcode;
    }

    public String getCheckName() {
        return checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }

    public String getDownStatus() {
        return downStatus;
    }

    public void setDownStatus(String downStatus) {
        this.downStatus = downStatus;
    }

    public String getDoStatus() {
        return doStatus;
    }

    public void setDoStatus(String doStatus) {
        this.doStatus = doStatus;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public Integer getIszgz() {
        return iszgz;
    }

    public void setIszgz(Integer iszgz) {
        this.iszgz = iszgz;
    }

    public String getOldammetername() {
        return oldammetername;
    }

    public void setOldammetername(String oldammetername) {
        this.oldammetername = oldammetername;
    }

    public String getInputSource() {
        return inputSource;
    }

    public void setInputSource(String inputSource) {
        this.inputSource = inputSource;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getDemandId() {
        return demandId;
    }

    public void setDemandId(String demandId) {
        this.demandId = demandId;
    }

    public String getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(String deliverId) {
        this.deliverId = deliverId;
    }

    public String getAmmeterId() {
        return ammeterId;
    }

    public void setAmmeterId(String ammeterId) {
        this.ammeterId = ammeterId;
    }
    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }
}
