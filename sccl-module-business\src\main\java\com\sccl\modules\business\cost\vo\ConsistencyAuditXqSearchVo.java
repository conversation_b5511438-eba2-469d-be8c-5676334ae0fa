package com.sccl.modules.business.cost.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 机楼电量业财一致率-详情 查询条件对象
 */
@Data
public class ConsistencyAuditXqSearchVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查询标识 1 机楼 2基站
     */
    private String flag;

    /**
     * 数据年份【yyyy】
     */
    private String year;

    /**
     * 月份【MM】
     */
    private String month;
    private int monthNum;

    /**
     * 月末 yyyyMMdd
     */
    private String endMonth;

    /**
     * 所属分公司
     */
    private String company;
    private String[] companys;

    /**
     * 所属部门
     */
    private String country;
    private String[] countrys;

    /**
     * 关键字 局站名称关键字查询
     */
    private String key;

    /**
     * 一致率 开始
     */
    private BigDecimal consFrom;

    /**
     * 一致率 结束
     */
    private BigDecimal consTo;

    /**
     * 站址编码
     */
    private String resstationCode;

    /**
     * 局站类型
     */
    private String stationType;

    /**
     * 年月【yyyy年MM月】
     * @ignore
     */
    private String yearStr;
}
