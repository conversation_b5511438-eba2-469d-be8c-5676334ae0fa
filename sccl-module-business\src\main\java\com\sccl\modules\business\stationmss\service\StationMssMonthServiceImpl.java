package com.sccl.modules.business.stationmss.service;

import com.google.common.collect.Lists;
import com.sccl.common.lang.StringUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.stationmss.domain.StationMssMonth;
import com.sccl.modules.business.stationmss.mapper.StationMssMonthMapper;
import com.sccl.modules.business.stationmss.vo.StationMssMonthParamVo;
import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter;
import com.sccl.modules.mssaccount.mssinterface.mapper.CollectMeterMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;


/**
 * 局站财务月日均电量表 服务层实现
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
@Slf4j
public class StationMssMonthServiceImpl extends BaseServiceImpl<StationMssMonth> implements IStationMssMonthService {

    @Autowired
    private StationMssMonthMapper stationMssMonthMapper;

    @Autowired
    private CollectMeterMapper collectMeterMapper;

    /**
     * 统计局站财务月日均电量表数据
     * @param tjsj 统计时间【yyyy-MM】
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer statisticsAuto(String tjsj) {
        //先根据统计时间及统计标识批量删除
        stationMssMonthMapper.delByTjsj(tjsj);

        //开始统计
        String year = StringUtils.left(tjsj, 4);
        String month = StringUtils.right(tjsj, 2);
        StationMssMonthParamVo entity = new StationMssMonthParamVo();
        entity.setTjsj(tjsj);  //统计时间
        entity.setYear(year);  //统计年份
        entity.setMonth(month);//统计月份
        List<StationMssMonth> list = stationMssMonthMapper.statisticsAuto(entity);
        Integer cnt = 0;
        if (list != null && list.size() > 0) {
            cnt = list.size();
            List<StationMssMonth> saveData = new ArrayList<>();
            for (StationMssMonth vo : list) {
                if (StringUtils.isBlank(vo.getStationCode())) {
                    continue;
                }
                StationMssMonth stationMssMonth = new StationMssMonth();
                BeanUtils.copyProperties(vo, stationMssMonth);
                stationMssMonth.initInsert("1");
                saveData.add(stationMssMonth);
                if (saveData.size() == 1000) {
                    stationMssMonthMapper.insertList(saveData);
                    saveData.clear();
                }
            }
            if (saveData.size() > 0) {
                stationMssMonthMapper.insertList(saveData);
            }
            saveData = null;
        }
        list = null;
        return cnt;
    }

    /**
     * 同步局站财务日均电量到采集表
     * @param collectTime 采集时间【yyyyMMdd】
     */
    @Override
    public Integer doSyncCollectmeter(String collectTime) {
        //先删除采集表数据
        collectMeterMapper.delAll();

        //获取局站财务日均电量
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate coll = LocalDate.parse(collectTime, formatter); // 明确指定格式
        LocalDate last = coll.minusMonths(1L);//上月
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
        String yearmonth = dtf.format(last);//财务帐期
        List<CollectMeter> list = stationMssMonthMapper.getSyncCollectmeter(collectTime, yearmonth);
        //开始同步局站财务日均电量到采集表
        Integer cnt = 0;
        if (list != null && list.size() > 0) {
            //分割成最大长度为1000的小list以便保存
            cnt = list.size();
            List<List<CollectMeter>> partLists = Lists.partition(list, 1000);
            for (List<CollectMeter> partList : partLists) {
                collectMeterMapper.insertList(partList);
            }
            partLists = null;
        }
        list = null;
        return cnt;
    }
}
