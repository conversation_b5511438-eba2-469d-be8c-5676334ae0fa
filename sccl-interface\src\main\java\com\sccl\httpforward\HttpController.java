package com.sccl.httpforward;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.sccl.common.io.PropertiesUtils;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ObjectStoreUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.exception.BusinessException;
import com.sccl.framework.datasource.DynamicDataSourceContextHolder;
import com.sccl.framework.utils.FileUploadUtils;
import com.sccl.framework.utils.WebServiceClient;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.httpforward.dto.BillIdDTO;
import com.sccl.httpforward.service.MssInterfaceServiceImpl;
import com.sccl.lnftp.AutoJobLn;
import com.sccl.modules.business.job.domain.JobLog;
import com.sccl.modules.business.job.service.IPowerIntelligentInf2Service;
import com.sccl.modules.business.job.service.JobService;
import com.sccl.modules.job.conf.AutoJob;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/httpforward")
public class HttpController extends BaseController {

    @Autowired
    private JobService jobLogService;
    @Autowired
    private MssClient mssClient;
    @Autowired
    private MssInterfaceServiceImpl mssInterfaceServiceImpl;

    @Autowired
    private IPowerIntelligentInf2Service powerIntelligentInf2Service;

    @Value("${towerMapUrl}")
    private String towerMapUrl;

    @Value("${TowerShareRelateInterface.UrlConig.USER_ID}")
    private String ACCOUNT_CALLBACK_USER_ID;

    @Value("${TowerShareRelateInterface.UrlConig.PASSWORD}")
    private String ACCOUNT_CALLBACK_PASSWORD;

    @Autowired(required = false)
    private AttachmentsMapper attachmentsMapper;

    @Value("${sccl.energyUrl}")
    private String energyUrl;

    @RequestMapping("/doMss")
    @ResponseBody
    public String doMss(@RequestBody MssModel mssModel) {
        System.out.println(mssModel.toString());
        String s = null;
        try {
            s = mssClient.callCF(mssModel.getUrl(), mssModel.getParams());
            System.out.println(s);
            JobLog model = new JobLog();
            model.setRunTime(new Date());
            model.setJobLog(mssModel.getParams() + "return" + s);
            model.setJobName("doMss");
            model.setJobType("SUCCESS");
            jobLogService.insert(model);
            s = StringEscapeUtils.unescapeHtml(s);// 转义字符
            //System.out.println("转义字符:"+s);
        } catch (Exception e) {
            e.printStackTrace();
            JobLog model = new JobLog();
            model.setRunTime(new Date());
            model.setJobLog(mssModel.getParams() + "ERROR" + e.getMessage());
            model.setJobName("doMss");
            model.setJobType("ERROR");
            jobLogService.insert(model);
            s = e.getMessage();
        }
        return s;
    }


    /**
     * 推送铁塔完成报账单至铁塔方
     */
    @RequestMapping("/sendToTowerAccountold")
    @ResponseBody
    public String sendToTowerAccountold(@RequestBody TowerSendModel TowerSendModel) {
        List<TowerAccountSendVo> dataList = TowerSendModel.getParams();
        String jsonData = JSONUtil.toJsonStr(dataList);
        HttpRequest post = HttpUtil.createPost(towerMapUrl + "whptcs/InterfaceApi/zgdx/monitor?OPERATION=REIMBURSE");
        Dict body = Dict.create().set("USER_ID", ACCOUNT_CALLBACK_USER_ID).
                set("PASSWORD", ACCOUNT_CALLBACK_PASSWORD).set("data", jsonData);
        HttpResponse response = post.body(JSONUtil.toJsonStr(body)).execute();
        String responseBody = response.body();

        JobLog model = new JobLog();
        model.setRunTime(new Date());
        model.setJobName("sendToTowerAccount");
        System.out.println(("电信报账接口新增报账流程回调的上报结果:" + responseBody));
        model.setJobLog("电信报账接口新增报账流程回调的上报结果:" + responseBody);
        if (responseBody.contains("已收到报账回调")) {
            model.setJobType("SUCCESS");
            model.setJobLog("电信报账接口新增报账流程上报成功!!!");
        } else {
            model.setJobType("FAIL");
            model.setJobLog("电信报账接口新增报账流程回调的上报结果:" + responseBody);
        }
        jobLogService.insert(model);
        return "上报流程结束";
    }

    /**
     * 推送铁塔完成报账单至铁塔方
     */
    @RequestMapping("/sendToTowerAccount")
    @ResponseBody
    public String sendToTowerAccount(@RequestBody TowerSendModel TowerSendModel) {
        List<TowerAccountSendVo> dataList = TowerSendModel.getParams();
        HttpRequest post = HttpUtil.createPost(towerMapUrl + "whptcs/InterfaceApi/zgdx/monitor?OPERATION=REIMBURSE");
        Dict body = Dict.create()
                .set("USER_ID", ACCOUNT_CALLBACK_USER_ID)
                .set("PASSWORD", ACCOUNT_CALLBACK_PASSWORD)
                // 必须是字符串
                .set("data", JSONUtil.toJsonStr(dataList));
        System.out.println(StrUtil.format("发送数据: {}", JSONUtil.toJsonStr(body)));
        HttpResponse response = post.body(JSONUtil.toJsonStr(body)).execute();
        String responseBody = response.body();

        JobLog model = new JobLog();
        model.setRunTime(new Date());
        model.setJobName("sendToTowerAccount");
        System.out.println(("电信报账接口新增报账流程回调的上报结果:" + responseBody));
        model.setJobLog("电信报账接口新增报账流程回调的上报结果:" + responseBody);
        if (responseBody.contains("已收到报账回调")) {
            model.setJobType("SUCCESS");
            model.setJobLog("电信报账接口新增报账流程上报成功!!!");
        } else {
            model.setJobType("FAIL");
            model.setJobLog("电信报账接口新增报账流程回调的上报结果:" + responseBody);
        }
        jobLogService.insert(model);

        return "上报流程结束";
    }

    /**
     * 电信稽核结果推送至铁塔方反馈
     */
    @RequestMapping("/sendToTowerAudit")
    @ResponseBody
    public String sendToTowerAudit(@RequestBody TowerSendModel TowerSendModel) {
        List<TowerAccountSendVo> dataList = TowerSendModel.getParams();
        Dict jobLog = Dict.create();
        String jsonData = JSONUtil.toJsonStr(dataList);
        HttpRequest post = HttpUtil.createPost(towerMapUrl + "whptcs/InterfaceApi/zgdx/monitor?OPERATION=REIMBURSE");
        Dict body = Dict.create().set("USER_ID", ACCOUNT_CALLBACK_USER_ID).
                set("PASSWORD", ACCOUNT_CALLBACK_PASSWORD).set("data", jsonData);
        jobLog.set("requestData", body);
        HttpResponse response = post.body(JSONUtil.toJsonStr(body)).execute();
        String responseBody = response.body();
        jobLog.set("responseBody", responseBody);

        JobLog model = new JobLog();
        model.setRunTime(new Date());
        model.setJobName("sendToTowerAudit");
        System.out.println(("电信稽核结果推送至铁塔方反馈结果:" + responseBody));
        model.setJobLog("电信稽核结果推送至铁塔方反馈结果:" + responseBody);
        List<Long> syncIds = dataList.stream().map(TowerAccountSendVo::getSyncId).collect(Collectors.toList());
        if (responseBody.contains("已收到报账回调")) {
            model.setJobType("SUCCESS");
            model.setJobLog(JSONUtil.toJsonStr(jobLog));
            // 更新电信稽核结果推送至铁塔方反馈成功
            saveFeedBack(syncIds, "1");
        } else {
            model.setJobType("FAIL");
            model.setJobLog(JSONUtil.toJsonStr(jobLog));
            // 更新电信稽核结果推送至铁塔方反馈成功
            saveFeedBack(syncIds, "0");
        }
        jobLogService.insert(model);
        return "反馈成功";
    }


    @RequestMapping("/doWeb")
    @ResponseBody
    public String doWeb(@RequestBody WebServiceModel webServiceModel) {
        System.out.println(webServiceModel.toString());
        String string = null;
        try {
            String xmlurl = webServiceModel.getUrl();
            String targetNamespace = webServiceModel.getNameSpace();
            String callMethod = webServiceModel.getMethod();
            String[] argNames = webServiceModel.getNames();
            String[] xmlvalues = webServiceModel.getValues();
            string = WebServiceClient.callWebService(xmlurl, "POST", null,
                    targetNamespace, callMethod, argNames, xmlvalues);

            JobLog model = new JobLog();
            model.setRunTime(new Date());
            model.setJobLog(webServiceModel.toString() + "return" + string);
            model.setJobName("doMss");
            model.setJobType("SUCCESS");
            jobLogService.insert(model);
        } catch (Exception e) {
            e.printStackTrace();
            JobLog model = new JobLog();
            model.setRunTime(new Date());
            model.setJobLog(webServiceModel.toString() + "ERROR" + e.getMessage());
            model.setJobName("doMss");
            model.setJobType("ERROR");
            jobLogService.insert(model);
            string = e.getMessage();
        }
        return string;
    }

    @Autowired
    private AutoJobLn autoJobLn;
    @Autowired
    private AutoJob autoJob;

    //辽宁化小
    @GetMapping("/doJob")
    @ResponseBody
    public AjaxResult doJob(@RequestParam(value = "data", required = false) String date) {
        if (StringUtils.isBlank(date)) {
            date = null;
        }
        AjaxResult res = new AjaxResult();
        autoJobLn.dojob(res, date);
        return res;
    }

    @GetMapping("/doPue/{id}")
    @ResponseBody
    public AjaxResult doPue() {
        AjaxResult res = new AjaxResult();
        DynamicDataSourceContextHolder.setDB("ECM");
        List<Map<String, Object>> lists = powerIntelligentInf2Service.selectTotalPower();
        if (lists.size() != 0) {
            powerIntelligentInf2Service.powerElectricity(lists);
        }
        res.put("PUE", lists.size());
        return res;
    }


    @RequestMapping("/getTafiles")
    @ResponseBody
    public AjaxResult getTafiles(@RequestBody TaFiles taFiles) {
        //AjaxResult res = new AjaxResult();
        DynamicDataSourceContextHolder.setDB("ECM");
        String files = taFiles.getFiles();
        String pcid = taFiles.getPcid();
        long Lpcid = Long.valueOf(pcid).longValue();
        String annexs[] = files.split(",");
        System.out.println("attachment files size" + annexs.length);
        for (int k = 0; k < annexs.length; k++) {
            Attachments attachments = new Attachments();
            //验证UpLoadFile对象必填值


            //封装attachment对象

            System.out.println(annexs[k]);
            String urlsub = annexs[k].substring(annexs[k].lastIndexOf("sctower.cn/") + 11, annexs[k].length());
            String scurl = towerMapUrl + urlsub;
            String fileName = annexs[k].substring(annexs[k].lastIndexOf("/") + 1);
            // 获取文件的后缀名
            String suffixName = annexs[k].substring(annexs[k].lastIndexOf("."));

            attachments.setBusiId(Lpcid);
            attachments.setCreatorId(2L);
            attachments.setCreatorName("sys");
            attachments.setBusiAlias("附件(铁塔对账台账)");
            attachments.setCategoryCode("file");
            attachments.setFileName(fileName);
            Long attachmentId = null;
            attachments.setYear(Integer.valueOf(DateUtils.getYear()));

            attachments.setDelFlag("0");
            attachments.setCollection("附件(铁塔对账台账)");
            attachments.setShardKey("SCCL");
            //MONGODB的数据库名为‘年’+‘分片’
            attachments.setDatabaseName(attachments.getYear() + attachments.getShardKey());

            //工具类保存
            attachments.setSaveType("GJ");


            //四川上传到对象服务器OSS
            String version = PropertiesUtils.getInstance().getProperty("sccl.deployTo");
            String envProfile = PropertiesUtils.getInstance().getEnvProfile();
            String filedIdName = "", filedId = "";
            if ("sc".equals(envProfile) && "sc".equals(version)) {
                filedIdName = FileUploadUtils.encodingFilename(fileName, suffixName);

                System.out.println("铁塔台账附件接口请求url:" + scurl);
                //Long sizef = (long) saveToFile(scurl, fileName);
                boolean isSuccess = ObjectStoreUtils.upload2OSS(filedIdName, getImageStream(scurl));

                if (isSuccess) {
                    System.out.println("upload to oss ");
                    //attachments.setFileSize(sizef);
                    filedId = filedIdName.substring(0, filedIdName.lastIndexOf("."));
                    attachments.setMongodbFileId(filedId);
                    attachments.setUrl("OSS");
                    attachmentsMapper.insert(attachments);
                } else {

                    System.out.println("upload to oss fail!");
                }
            }
        }

        return this.success("加入(" + annexs.length + ")条");
    }

    private void saveFeedBack(List<Long> ids, String status) {
        long startTime = System.currentTimeMillis();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate lastMonth = LocalDate
                .now()
                .minusMonths(1);
        String currDate = dtf.format(lastMonth);
        try {
            RestTemplate rest = new RestTemplate();

            String url = energyUrl + "/business/auditSyncFeedBack/updateFeedBackStatus";

            // 使用 UriComponentsBuilder 构建带有查询参数的 URL
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("ids", ids)
                    .queryParam("status", status);

            AjaxResult result = rest.getForObject(builder.toUriString(), AjaxResult.class);
            System.out.println(result.get("msg"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private InputStream getImageStream(String url) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setReadTimeout(10000);
            connection.setConnectTimeout(10000);
            connection.setRequestMethod("GET");
            System.out.println("connection.getResponseCode() :" + connection.getResponseCode());
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = connection.getInputStream();
                return inputStream;
            }
        } catch (IOException e) {
            System.out.println("获取网络图片出现异常，图片路径为：" + url);
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 测试同步铁塔报账数据
     * 查询铁塔报账单id
     * select DISTINCT concat('',mss_accountbill.id) as id from mss_accountbill
     * left join mss_r_billitem_account acc on acc.bill_id = mss_accountbill.id
     * left join `power_account` a on a.pcid = acc.account_id
     * LEFT JOIN power_ammeterorprotocol b on a.ammeterid = b.id
     * left join toweraccount c on a.toweraccountid = c.id
     * where mss_accountbill.WRITEOFF_INSTANCE_CODE is not null
     * and mss_accountbill.status = 7
     * and mss_accountbill.year = '2025'
     * and mss_accountbill.BIZ_ENTRY_CODE = '07'
     * and b.electrotype in (1411,1412)
     * order by mss_accountbill.id desc;
     *
     * @param dto bill对象
     * @return 结果
     */
    @PostMapping("/testSyncTieTaData")
    public AjaxResult testSyncTieTaData(@RequestBody BillIdDTO dto) {
        if (CollUtil.isEmpty(dto.getBillIdList())) {
            throw new BusinessException("报账单id不能为空");
        }
        System.out.println("测试同步铁塔报账数据-开始");
        try {
            // 切换到ECM数据库
            System.out.println("切换到ECM数据库");
            System.out.println("开始同步铁塔报账数据");
            for (Long billId : dto.getBillIdList()) {
                System.out.println("开始同步铁塔报账数据:" + billId);

                DynamicDataSourceContextHolder.setDB("ECM");
                TowerSendModel towerSendModel = mssInterfaceServiceImpl.sendAccount(billId);
                DynamicDataSourceContextHolder.clearDB();

                log.info("测试同步铁塔报账数据:{}", towerSendModel);
                sendToTowerAccount(towerSendModel);
            }
        } finally {
            // 清除数据源设置
            DynamicDataSourceContextHolder.clearDB();
        }
        return AjaxResult.success();
    }
}
