package com.sccl.modules.mssaccount.mssinterface.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 检查账单报账电表是否在铁塔转供清单中结果VO对象
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
public class CheckTowerVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用电类型
     */
    private String stationtype;

    /**
     * 对外结算类型
     */
    private String directsupplyflag;

    /**
     * 电表编号
     */
    private String ammeterName;

    /**
     * 项目名称
     */
    private String projectname;

    /**
     * 站址编码
     */
    protected String stationcode;

    /**
     * 站址名称
     */
    private String stationName;

    /**
     * 5gr站址
     */
    private String stationcode5gr;
}