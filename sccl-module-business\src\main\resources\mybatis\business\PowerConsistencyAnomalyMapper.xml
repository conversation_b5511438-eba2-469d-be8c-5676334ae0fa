<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.PowerConsistencyAnomalyMapper">
    <resultMap type="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly" id="PowerConsistencyAnomalyResult">
        <id property="id" column="id"/>
        <result property="auditTime" column="audit_time"/>
        <result property="stationId" column="station_id"/>
        <result property="stationName" column="stationName"/>
        <result property="stationType" column="stationType"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="dataType" column="data_type"/>
        <result property="bzdbsl" column="bzdbsl"/>
        <result property="company" column="company"/>
        <result property="country" column="country"/>
        <result property="cityCode" column="cityCode"/>
        <result property="countyCode" column="countyCode"/>
        <result property="ywrjdl" column="ywrjdl"/>
        <result property="cwrjdl" column="cwrjdl"/>
        <result property="rate" column="rate"/>
        <result property="rateD" column="rateD"/>
        <result property="startdate" column="startdate"/>
        <result property="enddate" column="enddate"/>
        <result property="status" column="status"/>
        <result property="dispatchTime" column="dispatch_time"/>
        <result property="dispatchBy" column="dispatch_by"/>
        <result property="dispatchDesc" column="dispatch_desc"/>
        <result property="dispatchFj" column="dispatch_fj"/>
        <result property="doTime" column="do_time"/>
        <result property="doBy" column="do_by"/>
        <result property="doDesc" column="do_desc"/>
        <result property="doFj" column="do_fj"/>
        <result property="proCessId" column="proCessId"/>
        <result property="createdBy" column="createdBy"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="selectVo">
        select id,
            audit_time,
            station_id,
            stationName,
            stationType,
            `year`,
            `month`,
            data_type,
            bzdbsl,
            company,
            country,
            cityCode,
            countyCode,
            ywrjdl,
            cwrjdl,
            rate,
            rateD,
            startdate,
            enddate,
            status,
            dispatch_time,
            dispatch_by,
            dispatch_desc,
            dispatch_fj,
            do_time,
            do_by,
            do_desc,
            do_fj,
            proCessId,
            createdBy,
            createTime,
            updateTime,
            del_flag
        from power_consistency_anomaly
    </sql>

    <sql id="other-condition">
        <if test="id != null">and id = #{id}</if>
        <if test="auditTime != null">and audit_time = #{auditTime}</if>
        <if test="stationId != null">and station_id = #{stationId}</if>
        <if test="stationName != null">and stationName = #{stationName}</if>
        <if test="stationType != null">and stationType = #{stationType}</if>
        <if test="year != null">and `year` = #{year}</if>
        <if test="month != null">and `month` = #{month}</if>
        <if test="dataType != null">and data_type = #{dataType}</if>
        <if test="bzdbsl != null">and bzdbsl = #{bzdbsl}</if>
        <if test="company != null">and company = #{company}</if>
        <if test="country != null">and country = #{country}</if>
        <if test="cityCode != null">and cityCode = #{cityCode}</if>
        <if test="countyCode != null">and countyCode = #{countyCode}</if>
        <if test="ywrjdl != null">and ywrjdl = #{ywrjdl}</if>
        <if test="cwrjdl != null">and cwrjdl = #{cwrjdl}</if>
        <if test="rate != null">and rate = #{rate}</if>
        <if test="rateD != null">and rateD = #{rateD}</if>
        <if test="startdate != null">and startdate = #{startdate}</if>
        <if test="enddate != null">and enddate = #{enddate}</if>
        <if test="status != null">and status = #{status}</if>
        <if test="dispatchTime != null">and dispatch_time = #{dispatchTime}</if>
        <if test="dispatchBy != null">and dispatch_by = #{dispatchBy}</if>
        <if test="dispatchDesc != null">and dispatch_desc = #{dispatchDesc}</if>
        <if test="dispatchFj != null">and dispatch_fj = #{dispatchFj}</if>
        <if test="doTime != null">and do_time = #{doTime}</if>
        <if test="doBy != null">and do_by = #{doBy}</if>
        <if test="doDesc != null">and do_desc = #{doDesc}</if>
        <if test="doFj != null">and do_fj = #{doFj}</if>
        <if test="proCessId != null">and proCessId = #{proCessId}</if>
        <if test="createdBy != null and createdBy !=''">and createdBy = #{createdBy}</if>
        <if test="createTime != null">and createTime = #{createTime}</if>
        <if test="updateTime != null">and updateTime = #{updateTime}</if>
        <if test="delFlag != null">and del_flag = #{delFlag}</if>
    </sql>

    <sql id="like-condition">
        <if test="id != null">and id like concat('%', #{id}, '%')</if>
        <if test="auditTime != null">and audit_time like concat('%', #{auditTime}, '%')</if>
        <if test="stationId != null">and station_id like concat('%', #{stationId}, '%')</if>
        <if test="stationName != null">and stationName like concat('%', #{stationName}, '%')</if>
        <if test="stationType != null">and stationType like concat('%', #{stationType}, '%')</if>
        <if test="year != null">and `year` like concat('%', #{year}, '%')</if>
        <if test="month != null">and `month` like concat('%', #{month}, '%')</if>
        <if test="dataType != null">and data_type like concat('%', #{dataType}, '%')</if>
        <if test="bzdbsl != null">and bzdbsl like concat('%', #{bzdbsl}, '%')</if>
        <if test="company != null">and company like concat('%', #{company}, '%')</if>
        <if test="country != null">and country like concat('%', #{country}, '%')</if>
        <if test="cityCode != null">and cityCode like concat('%', #{cityCode}, '%')</if>
        <if test="countyCode != null">and countyCode like concat('%', #{countyCode}, '%')</if>
        <if test="ywrjdl != null">and ywrjdl like concat('%', #{ywrjdl}, '%')</if>
        <if test="cwrjdl != null">and cwrjdl like concat('%', #{cwrjdl}, '%')</if>
        <if test="rate != null">and rate like concat('%', #{rate}, '%')</if>
        <if test="rateD != null">and rateD like concat('%', #{rateD}, '%')</if>
        <if test="startdate != null">and startdate like concat('%', #{startdate}, '%')</if>
        <if test="enddate != null">and enddate like concat('%', #{enddate}, '%')</if>
        <if test="status != null">and status like concat('%', #{status}, '%')</if>
        <if test="dispatchTime != null">and dispatch_time like concat('%', #{dispatchTime}, '%')</if>
        <if test="dispatchBy != null">and dispatch_by like concat('%', #{dispatchBy}, '%')</if>
        <if test="dispatchDesc != null">and dispatch_desc like concat('%', #{dispatchDesc}, '%')</if>
        <if test="dispatchFj != null">and dispatch_fj like concat('%', #{dispatchFj}, '%')</if>
        <if test="doTime != null">and do_time like concat('%', #{doTime}, '%')</if>
        <if test="doBy != null">and do_by like concat('%', #{doBy}, '%')</if>
        <if test="doDesc != null">and do_desc like concat('%', #{doDesc}, '%')</if>
        <if test="doFj != null">and do_fj like concat('%', #{doFj}, '%')</if>
        <if test="proCessId != null">and proCessId like concat('%', #{proCessId}, '%')</if>
        <if test="createdBy != null and createdBy !=''">and createdBy like concat('%', #{createdBy}, '%')</if>
        <if test="createTime != null">and createTime like concat('%', #{createTime}, '%')</if>
        <if test="updateTime != null">and updateTime like concat('%', #{updateTime}, '%')</if>
        <if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
    </sql>


    <select id="selectByLike" parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly" resultMap="PowerConsistencyAnomalyResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="PowerConsistencyAnomalyResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="PowerConsistencyAnomalyResult">
        <include refid="selectVo"/>
        where del_flag = '0' and id = #{id}
    </select>

    <select id="count" parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly" resultType="Integer">
        select count(*) from power_consistency_anomaly
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>

    <select id="selectModle" resultMap="PowerConsistencyAnomalyResult">
        <include refid="selectVo"/>
        <where>
            del_flag = 0
            <include refid="other-condition"/>
        </where>
    </select>


    <insert id="insert" parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into power_consistency_anomaly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id, audit_time,station_id,stationName,stationType,`year`,`month`,
            data_type,bzdbsl,company,country,cityCode,countyCode,ywrjdl,cwrjdl,rate,rateD,startdate,enddate,status,dispatch_time,
            dispatch_by,dispatch_desc,dispatch_fj,do_time,do_by,do_desc,do_fj,proCessId,createdBy,
            createTime,updateTime,del_flag
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{auditTime},
            #{stationId},
            #{stationName},
            #{stationType},
            #{year},
            #{month},
            #{dataType},
            #{bzdbsl},
            #{company},
            #{country},
            #{cityCode},
            #{countyCode},
            #{ywrjdl},
            #{cwrjdl},
            #{rate},
            #{rateD},
            #{startdate},
            #{enddate},
            #{status},
            #{dispatchTime},
            #{dispatchBy},
            #{dispatchDesc},
            #{dispatchFj},
            #{doTime},
            #{doBy},
            #{doDesc},
            #{doFj},
            #{proCessId},
            #{createdBy},
            #{createTime},
            #{updateTime},
            #{delFlag}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into power_consistency_anomaly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            audit_time,
            station_id,
            stationName,
            stationType,
            `year`,
            `month`,
            data_type,
            bzdbsl,
            company,
            country,
            cityCode,
            countyCode,
            ywrjdl,
            cwrjdl,
            rate,
            rateD,
            startdate,
            enddate,
            status,
            dispatch_time,
            dispatch_by,
            dispatch_desc,
            dispatch_fj,
            do_time,
            do_by,
            do_desc,
            do_fj,
            proCessId,
            createdBy,
            createTime,
            updateTime,
            del_flag
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.auditTime},
                #{item.stationId},
                #{item.stationName},
                #{item.stationType},
                #{item.year},
                #{item.month},
                #{item.dataType},
                #{item.bzdbsl},
                #{item.company},
                #{item.country},
                #{item.cityCode},
                #{item.countyCode},
                #{item.ywrjdl},
                #{item.cwrjdl},
                #{item.rate},
                #{item.rateD},
                #{item.startdate},
                #{item.enddate},
                #{item.status},
                #{item.dispatchTime},
                #{item.dispatchBy},
                #{item.dispatchDesc},
                #{item.dispatchFj},
                #{item.doTime},
                #{item.doBy},
                #{item.doDesc},
                #{item.doFj},
                #{item.proCessId},
                #{item.createdBy},
                #{item.createTime},
                #{item.updateTime},
                #{item.delFlag}
            </trim>
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly">
        update power_consistency_anomaly
        <trim prefix="SET" suffixOverrides=",">
            audit_time = #{auditTime},
            station_id = #{stationId},
            stationName = #{stationName},
            stationType = #{stationType},
            `year` = #{year},
            `month` = #{month},
            data_type = #{dataType},
            bzdbsl = #{bzdbsl},
            company = #{company},
            country = #{country},
            cityCode = #{cityCode},
            countyCode = #{countyCode},
            ywrjdl = #{ywrjdl},
            cwrjdl = #{cwrjdl},
            rate = #{rate},
            rateD = #{rateD},
            startdate = #{startdate},
            enddate = #{enddate},
            status = #{status},
            dispatch_time = #{dispatchTime},
            dispatch_by = #{dispatchBy},
            dispatch_desc = #{dispatchDesc},
            dispatch_fj = #{dispatchFj},
            do_time = #{doTime},
            do_by = #{doBy},
            do_desc = #{doDesc},
            do_fj = #{doFj},
            proCessId = #{proCessId},
            createdBy = #{createdBy},
            createTime = #{createTime},
            updateTime = #{updateTime},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly">
        update power_consistency_anomaly
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="bzdbsl != null">bzdbsl = #{bzdbsl},</if>
            <if test="company != null">company = #{company},</if>
            <if test="country != null">country = #{country},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="ywrjdl != null">ywrjdl = #{ywrjdl},</if>
            <if test="cwrjdl != null">cwrjdl = #{cwrjdl},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="rateD != null">rateD = #{rateD},</if>
            <if test="startdate != null">startdate = #{startdate},</if>
            <if test="enddate != null">enddate = #{enddate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="dispatchTime != null">dispatch_time = #{dispatchTime},</if>
            <if test="dispatchBy != null">dispatch_by = #{dispatchBy},</if>
            <if test="dispatchDesc != null">dispatch_desc = #{dispatchDesc},</if>
            <if test="dispatchFj != null">dispatch_fj = #{dispatchFj},</if>
            <if test="doTime != null">do_time = #{doTime},</if>
            <if test="doBy != null">do_by = #{doBy},</if>
            <if test="doDesc != null">do_desc = #{doDesc},</if>
            <if test="doFj != null">do_fj = #{doFj},</if>
            <if test="proCessId != null">proCessId = #{proCessId},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModelBatch" parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly">
        update power_consistency_anomaly
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="bzdbsl != null">bzdbsl = #{bzdbsl},</if>
            <if test="company != null">company = #{company},</if>
            <if test="country != null">country = #{country},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="ywrjdl != null">ywrjdl = #{ywrjdl},</if>
            <if test="cwrjdl != null">cwrjdl = #{cwrjdl},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="rateD != null">rateD = #{rateD},</if>
            <if test="startdate != null">startdate = #{startdate},</if>
            <if test="enddate != null">enddate = #{enddate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="dispatchTime != null">dispatch_time = #{dispatchTime},</if>
            <if test="dispatchBy != null">dispatch_by = #{dispatchBy},</if>
            <if test="dispatchDesc != null">dispatch_desc = #{dispatchDesc},</if>
            <if test="dispatchFj != null">dispatch_fj = #{dispatchFj},</if>
            <if test="doTime != null">do_time = #{doTime},</if>
            <if test="doBy != null">do_by = #{doBy},</if>
            <if test="doDesc != null">do_desc = #{doDesc},</if>
            <if test="doFj != null">do_fj = #{doFj},</if>
            <if test="proCessId != null">proCessId = #{proCessId},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="revoke" parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly">
        update power_consistency_anomaly
        <trim prefix="SET" suffixOverrides=",">
            status = #{status},
            dispatch_time = #{dispatchTime},
            dispatch_by = #{dispatchBy},
            dispatch_desc = #{dispatchDesc},
            dispatch_fj = #{dispatchFj},
            updateTime = #{updateTime},
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE power_consistency_anomaly SET DEL_FLAG='1' where id = #{id}
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE power_consistency_anomaly SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from power_consistency_anomaly where id = #{id}
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from power_consistency_anomaly where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="delByAuditTime">
        delete from power_consistency_anomaly
        where audit_time = #{auditTime}
        and data_type = #{flag}
    </delete>
</mapper>