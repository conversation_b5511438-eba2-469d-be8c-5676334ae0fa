package com.sccl.httpforward.mapper;

import com.sccl.modules.mssaccount.mssinterface.domain.TowerAccountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报账单数据访问层
 * 
 * <AUTHOR>
 */
@Mapper
public interface BillMapper {
    
    /**
     * 根据报账单ID获取台账列表
     * @param billId 报账单ID
     * @return 台账列表
     */
    List<TowerAccountVo> getAccountListByBilId(@Param("billId") Long billId);
}