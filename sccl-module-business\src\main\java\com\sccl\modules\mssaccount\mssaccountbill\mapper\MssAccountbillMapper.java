package com.sccl.modules.mssaccount.mssaccountbill.mapper;

import com.enrising.dcarbon.audit.RefereeDatasource;
import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.noderesult.domain.NodeResult;
import com.sccl.modules.business.stationaudit.msshistory.MssHistory;
import com.sccl.modules.business.stationaudit.pavgpowertoolow.AvgPowerTooLowContent;
import com.sccl.modules.business.stationaudit.pcomparequtoa.quotaCompareContent;
import com.sccl.modules.business.stationaudit.powerhistory.PowerHistory;
import com.sccl.modules.business.stationaudit.pstationaccountchange.StationAccountChangeRefereeContent;
import com.sccl.modules.business.stationaudit.pstationchangesamemeter.StationChangeSameMeterRefereeContent;
import com.sccl.modules.business.stationaudit.pstationgrade.StationGradeExRefereeContent;
import com.sccl.modules.business.stationaudit.pstationmeterchangesamecode.StationMeterChangeSameCodeRefereeContent;
import com.sccl.modules.business.stationaudit.pstationpowerchange.StationPowerChangeRefereeContent;
import com.sccl.modules.business.stationaudit.pstationprotocolexpired.StationProtocolExpiredRefereeContent;
import com.sccl.modules.business.stationaudit.pstationstop.StaionStopRefreeContent;
import com.sccl.modules.mssaccount.dataanalysis.vo.ElectricityBillVO;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssinterface.domain.TowerAccountVo;
import com.sccl.modules.mssaccount.mssinterface.vo.CheckTowerVo;
import com.sccl.modules.mssaccount.mssinterface.vo.StationMssBussElectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报账 数据层
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@Mapper
public interface MssAccountbillMapper extends BaseMapper<MssAccountbill> {

    /**
     * 报账统计分析
     */
    List<ElectricityBillVO> statisticalAnalysisOfAccounting(@Param("year") Integer year, @Param("orgId") Long orgId);



    List<MssAccountbill> selectListByAuto(MssAccountbill mssAccountbill);

    //查询 挑对单
    List<MssAccountbill> selectListByCheck(MssAccountbill mssAccountbill);

    // 收款挑对
    List<MssAccountbill> selectListByCheckSK(MssAccountbill mssAccountbill);

    /**
     * 插入一条新的报账单，并不会自动生成ID，此时必须制定id字段
     *
     * @param mssAccountbill 包含ID的报账单对象
     * @return int
     * <AUTHOR> Yongxiang
     * @date 2021/12/22 16:40
     */
    int insertWithoutAutoId(MssAccountbill mssAccountbill);

    List<MssAccountbill> selectListByIdsAndType(@Param("ids") String[] ids, @Param("type") BigDecimal type);


    /**
     * @Description: 通过id数组查询对象集合
     * @author: dongk
     * @date: 2019/5/23
     * @param:
     * @return:
     */
    List<MssAccountbill> selectListByIds(String[] ids);

    /**
     * @Description: 同比环比分析统计
     * @author: lc
     * @date: 2019/5/23
     * @param:
     * @return:
     */
    List<Map<String, Object>> statisticalAnalysis(Map<String, Object> params);

    MssAccountbill getByOldOne(MssAccountbill m);

    String   getmssbasecodebyPre(String preid);

    /**
     * 根据 报账单--》报账站址--》站址对应的站址历史
     *
     * @param i
     * @param mssAccountbill
     * @return
     */
    List<MssHistory> getHistroy(@Param("stationCode") String stationCode, @Param("month") int month);

    /**
     * 根据报账单号 获取 stationId最大 的 基站站址编号
     *
     * @param mssAccountbill
     * @return
     */
    String getStationCode(@Param("bill") MssAccountbill mssAccountbill);

    /**
     * 根据基站站址编号 查询最近6个月的 站址电量历史
     *
     * @param stationCode
     * @param i
     * @return
     */
    List<PowerHistory> getHistroyForPower(@Param("stationCode") String stationCode, @Param("month") int month);

    /**
     * 获取最新两条 设备模型库
     * @return
     */

    /**
     * 获取当前报账单 对应台账 对应电表 其中 站址为铁塔的记录
     * @param accountBill
     * @return
     */

    /**
     * 获取 对应 电表的 上期铁塔账号
     * @param accountBill
     * @return
     */

    /**
     *         //根据报账单号 获取 stationId最大 的 基站站址编号
     * @param accountBill
     * @return
     */

    /**
     *         //根据基站站址编号 最近电表编号
     * @param diffSA
     * @return
     */

    /**
     * 根据基站站址编号 最近报账单
     *
     * @param stationCode
     * @param happenDate
     * @return
     */
    MssAccountbill getLastBill(@Param("stationCode") String stationCode, @Param("happendate") String happenDate);

    /**
     * 根据报账单 获取对应的 铁塔站址
     * @param accountBill
     * @return
     */

    /**
     * 根据报账单号 获取 stationId最大 的 基站站址编号 及 用电类型
     * @param accountBill
     * @return
     */

    /**
     * 插入 基站一站式稽核 站址 ->电表变动
     *
     * @param list
     * @return
     */
    int insertListDiffSA(@Param("list") List<RefereeDatasource> list);

    int insertListMsshistory(List<MssHistory> list);

    BigDecimal getPowerByMssId(@Param("MsId") Long id);

    /**
     * 获取报账单对应 过去6月台账信息
     *
     * @param i
     * @param billId
     * @return
     */
    List<NodeResult> getPowerHistory(@Param("billId") Long billId);

    /**
     * 根据 台账主键 获取对应的台账 信息
     *
     * @param pcid
     * @return
     */
    List<NodeResult> getPowerHistoryOne(@Param("pcid") Long pcid);

    /**
     * 获取 本次台账 对应站址 的 上一次台账信息
     *
     * @param stationcode
     * @param now
     * @return
     */
    NodeResult getPowerHistoryLast(@Param("stationCode") String stationcode, @Param("now") LocalDateTime now);

    /**
     * 获取本次台账 对应 同一电表的 上一次 站址信息
     *
     * @param ammeterid
     * @return
     */
    String getStationCodeLastSamemeter(@Param("ammeterid") Long ammeterid);

    /**
     * 当前台账的电表 对应 站址的上期 电表情况
     * @param stationcode
     * @param now
     * @return
     */


    /**
     * 站址不同 电表 差异
     *
     * @param stationcode
     * @param year
     * @param bizCode
     * @return
     */
    String getMeterLastSameStationCode(@Param("stationcode") String stationcode, @Param("pcid") Long pcid);

    List<StationGradeExRefereeContent> getPowerHistoryOneNew(@Param("pcid") Long pcid);

    List<StationPowerChangeRefereeContent> getPowerChagenNew(@Param("pcid") Long pcid);

    StationPowerChangeRefereeContent getPowerChagenNewLast(@Param("stationcode") String stationcode,
                                                           @Param("startdate") String startdate);

    List<StationAccountChangeRefereeContent> getAccountChange(@Param("pcid") Long pcid);

    StationAccountChangeRefereeContent getAccountChangeLast(@Param("stationcode") String stationcode,
                                                            @Param("now") String now);

    List<StationProtocolExpiredRefereeContent> getStationProtocol(@Param("pcid") Long pcid);

    List<StationChangeSameMeterRefereeContent> getStationChangeSameMeter(@Param("pcid") Long pcid);

    List<StationMeterChangeSameCodeRefereeContent> getMeterChangeSameCode(@Param("pcid") Long pcid);

    /**
     * 根据台账主键获取 对应的 起租单相关信息
     *
     * @param pcid
     * @return
     */
    StaionStopRefreeContent getStationStop(@Param("pcid") Long pcid);

    StaionStopRefreeContent getStationStop2(@Param("pcid") Long pcid);

    List<MssAccountbill> selectQuery(@Param("time") String time);

    quotaCompareContent getCompareQuota(@Param("pcid") Long pcid);

    String getElecType(@Param("pcid") Long pcid);

    AvgPowerTooLowContent getAvgPowerToolLow(@Param("pcid") Long pcid);

    boolean selectSendFalg1(@Param("billid") String billId);
    boolean selectSendFalg2(@Param("billid") String billId);

    List<MssAccountbill> selectNonElectricListByAuto(MssAccountbill mssAccountbill);

    int updatemeterdatesfortwoc(@Param("time") String time);
    int updatemeterinfo(@Param("budget") String time);

    MssAccountbill selectForSameCity(MssAccountbill mForSameCity);

    boolean verdictJZ(Accountbillitempre accountbillitempre);

    String getAmmeUseForPcid(@Param("pcid") Long accountId);

    int deleJtForBillId(@Param("billId") Long oldBillid, @Param("list") List<Long> accountIds);

    int deletePowerAuditByBillIds(@Param("list") String[] billIds);

    List<TowerAccountVo> getAccountListByBilId(@Param("billId") Long id);

    String getMssAccountbillStatusById(@Param("billId") Long billId);

    void updateMssAccountbillStatusById(@Param("billId") Long billid,@Param("status") String status);

    List<MssAccountbill> selectListByBudgetSetName(MssAccountbill mssAccountbill);

    List<HashMap<String, Object>> getAccountByMssBill(Long billId);

    List<HashMap<String, Object>> getBaseInfoByMssBill(Long billId);

    BigDecimal getAccountPrice(Long ammeterId);

    List<HashMap<String, Object>> getStationListByBillId(long billId);

    /**
     * 查询报账单内的局站财务日均电量及业务日均电量
     * @param billId 报账单id
     * @return
     */
    List<StationMssBussElectVo> getStationMssBuss(@Param("billId") long billId);

    /**
     * 检查账单报账电表是否在铁塔转供清单中
     * @param billId 报账单id
     * @return
     */
    List<CheckTowerVo> checkTower(@Param("billId") long billId);
}