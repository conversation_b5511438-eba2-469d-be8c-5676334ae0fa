package com.sccl.modules.business.cost.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 业财一致率稽核表 数据层
 * 
 * <AUTHOR>
 * @date 2024-11-01
 */
@Mapper
public interface PowerConsistencyAnomalyMapper extends BaseMapper<PowerConsistencyAnomaly>
{
    /**
     * 根据主键id数组批量更新
     * @param entity
     * @return
     */
    int updateForModelBatch(PowerConsistencyAnomaly entity);

    /**
     * 批量撤销
     * @param entity
     * @return
     */
    int revoke(PowerConsistencyAnomaly entity);

    /**
     * 根据统计时间及统计标识批量删除
     * @param auditTime 稽核时间
     * @param flag      统计标识 1 机楼 2基站
     * @return
     */
    int delByAuditTime(@Param("auditTime") String auditTime, @Param("flag") String flag);

}