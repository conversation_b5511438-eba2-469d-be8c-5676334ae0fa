package com.sccl.modules.business.cost.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.utils.ExportExcelUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.cost.service.IConsistencyAuditService;
import com.sccl.modules.business.cost.service.IConsistencyGkService;
import com.sccl.modules.business.cost.vo.*;
import com.sccl.modules.business.statinAudit.domain.SysOrganizations;
import com.sccl.modules.business.statinAudit.mapper.PowerStationInfoMapper;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 电量业财一致率稽核
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/business/cost/consistencyAudit")
@Slf4j
public class ConsistencyAuditController extends BaseController {

    @Autowired
    private IConsistencyAuditService consistencyAuditService;

    @Autowired
    private PowerStationInfoMapper stationInfoMapper;

    @Autowired
    private IConsistencyGkService consistencyGkService;

    @Value("${sccl.deployTo}")
    private String deployTo;

    /**
     * 电量业财一致率稽核 定时任务调用
     * @param auditTime 稽核时间【yyyy-MM】
     * @param flag      统计标识 1 机楼 2基站
     * @return
     */
    @GetMapping("/audit/{flag}")
    public AjaxResult audit(@RequestParam(required = false) String auditTime,
                            @PathVariable("flag") String flag) {
        String msg = "2".equals(flag) ? "基站" : "机楼";
        msg += "一致率稽核完成";
        boolean isSaveRecord = false;
        String inputDate = "";
        if (StringUtils.isBlank(auditTime)) {
            //统计当前月份及上月
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");
            LocalDate now = LocalDate.now();
            int i = 0;
            do {
                if (i == 0) {
                    //当月
                    auditTime = dtf.format(now);
                } else {
                    //上月
                    LocalDate last = now.minusMonths(i);
                    auditTime = dtf.format(last);
                    //判断是否保存稽核记录
                    DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    String day = dtf2.format(now); //当前日期
                    String firstDay = dtf.format(now) + "-01";//当前月份1号
                    if(day.equals(firstDay)){
                        //如果是当前月份的第一天，保存为上月末的执行记录
                        isSaveRecord = true;
                        //上月末
                        LocalDate lastDay = now.minusDays(1);
                        DateTimeFormatter dtf3 = DateTimeFormatter.ofPattern("yyyyMMdd");
                        inputDate = dtf3.format(lastDay);
                    }
                }
                consistencyAuditService.audit(auditTime, flag, isSaveRecord, inputDate);
                i = i + 1;
            } while (i < 2);
            msg += "，起始月份：" + auditTime;
        } else {
            isSaveRecord = true;
            DateTimeFormatter dtf3 = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate now = LocalDate.now();
            inputDate = dtf3.format(now);
            consistencyAuditService.audit(auditTime, flag, isSaveRecord, inputDate);
            msg += "，统计月份：" + auditTime;
        }
        return this.success(msg);
    }

    /**
     * 机楼/基站电量业财一致率
     * @return
     */
    @GetMapping("")
    public AjaxResult consistency(ConsistencyAuditSearchVo searchVo) {
        //查询参数处理
        setParam(searchVo);
        ConsistencyAuditVo result = consistencyAuditService.consistency(searchVo);
        Object object = JSONObject.toJSON(result);
        return this.success(object);
    }

    /**
     * 机楼/基站电量业财一致率一览
     */
    @GetMapping("/list")
    public TableDataInfo list(ConsistencyAuditSearchVo searchVo) {
        setParam(searchVo);//参数处理
        List<ConsistencyAuditResultVo> list = consistencyAuditService.list(searchVo);
        return getDataTable(list);
    }

    /**
     * 机楼/基站电量业财一致率一览 导出
     */
    @GetMapping("/export")
    public AjaxResult export(ConsistencyAuditSearchVo searchVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            setParam(searchVo);//参数处理
            List<ConsistencyAuditResultVo> list = consistencyAuditService.list(searchVo);
            InputStream inputStream = this.getClass().getResourceAsStream("/template/consistenceAuditList.xls");
            ExportExcelUtil exportExce = new ExportExcelUtil(inputStream);
            String[] fields = {"companyName",//所属部门
                    "cons1",              //1月-一致率
                    "seq1",               //1月-环比数
                    "com1",               //1月-整改完成率
                    "cons2",              //2月-一致率
                    "seq2",               //2月-环比数
                    "com2",               //2月-整改完成率
                    "cons3",              //3月-一致率
                    "seq3",               //3月-环比数
                    "com3",               //3月-整改完成率
                    "cons4",              //4月-一致率
                    "seq4",               //4月-环比数
                    "com4",               //4月-整改完成率
                    "cons5",              //5月-一致率
                    "seq5",               //5月-环比数
                    "com5",               //5月-整改完成率
                    "cons6",              //6月-一致率
                    "seq6",               //6月-环比数
                    "com6",               //6月-整改完成率
                    "cons7",              //7月-一致率
                    "seq7",               //7月-环比数
                    "com7",               //7月-整改完成率
                    "cons8",              //8月-一致率
                    "seq8",               //8月-环比数
                    "com8",               //8月-整改完成率
                    "cons9",              //9月-一致率
                    "seq9",               //9月-环比数
                    "com9",               //9月-整改完成率
                    "cons10",             //10月-一致率
                    "seq10",              //10月-环比数
                    "com10",              //10月-整改完成率
                    "cons11",             //11月-一致率
                    "seq11",              //11月-环比数
                    "com11",              //11月-整改完成率
                    "cons12",             //12月-一致率
                    "seq12",              //12月-环比数
                    "com12",              //12月-整改完成率
                    "avg",                //月均-一致率
                    "rank"                //排名
            };
            if (list != null) {
                exportExce.cteateTableByList(list, fields, 2);
            }
            // 标题设置
            String title = "";
            if (StringUtils.isNotBlank(searchVo.getCompany())) {
                SysOrganizations o = stationInfoMapper.selectsysOrgNameById(Long.valueOf(searchVo.getCompany()));
                if (o != null) {
                    title += o.getOrgName();
                }
            } else {
                title += "全省";
            }
            if (StringUtils.isNotBlank(searchVo.getCountry())) {
                SysOrganizations o = stationInfoMapper.selectsysOrgNameById(Long.valueOf(searchVo.getCountry()));
                if (o != null) {
                    title += o.getOrgName();
                }
            }
            if (searchVo.getYear() != null) {
                title = searchVo.getYear() + "年";
            }
            if("2".equals(searchVo.getFlag())){
                title += "基站";
            } else {
                title += "机楼";
            }
            title += "业财一致率";
            exportExce.getWb().setSheetName(0, title);
            return exportExce.outputExcel(title, request, response);
        } catch (Exception e) {
            return error("导出Excel失败，请联系网站管理员！");
        }
    }

    /**
     * 查询参数处理
     * @param searchVo
     */
    private void setParam(ConsistencyAuditSearchVo searchVo) {
        if (StringUtils.isBlank(searchVo.getYear())) {
            searchVo.setYear(DateUtils.getYear());
        }
        String preYear = (Integer.valueOf(searchVo.getYear()) - 1) + "";//去年
        searchVo.setPreYear(preYear);
        searchVo.setSeqStartYear(preYear + "-12");         //开始年月 上一期
        searchVo.setSeqEndYear(searchVo.getYear() + "-11");//结束年月 上一期

        //所属公司和所属部门处理
        if (StringUtils.isBlank(searchVo.getCompany()) || StringUtils.isBlank(searchVo.getCountry())) {
            User user = ShiroUtils.getUser();
            String admin = consistencyGkService.getAdmin(user);//判断登录用是省/市/区级
            searchVo.setAdmin(admin);
            if ("2".equals(admin) || "3".equals(admin)) {
                //市级
                if (StringUtils.isBlank(searchVo.getCompany())) {
                    List<IdNameVO> companies = user.getCompanies();
                    if (companies != null && companies.size() > 0) {
                        List<String> ids = new ArrayList<>();
                        for (IdNameVO company : companies) {
                            ids.add(company.getId());
                        }
                        searchVo.setCompanys(ids.toArray(new String[companies.size()]));
                    }
                }
                //区县级
                if ("3".equals(admin) && StringUtils.isBlank(searchVo.getCountry())) {
                    List<IdNameVO> countrys = user.getDepartments();
                    if (countrys != null && countrys.size() > 0) {
                        List<String> ids = new ArrayList<>();
                        for (IdNameVO country : countrys) {
                            ids.add(country.getId());
                        }
                        searchVo.setCountrys(ids.toArray(new String[countrys.size()]));
                    }
                }
            }
        }

        //数据部门处理
        String prov = "9999999991";//四川登录用户所属省份id
        if ("ln".equals(deployTo)) {
            prov = "2600000000";//辽宁登录用户所属省份id
        }
        searchVo.setProv(prov);
        if ("-1".equals(searchVo.getCompany()) || prov.equals(searchVo.getCompany())) {
            searchVo.setCompany("");
        }
    }

    /**
     * 详情
     * @return
     */
    @GetMapping("/details")
    public AjaxResult details(ConsistencyAuditXqSearchVo searchVo) {
        //查询参数处理
        setDetailsParam(searchVo);
        ConsistencyAuditXqVo result = consistencyAuditService.details(searchVo);
        Object object = JSONObject.toJSON(result);
        return this.success(object);
    }

    /**
     * 详情-详细信息列表
     */
    @GetMapping("/details/list")
    public TableDataInfo detailsList(ConsistencyAuditXqSearchVo searchVo) {
        setDetailsParam(searchVo);//参数处理
        startPage();
        List<ConsistencyAuditXqResultVo> list = consistencyAuditService.detailsList(searchVo);
        return getDataTable(list);
    }

    /**
     * 详情-详细信息列表 导出
     */
    @GetMapping("/details/export")
    public AjaxResult detailsExport(ConsistencyAuditXqSearchVo searchVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        setDetailsParam(searchVo);//参数处理
        startPage();
        List<ConsistencyAuditXqResultVo> list = consistencyAuditService.detailsList(searchVo);
        // 标题设置
        String title = "";
        if (StringUtils.isNotBlank(searchVo.getCompany())) {
            SysOrganizations o = stationInfoMapper.selectsysOrgNameById(Long.valueOf(searchVo.getCompany()));
            if (o != null) {
                title += o.getOrgName();
            }
        } else {
            title += "全省";
        }
        if (StringUtils.isNotBlank(searchVo.getCountry())) {
            SysOrganizations o = stationInfoMapper.selectsysOrgNameById(Long.valueOf(searchVo.getCountry()));
            if (o != null) {
                title += o.getOrgName();
            }
        }
        title += searchVo.getYearStr();
        title += "局站一致率";
        ExcelUtil<ConsistencyAuditXqResultVo> util = new ExcelUtil<>(ConsistencyAuditXqResultVo.class);
        return util.exportExcelToBrowser(response, list, title);
    }

    /**
     * 查询参数处理
     * @param searchVo
     */
    private void setDetailsParam(ConsistencyAuditXqSearchVo searchVo) {
        //所属公司和所属部门处理
        if (StringUtils.isBlank(searchVo.getCompany()) || StringUtils.isBlank(searchVo.getCountry())) {
            User user = ShiroUtils.getUser();
            String admin = consistencyGkService.getAdmin(user);//判断登录用是省/市/区级
            if ("2".equals(admin) || "3".equals(admin)) {
                //市级
                if (StringUtils.isBlank(searchVo.getCompany())) {
                    List<IdNameVO> companies = user.getCompanies();
                    if (companies != null && companies.size() > 0) {
                        List<String> ids = new ArrayList<>();
                        for (IdNameVO company : companies) {
                            ids.add(company.getId());
                        }
                        searchVo.setCompanys(ids.toArray(new String[companies.size()]));
                    }
                }
                //区县级
                if ("3".equals(admin) && StringUtils.isBlank(searchVo.getCountry())) {
                    List<IdNameVO> countrys = user.getDepartments();
                    if (countrys != null && countrys.size() > 0) {
                        List<String> ids = new ArrayList<>();
                        for (IdNameVO country : countrys) {
                            ids.add(country.getId());
                        }
                        searchVo.setCountrys(ids.toArray(new String[countrys.size()]));
                    }
                }
            }
        }

        //数据部门处理
        String prov = "9999999991";              //四川登录用户所属省份id
        if ("ln".equals(deployTo)) {
            prov = "2600000000";                 //辽宁登录用户所属省份id
        }
        if ("-1".equals(searchVo.getCompany()) || prov.equals(searchVo.getCompany())) {
            searchVo.setCompany("");
        }

        //年份
        if (StringUtils.isBlank(searchVo.getYear())) {
            searchVo.setYear(DateUtils.getYear());
        }
        String month = searchVo.getMonth();
        if (StringUtils.isBlank(searchVo.getMonth())) {
            month = "01";
            searchVo.setMonth("01");
            searchVo.setMonthNum(1);
        } else {
            month = String.format("%02d", Integer.valueOf(month));
            searchVo.setMonthNum(Integer.valueOf(month));
        }
        searchVo.setMonth(month);
        searchVo.setYearStr(searchVo.getYear() + "年" + month + "月");
        //月末
        Date last = DateUtils.getLastDayOfMonth(DateUtils.parseDate(searchVo.getYear() + month + "01"));
        searchVo.setEndMonth(DateUtils.formatDate(last, "yyyyMMdd"));
    }
}
