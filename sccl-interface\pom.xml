<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sccl</groupId>
        <artifactId>sccl-basic-frame</artifactId>
        <version>1.0</version>
    </parent>
    <artifactId>sccl-interface</artifactId>
    <version>1.0.0</version>
    <packaging>war</packaging>
    <name>接口服务入口</name>

    <properties>
        <start-class>com.sccl.Application</start-class>
        <!-- environment setting -->
        <java.version>1.8</java.version>
        <eclipse-plugin-download-sources>false</eclipse-plugin-download-sources>
        <eclipse-plugin-download-javadocs>false</eclipse-plugin-download-javadocs>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <axis.version>1.4</axis.version>
        <ctg-mq-version>2.7.5</ctg-mq-version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-activemq</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sccl</groupId>
            <artifactId>sccl-module-base</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>mongodb-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-data-mongodb</artifactId>
                    <groupId>org.springframework.data</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>
                        spring-boot-starter-data-mongodb
                    </artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-rabbit</artifactId>
                    <groupId>org.springframework.amqp</groupId>
                </exclusion>
                <!--                <exclusion>-->
                <!--                    <artifactId>log4j</artifactId>-->
                <!--                    <groupId>log4j</groupId>-->
                <!--                </exclusion>-->

                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <!--                <exclusion>-->
                <!--                    <artifactId>-->
                <!--                        spring-boot-starter-data-redis-->
                <!--                    </artifactId>-->
                <!--                    <groupId>org.springframework.boot</groupId>-->
                <!--                </exclusion>-->
                <exclusion>
                    <artifactId>spring-data-redis</artifactId>
                    <groupId>org.springframework.data</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-amqp</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <!--            <version>2.11.1</version>-->
        </dependency>

        <dependency>
            <groupId>io.github.hyxl520</groupId>
            <artifactId>auto-job-framework</artifactId>
            <version>0.9.6</version>
        </dependency>


        <!--5.1.47的驱动有个BUG,setTimestamp会报NullPointerException的异常-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.46</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.opencsv/opencsv -->
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>4.6</version>
        </dependency>


        <!--        <dependency>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                    <optional>true</optional>
                </dependency>-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.0.4.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.14</version>
            <scope>provided</scope>
        </dependency>

        <!-- axis2 webservice 依赖 -->
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
            <version>${axis.version}</version>
        </dependency>

        <!--============================================-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.17.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.17.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>2.17.0</version>
        </dependency>


        <dependency>
            <groupId>net.jodah</groupId>
            <artifactId>expiringmap</artifactId>
            <version>0.5.10</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <version>2.17.0</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.slf4j</groupId>-->
        <!--            <artifactId>log4j-over-slf4j</artifactId>-->
        <!--            <version>1.7.25</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>log4j</groupId>-->
        <!--            <artifactId>log4j</artifactId>-->
        <!--            <version>1.2.17</version>-->
        <!--        </dependency>-->
        <!--============================================-->

        <dependency>
            <groupId>commons-discovery</groupId>
            <artifactId>commons-discovery</artifactId>
            <version>0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-jaxrpc</artifactId>
            <version>${axis.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-saaj</artifactId>
            <version>${axis.version}</version>
        </dependency>
        <dependency>
            <groupId>wsdl4j</groupId>
            <artifactId>wsdl4j</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/commons-net/commons-net -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.8.0</version>
        </dependency>


        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.1.0</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.66</version>
        </dependency>
        <!--        <dependency>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-tcnative</artifactId>
                    <version>1.1.33.Fork22</version>
                </dependency>
                <dependency>
                    <groupId>com.ctg.itrdc.mq</groupId>
                    <artifactId>ctg-mq-api</artifactId>
                    <version>${ctg-mq-version}</version>
                </dependency>
                <dependency>
                    <groupId>com.ctg.mq</groupId>
                    <artifactId>ctg-mq-client</artifactId>
                    <version>${ctg-mq-version}</version>
                </dependency>
                <dependency>
                    <groupId>com.ctg.mq</groupId>
                    <artifactId>ctg-mq-common</artifactId>
                    <version>${ctg-mq-version}</version>
                </dependency>
                <dependency>
                    <groupId>com.ctg.mq</groupId>
                    <artifactId>ctg-mq-remoting</artifactId>
                    <version>${ctg-mq-version}</version>
                </dependency>
                <dependency>
                    <groupId>com.ctg.mq</groupId>
                    <artifactId>ctg-mq-tools</artifactId>
                    <version>${ctg-mq-version}</version>
                    <exclusions>
                        <exclusion>
                            <artifactId>commons-cli</artifactId>
                            <groupId>commons-cli</groupId>
                        </exclusion>
                    </exclusions>
                </dependency>-->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.3.70</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <!--        <dependency>
                    <groupId>com.sccl</groupId>
                    <artifactId>sccl-module-business</artifactId>
                    <version>1.0</version>
                    <scope>compile</scope>
                </dependency>-->
        <!--        <dependency>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                    <version>5.3.10</version>
                    <scope>compile</scope>
                </dependency>-->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.54</version>
        </dependency>
        <dependency>
            <groupId>com.enrising</groupId>
            <artifactId>sccl-biz-framework</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-stdlib</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>loc-ln</id>
            <properties>
                <spring.profiles.active>loc-ln</spring.profiles.active>
                <log.level>debug</log.level>
            </properties>
        </profile>
        <profile>
            <id>loc-sc</id>
            <properties>
                <spring.profiles.active>loc-sc</spring.profiles.active>
                <log.level>debug</log.level>
            </properties>
        </profile>
        <profile>
            <id>dev-ln</id>
            <properties>
                <spring.profiles.active>dev-ln</spring.profiles.active>
                <log.level>debug</log.level>
            </properties>
        </profile>
        <profile>
            <id>test-ln</id>
            <properties>
                <spring.profiles.active>test-ln</spring.profiles.active>
                <log.level>debug</log.level>
            </properties>
        </profile>
        <profile>
            <id>dev-sc</id>
            <properties>
                <spring.profiles.active>dev-sc</spring.profiles.active>
                <log.level>WARN</log.level>
            </properties>
        </profile>
        <!-- 辽宁 -->
        <profile>
            <id>ln</id>
            <properties>
                <spring.profiles.active>ln</spring.profiles.active>
                <log.level>debug</log.level>
            </properties>
        </profile>
        <!-- 四川 -->
        <profile>
            <id>sc</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>sc</spring.profiles.active>
                <log.level>WARN</log.level>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>energy-interface</finalName>
        <resources>
            <resource>
                <!-- 指定resources插件处理哪个目录下的资源文件 -->
                <directory>src/main/webapp</directory>
                <!--注意此次必须要放在此目录下才能被访问到 -->
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <packagingExcludes>
                        WEB-INF/lib/tomcat-embed-*.jar,
                        WEB-INF/lib/spring-boot-starter-tomcat-*.jar
                    </packagingExcludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
