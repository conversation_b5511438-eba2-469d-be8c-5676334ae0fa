<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolRecordMapper">

    <resultMap type="com.sccl.modules.business.ammeterorprotocol.domain.AmmeterorprotocolRecord" id="AmmeterorprotocolRecordResult">
        <id property="id" column="id"/>
        <id property="ammeterprotocolid" column="ammeterprotocolid"/>
        <result property="category" column="category"/>
        <result property="ammetername" column="ammetername"/>
        <result property="protocolname" column="protocolname"/>
        <result property="country" column="country"/>
        <result property="company" column="company"/>
        <result property="substation" column="substation"/>
        <result property="projectname" column="projectname"/>
        <result property="address" column="address"/>
        <result property="payname" column="payname"/>
        <result property="paytype" column="paytype"/>
        <result property="payperiod" column="payperiod"/>
        <result property="paymanager" column="paymanager"/>
        <result property="ammetermanager" column="ammetermanager"/>
        <result property="ammetertype" column="ammetertype"/>
        <result property="electrovalencenature" column="electrovalencenature"/>
        <result property="electronature" column="electronature"/>
        <result property="electrotype" column="electrotype"/>
        <result property="magnification" column="magnification"/>
        <result property="isStdStation" column="is_std_station"/>
        <result property="directsupplyflag" column="directsupplyflag"/>
        <result property="price" column="price"/>
        <result property="packagetype" column="packagetype"/>
        <result property="outeruser" column="outeruser"/>
        <result property="userunit" column="userunit"/>
        <result property="location" column="location"/>
        <result property="contractname" column="contractname"/>
        <result property="officephone" column="officephone"/>
        <result property="telephone" column="telephone"/>
        <result property="receiptaccountname" column="receiptaccountname"/>
        <result property="receiptaccountbank" column="receiptaccountbank"/>
        <result property="receiptaccounts" column="receiptaccounts"/>
        <result property="protocolscan" column="protocolscan"/>
        <result property="protocolsigneddate" column="protocolsigneddate"/>
        <result property="protocolterminatedate" column="protocolterminatedate"/>
        <result property="status" column="status"/>
        <result property="fee" column="fee"/>
        <result property="wastageFlag" column="wastage_flag"/>
        <result property="maxdegree" column="maxdegree"/>
        <result property="inputdate" column="inputdate"/>
        <result property="inputuser" column="inputuser"/>
        <result property="memo" column="memo"/>
        <result property="substationid" column="substationid"/>
        <result property="mapname" column="mapname"/>
        <result property="sourcetype" column="sourcetype"/>
        <result property="sourceinvoicetype" column="sourceinvoicetype"/>
        <result property="supplytype" column="supplytype"/>
        <result property="supplyinvoicetype" column="supplyinvoicetype"/>
        <result property="property" column="property"/>
        <result property="paymanagerTele" column="paymanager_tele"/>
        <result property="percent" column="percent"/>
        <result property="iprocessinstid" column="iprocessinstid"/>
        <result property="finishTime" column="finish_time"/>
        <result property="finishFlag" column="finish_flag"/>
        <result property="phyMac" column="phy_mac"/>
        <result property="phyName" column="phy_name"/>
        <result property="jtStationid" column="jt_stationid"/>
        <result property="jtDeviceid" column="jt_deviceid"/>
        <result property="jtMonth" column="jt_month"/>
        <result property="jtType" column="jt_type"/>
        <result property="jtHousecode" column="jt_housecode"/>
        <result property="jtStationname" column="jt_stationname"/>
        <result property="jtDevicename" column="jt_devicename"/>
        <result property="jtHousename" column="jt_housename"/>
        <result property="jtPhyMac" column="jt_phy_mac"/>
        <result property="contractOthPart" column="contract_oth_part"/>
        <result property="stationName" column="station_name"/>
        <result property="nmCcode" column="nm_ccode"/>
        <result property="nmL2100" column="nm_l2100"/>
        <result property="nmL1800" column="nm_l1800"/>
        <result property="nmCl800m" column="nm_cl800m"/>
        <result property="islumpsum" column="islumpsum"/>
        <result property="lumpstartdate" column="lumpstartdate"/>
        <result property="lumprrunum" column="lumprrunum"/>
        <result property="supplybureauammetercode" column="supplybureauammetercode"/>
        <result property="issmartammeter" column="issmartammeter"/>
        <result property="supplybureauname" column="supplybureauname"/>
        <result property="generationof" column="generationof"/>
        <result property="quotapowerratio" column="quotapowerratio"/>
        <result property="stationcode" column="stationcode"/>
        <result property="stationstatus" column="stationstatus"/>
        <result property="stationtype" column="stationtype"/>
        <result property="stationaddress" column="stationaddress"/>
        <result property="stationaddresscode" column="stationaddresscode"/>
        <result property="isairconditioning" column="isairconditioning"/>
        <result property="countryName" column="countryName"/>
        <result property="companyName" column="companyName"/>
        <result property="vouchelectricity" column="vouchelectricity"/>
        <result property="billStatus"            column="bill_status"            />
        <result property="ammeterprotocolid"            column="ammeterprotocolid"            />
        <result property="isAttach" column="is_attach"/>
        <result property="ammeteruse"               column="ammeteruse"                 />
        <result property="ammeterno"               column="ammeterno"                 />
        <result property="isentityammeter"               column="isentityammeter"                 />
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="parentId" column="parent_id"/>
        <result property="parentCode" column="parentCode"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customerName"/>
        <result property="creatorId"             column="creator_id"                 />
        <result property="updateById"             column="update_id"                 />
        <result property="creatorName"             column="creatorName"                 />
        <result property="updateByName"             column="updateName"                 />
        <result property="oldAmmeterId"             column="old_ammeter_id"                 />
        <result property="oldAmmeterName"             column="oldAmmeterName1"                 />
        <result property="ischangeammeter"             column="ischangeammeter"                 />
        <result property="oldBillPower"             column="old_bill_power"                 />
        <result property="ybgPower"             column="ybg_power"                 />
        <result property="lumpenddate"             column="lumpenddate"                 />
        <result property="iszgz"                      column="iszgz"                 />
        <result property="oldammetername"             column="oldammetername"                 />
        <result property="voltageClass" column="voltageClass"/>
        <result property="directFlag" column="iszg"/>
        <result property="officeFlag" column="isbg"/>
        <result property="transdistricompany" column="transdistricompany"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="demandId" column="demand_id"/>
        <result property="deliverId" column="deliver_id"/>
        <result property="startDegree" column="start_degree"/>
        <result property="oldDegree" column="old_degree"/>
        <result property="ammeterId" column="ammeter_id"/>
        <result property="lifnr" column="LIFNR"/>
        <result property="contractId" column="contract_id"/>
        <result property="priceType" column="price_type"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="floatingPrice" column="floating_price"/>
        <result property="includeTax" column="include_tax"/>
        <result property="ammeterIdNoPay" column="ammeterIdNoPay"/>
        <result property="transfercontractname" column="transfercontractname"/>
        <result property="transferPowerSupplyContractCode" column="transferPowerSupplyContractCode"/>
        <result property="transferPowerSupplyContractPrice" column="transferPowerSupplyContractPrice"/>
    </resultMap>

    <sql id="other-condition">
        <if test="id != null">and par.id = #{id}</if>
        <if test="category != null">and par.category = #{category}</if>
        <if test="ammetername != null">and par.ammetername = #{ammetername}</if>
        <if test="protocolname != null">and par.protocolname = #{protocolname}</if>
        <if test="country != null">and par.country = #{country}</if>
        <if test="company != null">and par.company = #{company}</if>
        <if test="substation != null">and par.substation = #{substation}</if>
        <if test="projectname != null">and par.projectname = #{projectname}</if>
        <if test="address != null">and par.address = #{address}</if>
        <if test="payname != null">and par.payname = #{payname}</if>
        <if test="paytype != null">and par.paytype = #{paytype}</if>
        <if test="payperiod != null">and par.payperiod = #{payperiod}</if>
        <if test="paymanager != null">and par.paymanager = #{paymanager}</if>
        <if test="ammetermanager != null">and par.ammetermanager = #{ammetermanager}</if>
        <if test="ammetertype != null">and par.ammetertype = #{ammetertype}</if>
        <if test="electrovalencenature != null">and par.electrovalencenature = #{electrovalencenature}</if>
        <if test="electronature != null">and par.electronature = #{electronature}</if>
        <if test="electrotype != null">and par.electrotype = #{electrotype}</if>
        <if test="magnification != null">and par.magnification = #{magnification}</if>
        <if test="isStdStation != null">and par.is_std_station = #{isStdStation}</if>
        <if test="directsupplyflag != null">and par.directsupplyflag = #{directsupplyflag}</if>
        <if test="price != null">and par.price = #{price}</if>
        <if test="packagetype != null">and par.packagetype = #{packagetype}</if>
        <if test="outeruser != null">and par.outeruser = #{outeruser}</if>
        <if test="userunit != null">and par.userunit = #{userunit}</if>
        <if test="location != null">and par.location = #{location}</if>
        <if test="contractname != null">and par.contractname = #{contractname}</if>
        <if test="officephone != null">and par.officephone = #{officephone}</if>
        <if test="telephone != null">and par.telephone = #{telephone}</if>
        <if test="receiptaccountname != null">and par.receiptaccountname = #{receiptaccountname}</if>
        <if test="receiptaccountbank != null">and par.receiptaccountbank = #{receiptaccountbank}</if>
        <if test="receiptaccounts != null">and par.receiptaccounts = #{receiptaccounts}</if>
        <if test="protocolscan != null">and par.protocolscan = #{protocolscan}</if>
        <if test="protocolsigneddate != null">and par.protocolsigneddate = #{protocolsigneddate}</if>
        <if test="protocolterminatedate != null">and par.protocolterminatedate = #{protocolterminatedate}</if>
        <if test="status != null">and par.status = #{status}</if>
        <if test="fee != null">and par.fee = #{fee}</if>
        <if test="wastageFlag != null">and par.wastage_flag = #{wastageFlag}</if>
        <if test="maxdegree != null">and par.maxdegree = #{maxdegree}</if>
        <if test="inputdate != null">and par.inputdate = #{inputdate}</if>
        <if test="inputuser != null">and par.inputuser = #{inputuser}</if>
        <if test="memo != null">and par.memo = #{memo}</if>
        <if test="substationid != null">and par.substationid = #{substationid}</if>
        <if test="mapname != null">and par.mapname = #{mapname}</if>
        <if test="sourcetype != null">and par.sourcetype = #{sourcetype}</if>
        <if test="sourceinvoicetype != null">and par.sourceinvoicetype = #{sourceinvoicetype}</if>
        <if test="supplytype != null">and par.supplytype = #{supplytype}</if>
        <if test="supplyinvoicetype != null">and par.supplyinvoicetype = #{supplyinvoicetype}</if>
        <if test="property != null">and par.property = #{property}</if>
        <if test="paymanagerTele != null">and par.paymanager_tele = #{paymanagerTele}</if>
        <if test="percent != null">and par.percent = #{percent}</if>
        <if test="iprocessinstid != null">and par.iprocessinstid = #{iprocessinstid}</if>
        <if test="finishTime != null">and par.finish_time = #{finishTime}</if>
        <if test="finishFlag != null">and par.finish_flag = #{finishFlag}</if>
        <if test="phyMac != null">and par.phy_mac = #{phyMac}</if>
        <if test="phyName != null">and par.phy_name = #{phyName}</if>
        <if test="jtStationid != null">and par.jt_stationid = #{jtStationid}</if>
        <if test="jtDeviceid != null">and par.jt_deviceid = #{jtDeviceid}</if>
        <if test="jtMonth != null">and par.jt_month = #{jtMonth}</if>
        <if test="jtType != null">and par.jt_type = #{jtType}</if>
        <if test="jtHousecode != null">and par.jt_housecode = #{jtHousecode}</if>
        <if test="jtStationname != null">and par.jt_stationname = #{jtStationname}</if>
        <if test="jtDevicename != null">and par.jt_devicename = #{jtDevicename}</if>
        <if test="jtHousename != null">and par.jt_housename = #{jtHousename}</if>
        <if test="jtPhyMac != null">and par.jt_phy_mac = #{jtPhyMac}</if>
        <if test="contractOthPart != null">and par.contract_oth_part = #{contractOthPart}</if>
        <if test="stationName != null">and par.station_name = #{stationName}</if>
        <if test="nmCcode != null">and par.nm_ccode = #{nmCcode}</if>
        <if test="nmL2100 != null">and par.nm_l2100 = #{nmL2100}</if>
        <if test="nmL1800 != null">and par.nm_l1800 = #{nmL1800}</if>
        <if test="nmCl800m != null">and par.nm_cl800m = #{nmCl800m}</if>
        <if test="islumpsum != null">and par.islumpsum = #{islumpsum}</if>
        <if test="lumpstartdate != null">and par.lumpstartdate = #{lumpstartdate}</if>
        <if test="lumprrunum != null">and par.lumprrunum = #{lumprrunum}</if>
        <if test="supplybureauammetercode != null">and par.supplybureauammetercode = #{supplybureauammetercode}</if>
        <if test="issmartammeter != null">and par.issmartammeter = #{issmartammeter}</if>
        <if test="supplybureauname != null">and par.supplybureauname = #{supplybureauname}</if>
        <if test="generationof != null">and par.generationof = #{generationof}</if>
        <if test="quotapowerratio != null">and par.quotapowerratio = #{quotapowerratio}</if>
        <if test="stationcode != null">and par.stationcode = #{stationcode}</if>
        <if test="stationstatus != null">and par.stationstatus = #{stationstatus}</if>
        <if test="stationtype != null">and par.stationtype = #{stationtype}</if>
        <if test="stationaddress != null">and par.stationaddress = #{stationaddress}</if>
        <if test="stationaddresscode != null">and par.stationaddresscode = #{stationaddresscode}</if>
        <if test="isairconditioning != null">and par.isairconditioning = #{isairconditioning}</if>
        <if test="vouchelectricity != null">and par.vouchelectricity = #{vouchelectricity}</if>
        <if test="billStatus != null"> and par.bill_status = #{billStatus}</if>
        <if test="isAttach != null"> and par.is_attach = #{isAttach}</if>
        <if test="ammeterprotocolid != null"> and par.ammeter_protocol_id = #{ammeterprotocolid}</if>
        <if test="ammeteruse != null"> and par.ammeteruse = #{ammeteruse}</if>
        <if test="ammeterno != null"> and par.ammeterno = #{ammeterno}</if>
        <if test="isentityammeter != null"> and par.isentityammeter = #{isentityammeter}</if>
        <if test="creatorId != null"> and par.creator_id = #{creatorId}</if>
        <if test="updateById != null"> and par.update_id = #{updateById}</if>
        <if test="oldAmmeterId != null"> and par.old_ammeter_id = #{oldAmmeterId}</if>
        <if test="ischangeammeter != null"> and par.ischangeammeter = #{ischangeammeter}</if>
        <if test="oldBillPower != null"> and par.old_bill_power = #{oldBillPower}</if>
        <if test="ybgPower != null">and par.ybg_power = #{ybgPower}</if>
        <if test="lumpenddate != null">and par.lumpenddate = #{lumpenddate}</if>
        <if test="delFlag != null">and par.del_flag = #{delFlag}</if>
        <if test="invoiceType != null and invoiceType != ''">and par.invoice_type = #{invoiceType}</if>
        <if test="demandId != null and demandId != ''">and par.demand_id = #{demandId}</if>
        <if test="deliverId != null and deliverId != ''">and par.deliver_id = #{deliverId}</if>
        <if test="ammeterId != null and ammeterId != ''">and par.ammeter_id = #{ammeterId}</if>
        <if test="lifnr != null and lifnr != ''">and par.LIFNR = #{lifnr}</if>
        <if test="contractId != null">and par.contract_id = #{contractId}</if>
    </sql>

    <sql id="selectVo">
        select
            par.id,
            par.category,
            par.ammetername,
            par.protocolname,
            par.country,
            par.company,
            par.substation,
            par.projectname,
            par.address,
            par.payname,
            par.paytype,
            par.payperiod,
            par.paymanager,
            par.ammetermanager,
            par.ammetertype,
            par.electrovalencenature,
            par.electronature,
            par.electrotype,
            par.magnification,
            par.is_std_station,
            par.directsupplyflag,
            par.price,
            par.packagetype,
            par.outeruser,
            par.userunit,
            par.location,
            par.contractname,
            par.officephone,
            par.telephone,
            par.receiptaccountname,
            par.receiptaccountbank,
            par.receiptaccounts,
            par.protocolscan,
            par.protocolsigneddate,
            par.protocolterminatedate,
            par.status,
            par.fee,
            par.wastage_flag,
            par.maxdegree,
            par.inputdate,
            par.inputuser,
            par.memo,
            par.substationid,
            par.mapname,
            par.sourcetype,
            par.sourceinvoicetype,
            par.supplytype,
            par.supplyinvoicetype,
            par.property,
            par.paymanager_tele,
            par.percent,
            par.iprocessinstid,
            par.finish_time,
            par.finish_flag,
            par.phy_mac,
            par.phy_name,
            par.jt_stationid,
            par.jt_deviceid,
            par.jt_month,
            par.jt_type,
            par.jt_housecode,
            par.jt_stationname,
            par.jt_devicename,
            par.jt_housename,
            par.jt_phy_mac,
            par.contract_oth_part,
            par.station_name,
            par.nm_ccode,
            par.nm_l2100,
            par.nm_l1800,
            par.nm_cl800m,
            par.islumpsum,
            par.lumpstartdate,
            par.lumprrunum,
            par.supplybureauammetercode,
            par.issmartammeter,
            par.supplybureauname,
            par.generationof,
            par.quotapowerratio,
            par.stationcode,
            par.stationstatus,
            par.stationtype,
            par.stationaddress,
            par.stationaddresscode,
            par.isairconditioning,
            par.vouchelectricity,
            par.bill_status,
            par.ammeteruse,
            par.ammeterno,
            par.isentityammeter,
            par.ammeter_protocol_id ammeterprotocolid,
            par.del_flag,
            par.parent_id,
            par.customer_id,
            par.creator_id,
            par.update_id,
            par.old_ammeter_id,
            par.ischangeammeter,
            par.old_bill_power,
            par.ybg_power,
            par.lumpenddate,
            par.iszgz,
            <if test="deployTo == 'sc'">
                psiv.stationcodeintid as stationcode5gr,
                psiv.stationname as stationname5gr,
            </if>
            par.oldammetername,
            par.create_time,
            par.voltageClass,
            par.iszg,
            par.isbg,
            par.transdistricompany,
            par.invoice_type,
            par.demand_id,
            par.deliver_id,
            par.start_degree,
            par.old_degree,
            par.ammeter_id,
            par.LIFNR as lifnr,
        par.contract_id,
        par.price_type,
        par.unit_price,
        par.floating_price,
        par.include_tax,
        par.ammeterIdNoPay
        from power_ammeterorprotocol_record par
        <if test="deployTo == 'sc'">
             LEFT JOIN power_station_info psi on par.stationcode=psi.id
             LEFT JOIN power_station_info_validity psiv on psiv.del_flag='0' and psiv.`status`='1' and psiv.stationcodeintid=psi.stationcodeintid
        </if>
    </sql>
    <sql id="selectVoObject">
        select
            papr.id,
            papr.category,
            papr.ammetername,
            papr.protocolname,
            papr.country,
            papr.company,
            papr.substation,
            papr.projectname,
            papr.address,
            papr.payname,
            papr.paytype,
            papr.payperiod,
            papr.paymanager,
            papr.ammetermanager,
            papr.ammetertype,
            papr.electrovalencenature,
            papr.electronature,
            papr.electrotype,
            papr.magnification,
            papr.is_std_station,
            papr.directsupplyflag,
            papr.price,
            papr.packagetype,
            papr.outeruser,
            papr.userunit,
            papr.location,
            papr.contractname,
            papr.officephone,
            papr.telephone,
            papr.receiptaccountname,
            papr.receiptaccountbank,
            papr.receiptaccounts,
            papr.protocolscan,
            papr.protocolsigneddate,
            papr.protocolterminatedate,
            papr.status,
            papr.fee,
            papr.wastage_flag,
            papr.maxdegree,
            papr.inputdate,
            papr.inputuser,
            papr.memo,
            papr.substationid,
            papr.mapname,
            papr.sourcetype,
            papr.sourceinvoicetype,
            papr.supplytype,
            papr.supplyinvoicetype,
            papr.property,
            papr.paymanager_tele,
            papr.percent,
            papr.iprocessinstid,
            papr.finish_time,
            papr.finish_flag,
            papr.phy_mac,
            papr.phy_name,
            papr.jt_stationid,
            papr.jt_deviceid,
            papr.jt_month,
            papr.jt_type,
            papr.jt_housecode,
            papr.jt_stationname,
            papr.jt_devicename,
            papr.jt_housename,
            papr.jt_phy_mac,
            papr.contract_oth_part,
            papr.station_name,
            papr.nm_ccode,
            papr.nm_l2100,
            papr.nm_l1800,
            papr.nm_cl800m,
            papr.islumpsum,
            papr.lumpstartdate,
            papr.lumprrunum,
            papr.supplybureauammetercode,
            papr.issmartammeter,
            papr.supplybureauname,
            papr.generationof,
            papr.quotapowerratio,
            papr.stationcode,
            papr.stationstatus,
            papr.stationtype,
            papr.stationaddress,
            papr.stationaddresscode,
            papr.isairconditioning,
            papr.vouchelectricity,
            papr.bill_status,
            papr.ammeteruse,
            papr.ammeterno,
            papr.isentityammeter,
            papr.ammeter_protocol_id ammeterprotocolid,
            papr.del_flag,
            papr.create_time,
            papr.parent_id,
            papr.creator_id,
            papr.update_id,
            papr.old_ammeter_id,
            papr.ischangeammeter,
            papr.old_bill_power,
            papr.ybg_power,
            papr.lumpenddate,
            papr.iszgz,
            <if test="deployTo == 'sc'">
                psiv.stationcodeintid as stationcode5gr,
                psiv.stationname as stationname5gr,
            </if>
            papr.oldammetername,
            papr.voltageClass,
            papr.iszg,
            papr.isbg,
            papr.transdistricompany,
            (select (case category when 1 then ammetername else protocolname end) from power_ammeterorprotocol where id=papr.old_ammeter_id limit 1) oldAmmeterName1,
            (select (case category when 1 then ammetername else protocolname end) from power_ammeterorprotocol where id=papr.parent_id limit 1) parentCode,
            papr.customer_id,
            (select name1 from mss_abccustomer where KUNNR=papr.customer_id and INF_STATUS =1 limit 1) customerName,
			proc.busi_alias busiAlias,
            proc.id processinstId,
            papr.transferPowerSupplyContractCode,papr.transferPowerSupplyContractPrice,papr.transfercontractname,
            papr.invoice_type,
            papr.demand_id,
            papr.deliver_id,
            papr.start_degree,
            papr.old_degree,
            papr.ammeter_id,
            papr.LIFNR as lifnr,
        papr.contract_id,
        papr.price_type,
        papr.unit_price,
        papr.floating_price,
        papr.include_tax,
        papr.ammeterIdNoPay
        from power_ammeterorprotocol_record papr
        left join (SELECT pi.id,pi.busi_key,pi.busi_alias FROM wf_proc_inst pi
            RIGHT JOIN (SELECT MAX(id) id,MAX(start_time) start_time FROM wf_proc_inst
            where busi_alias in ('MODIFY_PTC','ADD_PTC','ADD_AMM','MODIFY_AMM','AMM_SWITCH_AMM','PRO_SWITCH_AMM') GROUP BY busi_key) tmp
            ON pi.start_time = tmp.start_time and tmp.id=pi.id) proc ON proc.busi_key = papr.ammeter_protocol_id
        <if test="deployTo == 'sc'">
            LEFT JOIN power_station_info psi on papr.stationcode=psi.id
            LEFT JOIN power_station_info_validity psiv on psiv.del_flag='0' and psiv.`status`='1' and psiv.stationcodeintid=psi.stationcodeintid
        </if>
    </sql>


    <select id="getByAmmeProtId" resultMap="AmmeterorprotocolRecordResult">
        <include refid="selectVoObject"/>
        <where>
            papr.del_flag = '0'
            <if test="ammeterprotocolid != null"> and papr.ammeter_protocol_id =#{ammeterprotocolid}</if>
            <if test="creatorId != null"> and papr.creator_id =#{creatorId}</if>
            <if test="billStatus != null"> and papr.bill_status =#{billStatus}</if>
            <if test="type != null"> and (papr.bill_status =2 or papr.bill_status = 0) order by papr.bill_status desc,papr.create_time desc</if>
            <if test="type == null"> ORDER BY papr.create_time is null,papr.create_time DESC</if>
        </where>
    </select>
    <select id="getByAmmeProtIdOne" resultMap="AmmeterorprotocolRecordResult">
        select * from (
        <include refid="selectVoObject"/>
        <where>
            papr.del_flag = '0'
            <if test="ammeterprotocolid != null">and papr.ammeter_protocol_id =#{ammeterprotocolid}</if>
            <if test="creatorId != null">and papr.creator_id =#{creatorId}</if>
            <if test="billStatus != null">and papr.bill_status =#{billStatus}</if>
            <if test="type != null">and (papr.bill_status =2 or papr.bill_status = 0) order by papr.bill_status desc,papr.create_time desc
            </if>
            <if test="type == null">ORDER BY papr.create_time is null,papr.create_time DESC</if>
        </where>
        limit 1) temp
    </select>
    <select id="getByAmmeProtDateId" resultMap="AmmeterorprotocolRecordResult">
        <include refid="selectVoObject"/>
        <where>
            papr.del_flag = '0'
            <if test="createTime == null">
                and papr.create_time<![CDATA[>=]]>  (select max(create_time) from power_ammeterorprotocol_record
                where del_flag = '0'
                <if test="ammeterprotocolid != null"> and ammeter_protocol_id =#{ammeterprotocolid}</if>
                <if test="billStatus != null"> and bill_status =#{billStatus}</if>
                )
            </if>
            <if test="createTime != null">
                and papr.create_time<![CDATA[>=]]>#{createTime}
            </if>
            <if test="creatorId != null"> and papr.creator_id =#{creatorId}</if>
            <if test="ammeterprotocolid != null"> and papr.ammeter_protocol_id =#{ammeterprotocolid}</if>
             ORDER BY papr.create_time is null,papr.create_time DESC
        </where>
    </select>
    <select id="getByCreateTime" resultType="String">
        SELECT max( create_time ) FROM power_ammeterorprotocol_record
        WHERE del_flag = '0'
            <if test="ammeterprotocolid != null"> and ammeter_protocol_id =#{ammeterprotocolid}</if>
            <if test="billStatus != null"> and bill_status =#{billStatus}</if>
    </select>

    <!--  保存电表记录  -->
    <insert id="insert" parameterType="AmmeterorprotocolRecord" useGeneratedKeys="false" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            <if test="id == null">
                select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
            </if>
            <if test="id != null">
                select #{id} as id from dual
            </if>
        </selectKey>
        insert into power_ammeterorprotocol_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,ammeter_protocol_id,category,ammetername,protocolname,country,company,substation,projectname,address,payname,paytype,payperiod,paymanager,ammetermanager,ammetertype,electrovalencenature,electronature,electrotype,magnification,is_std_station,directsupplyflag,price,packagetype,outeruser,userunit,location,contractname,officephone,telephone,receiptaccountname,receiptaccountbank,receiptaccounts,protocolscan,protocolsigneddate,protocolterminatedate,status,fee,wastage_flag,maxdegree,inputdate,inputuser,memo,substationid,mapname,sourcetype,sourceinvoicetype,supplytype,supplyinvoicetype,property,paymanager_tele,percent,iprocessinstid,finish_time,finish_flag,phy_mac,phy_name,jt_stationid,jt_deviceid,jt_month,jt_type,jt_housecode,jt_stationname,jt_devicename,jt_housename,jt_phy_mac,contract_oth_part,station_name,nm_ccode,nm_l2100,nm_l1800,nm_cl800m,islumpsum,lumpstartdate,lumprrunum,supplybureauammetercode,issmartammeter,supplybureauname,generationof,quotapowerratio,stationcode,stationstatus,stationtype,stationaddress,stationaddresscode,isairconditioning,vouchelectricity,create_time,ammeteruse,ammeterno,isentityammeter,bill_status,del_flag,parent_id,customer_id,
            creator_id,update_id,old_ammeter_id,ischangeammeter,old_bill_power,ybg_power,lumpenddate,iszgz,oldammetername,voltageClass,iszg,isbg,transdistricompany,
            transferPowerSupplyContractCode,transferPowerSupplyContractPrice,transfercontractname,invoice_type,
            demand_id,deliver_id,start_degree,old_degree,ammeter_id,LIFNR,contract_id,price_type,unit_price,
            floating_price,include_tax,ammeterIdNoPay
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},#{ammeterprotocolid}, #{category}, #{ammetername}, #{protocolname}, #{country}, #{company}, #{substation}, #{projectname},
            #{address}, #{payname}, #{paytype}, #{payperiod}, #{paymanager}, #{ammetermanager}, #{ammetertype},
            #{electrovalencenature}, #{electronature}, #{electrotype}, #{magnification}, #{isStdStation},
            #{directsupplyflag}, #{price}, #{packagetype}, #{outeruser}, #{userunit}, #{location}, #{contractname},
            #{officephone}, #{telephone}, #{receiptaccountname}, #{receiptaccountbank}, #{receiptaccounts},
            #{protocolscan}, #{protocolsigneddate}, #{protocolterminatedate}, #{status}, #{fee}, #{wastageFlag},
            #{maxdegree}, #{inputdate}, #{inputuser}, #{memo}, #{substationid}, #{mapname}, #{sourcetype},
            #{sourceinvoicetype}, #{supplytype}, #{supplyinvoicetype}, #{property}, #{paymanagerTele}, #{percent},
            #{iprocessinstid}, #{finishTime}, #{finishFlag}, #{phyMac}, #{phyName}, #{jtStationid}, #{jtDeviceid},
            #{jtMonth}, #{jtType}, #{jtHousecode}, #{jtStationname}, #{jtDevicename}, #{jtHousename}, #{jtPhyMac},
            #{contractOthPart}, #{stationName}, #{nmCcode}, #{nmL2100}, #{nmL1800}, #{nmCl800m}, #{islumpsum},
            #{lumpstartdate}, #{lumprrunum},
            #{supplybureauammetercode},#{issmartammeter},#{supplybureauname},#{generationof},#{quotapowerratio},
            #{stationcode},#{stationstatus},#{stationtype},#{stationaddress},#{stationaddresscode},#{isairconditioning},
            #{vouchelectricity},(select SYSDATE() from dual)
            ,#{ammeteruse},#{ammeterno},#{isentityammeter},#{billStatus},'0',#{parentId},#{customerId},#{creatorId},
            #{updateById},#{oldAmmeterId},#{ischangeammeter},#{oldBillPower},#{ybgPower},#{lumpenddate},#{iszgz},#{oldammetername},#{voltageClass},#{directFlag},#{officeFlag},#{transdistricompany},
            #{transferPowerSupplyContractCode},#{transferPowerSupplyContractPrice},#{transfercontractname},
            #{invoiceType},#{demandId},#{deliverId},#{startDegree},#{oldDegree},#{ammeterId},#{lifnr},#{contractId},
            #{priceType},#{unitPrice},#{floatingPrice},#{includeTax},#{ammeterIdNoPay}
        </trim>
    </insert>

    <update id="updateForModel" parameterType="AmmeterorprotocolRecord">
        update power_ammeterorprotocol_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="ammeterprotocolid != null  ">ammeter_protocol_id = #{ammeterprotocolid},</if>
            <if test="category != null  ">category = #{category},</if>
            <if test="ammetername != null  and ammetername != ''  ">ammetername = #{ammetername},</if>
            <if test="protocolname != null  and protocolname != ''  ">protocolname = #{protocolname},</if>
            <if test="country != null  ">country = #{country},</if>
            <if test="company != null  ">company = #{company},</if>
            <if test="substation != null  and substation != ''  ">substation = #{substation},</if>
            <if test="projectname != null  and projectname != ''  ">projectname = #{projectname},</if>
            <if test="address != null  and address != ''  ">address = #{address},</if>
            <if test="payname != null  and payname != ''  ">payname = #{payname},</if>
            <if test="paytype != null  ">paytype = #{paytype},</if>
            <if test="payperiod != null  ">payperiod = #{payperiod},</if>
            <if test="paymanager != null  and paymanager != ''  ">paymanager = #{paymanager},</if>
            <if test="ammetermanager != null  and ammetermanager != ''  ">ammetermanager = #{ammetermanager},</if>
            <if test="ammetertype != null  ">ammetertype = #{ammetertype},</if>
            <if test="electrovalencenature != null  ">electrovalencenature = #{electrovalencenature},</if>
            <if test="electronature != null  ">electronature = #{electronature},</if>
            <if test="electrotype != null  ">electrotype = #{electrotype},</if>
            <if test="magnification != null  ">magnification = #{magnification},</if>
            <if test="isStdStation != null  ">is_std_station = #{isStdStation},</if>
            <if test="directsupplyflag != null  ">directsupplyflag = #{directsupplyflag},</if>
            <if test="price != null  ">price = #{price},</if>
            <if test="packagetype != null  ">packagetype = #{packagetype},</if>
            <if test="outeruser != null  and outeruser != ''  ">outeruser = #{outeruser},</if>
            <if test="userunit != null  and userunit != ''  ">userunit = #{userunit},</if>
            <if test="location != null  and location != ''  ">location = #{location},</if>
            <if test="contractname != null  and contractname != ''  ">contractname = #{contractname},</if>
            <if test="officephone != null  and officephone != ''  ">officephone = #{officephone},</if>
            <if test="telephone != null  and telephone != ''  ">telephone = #{telephone},</if>
            <if test="receiptaccountname != null  and receiptaccountname != ''  ">receiptaccountname =
                #{receiptaccountname},
            </if>
            <if test="receiptaccountbank != null  and receiptaccountbank != ''  ">receiptaccountbank =
                #{receiptaccountbank},
            </if>
            <if test="receiptaccounts != null  and receiptaccounts != ''  ">receiptaccounts = #{receiptaccounts},</if>
            <if test="protocolscan != null  and protocolscan != ''  ">protocolscan = #{protocolscan},</if>
            <if test="protocolsigneddate != null  ">protocolsigneddate = #{protocolsigneddate},</if>
            <if test="protocolterminatedate != null  ">protocolterminatedate = #{protocolterminatedate},</if>
            <if test="status != null  ">status = #{status},</if>
            <if test="fee != null  ">fee = #{fee},</if>
            <if test="wastageFlag != null  ">wastage_flag = #{wastageFlag},</if>
            <if test="maxdegree != null  ">maxdegree = #{maxdegree},</if>
            <if test="inputdate != null  ">inputdate = #{inputdate},</if>
            <if test="inputuser != null  ">inputuser = #{inputuser},</if>
            <if test="memo != null  and memo != ''  ">memo = #{memo},</if>
            <if test="substationid != null  ">substationid = #{substationid},</if>
            <if test="mapname != null  and mapname != ''  ">mapname = #{mapname},</if>
            <if test="sourcetype != null  ">sourcetype = #{sourcetype},</if>
            <if test="sourceinvoicetype != null  ">sourceinvoicetype = #{sourceinvoicetype},</if>
            <if test="supplytype != null  ">supplytype = #{supplytype},</if>
            <if test="supplyinvoicetype != null  ">supplyinvoicetype = #{supplyinvoicetype},</if>
            <if test="property != null  ">property = #{property},</if>
            <if test="paymanagerTele != null  and paymanagerTele != ''  ">paymanager_tele = #{paymanagerTele},</if>
            <if test="percent != null  ">percent = #{percent},</if>
            <if test="iprocessinstid != null  ">iprocessinstid = #{iprocessinstid},</if>
            <if test="finishTime != null  ">finish_time = #{finishTime},</if>
            <if test="finishFlag != null  ">finish_flag = #{finishFlag},</if>
            <if test="phyMac != null  and phyMac != ''  ">phy_mac = #{phyMac},</if>
            <if test="phyName != null  and phyName != ''  ">phy_name = #{phyName},</if>
            <if test="jtStationid != null  and jtStationid != ''  ">jt_stationid = #{jtStationid},</if>
            <if test="jtDeviceid != null  and jtDeviceid != ''  ">jt_deviceid = #{jtDeviceid},</if>
            <if test="jtMonth != null  ">jt_month = #{jtMonth},</if>
            <if test="jtType != null  ">jt_type = #{jtType},</if>
            <if test="jtHousecode != null  and jtHousecode != ''  ">jt_housecode = #{jtHousecode},</if>
            <if test="jtStationname != null  and jtStationname != ''  ">jt_stationname = #{jtStationname},</if>
            <if test="jtDevicename != null  and jtDevicename != ''  ">jt_devicename = #{jtDevicename},</if>
            <if test="jtHousename != null  and jtHousename != ''  ">jt_housename = #{jtHousename},</if>
            <if test="jtPhyMac != null  and jtPhyMac != ''  ">jt_phy_mac = #{jtPhyMac},</if>
            <if test="contractOthPart != null  and contractOthPart != ''  ">contract_oth_part = #{contractOthPart},</if>
            <if test="stationName != null  and stationName != ''  ">station_name = #{stationName},</if>
            <if test="nmCcode != null  and nmCcode != ''  ">nm_ccode = #{nmCcode},</if>
            <if test="nmL2100 != null  and nmL2100 != ''  ">nm_l2100 = #{nmL2100},</if>
            <if test="nmL1800 != null  and nmL1800 != ''  ">nm_l1800 = #{nmL1800},</if>
            <if test="nmCl800m != null  and nmCl800m != ''  ">nm_cl800m = #{nmCl800m},</if>
            <if test="islumpsum != null  ">islumpsum = #{islumpsum},</if>
            <if test="lumpstartdate != null  ">lumpstartdate = #{lumpstartdate},</if>
            <if test="lumprrunum != null  ">lumprrunum = #{lumprrunum},</if>
            <if test="supplybureauammetercode != null  ">supplybureauammetercode = #{supplybureauammetercode},</if>
            <if test="issmartammeter != null  ">issmartammeter = #{issmartammeter},</if>
            <if test="supplybureauname != null  ">supplybureauname = #{supplybureauname},</if>
            <if test="generationof != null  ">generationof = #{generationof},</if>
            <if test="quotapowerratio != null  ">quotapowerratio = #{quotapowerratio},</if>
            <if test="stationcode != null  ">stationcode = #{stationcode},</if>
            <if test="stationstatus != null  ">stationstatus = #{stationstatus},</if>
            <if test="stationtype != null  ">stationtype = #{stationtype},</if>
            <if test="stationaddress != null  ">stationaddress = #{stationaddress},</if>
            <if test="stationaddresscode != null  ">stationaddresscode = #{stationaddresscode},</if>
            <if test="isairconditioning != null  ">isairconditioning = #{isairconditioning},</if>
            <if test="vouchelectricity != null  ">vouchelectricity = #{vouchelectricity},</if>
            <if test="billStatus != null  ">bill_status = #{billStatus},</if>
            <if test="isAttach != null  ">is_attach = #{isAttach},</if>
            <if test="delFlag != null  ">del_flag = #{delFlag},</if>
            <if test="parentId != null  ">parent_id = #{parentId},</if>
            <if test="customerId != null  ">customer_id = #{customerId},</if>
            <if test="creatorId != null  ">creator_id = #{creatorId},</if>
            <if test="updateById != null  ">update_id = #{updateById},</if>
            <if test="oldAmmeterId != null"> old_ammeter_id = #{oldAmmeterId},</if>
            <if test="ischangeammeter != null"> ischangeammeter = #{ischangeammeter},</if>
            <if test="oldBillPower != null"> old_bill_power = #{oldBillPower},</if>
            <if test="ybgPower != null"> ybg_power = #{ybgPower},</if>
            <if test="lumpenddate != null"> lumpenddate = #{lumpenddate},</if>
            <if test="iszgz != null"> iszgz = #{iszgz},</if>
            <if test="oldammetername != null"> oldammetername = #{oldammetername},</if>
            <if test="voltageClass != null">voltageClass=#{voltageClass},</if>
            <if test="directFlag != null">iszg=#{directFlag},</if>
            <if test="officeFlag != null">isbg=#{officeFlag},</if>
            <if test="transdistricompany != null">transdistricompany=#{transdistricompany},</if>
            <if test="invoiceType != null">invoice_type=#{invoiceType},</if>
            <if test="demandId != null">demand_id=#{demandId},</if>
            <if test="deliverId != null">deliver_id=#{deliverId},</if>
            <if test="startDegree != null">start_degree = #{startDegree},</if>
            <if test="oldDegree != null">old_degree = #{oldDegree},</if>
            <if test="ammeterId != null">ammeter_id=#{ammeterId},</if>
            <if test="lifnr != null">LIFNR=#{lifnr},</if>
            <if test="contractId != null and contractId != ''">contract_id=#{contractId},</if>
            <if test="priceType != null">price_type=#{priceType},</if>
            <if test="unitPrice != null">unit_price=#{unitPrice},</if>
            <if test="floatingPrice != null">floating_price=#{floatingPrice},</if>
            <if test="includeTax != null">include_tax=#{includeTax},</if>
            <if test="ammeterIdNoPay != null">ammeterIdNoPay=#{ammeterIdNoPay},</if>
            <if test="transferPowerSupplyContractCode != null and transferPowerSupplyContractCode != ''">transferPowerSupplyContractCode=#{transferPowerSupplyContractCode},</if>
            <if test="transferPowerSupplyContractPrice != null and transferPowerSupplyContractPrice != ''">transferPowerSupplyContractPrice=#{transferPowerSupplyContractPrice},</if>
            <if test="transfercontractname != null and transfercontractname != ''">transfercontractname=#{transfercontractname},</if>
        </trim>
        where id = #{id}
    </update>
    <!--  根据条件修改数据  -->
    <update id="updateByCondition" parameterType="AmmeterorprotocolRecord">
        update power_ammeterorprotocol_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null  ">status = #{status},</if>
        </trim>
        where bill_status != 2 <if test="ammeterprotocolid != null"> and ammeter_protocol_id =#{ammeterprotocolid}</if>
    </update>
    <update id="updateCreatorId">
        update power_ammeterorprotocol_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="ammeterorprotocolRecord.creatorId != null  ">creator_id = #{ammeterorprotocolRecord.creatorId}</if>
        </trim>
        where ammeter_protocol_id = #{ammeterorprotocolRecord.ammeterprotocolid}
        and creator_id = -1
    </update>
    <sql id="selectVo1">
        select
            pap.id,
            pap.category,
            pap.ammetername,
            pap.protocolname,
            pap.country,
            pap.company,
            pap.substation,
            pap.projectname,
            pap.address,
            pap.payname,
            pap.paytype,
            pap.payperiod,
            pap.paymanager,
            pap.ammetermanager,
            pap.ammetertype,
            pap.electrovalencenature,
            pap.electronature,
            pap.electrotype,
            pap.magnification,
            pap.is_std_station,
            pap.directsupplyflag,
            pap.price,
            pap.packagetype,
            pap.outeruser,
            pap.userunit,
            pap.location,
            pap.contractname,
            pap.officephone,
            pap.telephone,
            pap.receiptaccountname,
            pap.receiptaccountbank,
            pap.receiptaccounts,
            pap.protocolscan,
            pap.protocolsigneddate,
            pap.protocolterminatedate,
            pap.status,
            pap.fee,
            pap.wastage_flag,
            pap.maxdegree,
            pap.inputdate,
            pap.inputuser,
            pap.memo,
            pap.substationid,
            pap.mapname,
            pap.sourcetype,
            pap.sourceinvoicetype,
            pap.supplytype,
            pap.supplyinvoicetype,
            pap.property,
            pap.paymanager_tele,
            pap.percent,
            pap.iprocessinstid,
            pap.finish_time,
            pap.finish_flag,
            pap.phy_mac,
            pap.phy_name,
            pap.jt_stationid,
            pap.jt_deviceid,
            pap.jt_month,
            pap.jt_type,
            pap.jt_housecode,
            pap.jt_stationname,
            pap.jt_devicename,
            pap.jt_housename,
            pap.jt_phy_mac,
            pap.contract_oth_part,
            pap.station_name,
            pap.nm_ccode,
            pap.nm_l2100,
            pap.nm_l1800,
            pap.nm_cl800m,
            pap.islumpsum,
            pap.lumpstartdate,
            pap.lumprrunum,
            pap.supplybureauammetercode,
            pap.issmartammeter,
            pap.supplybureauname,
            pap.generationof,
            pap.quotapowerratio,
            pap.stationcode,
            pap.stationstatus,
            pap.stationtype,
            pap.stationaddress,
            pap.stationaddresscode,
            pap.isairconditioning,
            pap.del_flag,
            pap.vouchelectricity,
            pap.bill_status,
            pap.ammeteruse,
            pap.ammeterno,
            pap.isentityammeter,
            pap.ammeter_protocol_id ammeterprotocolid,
            pec.type_name electrotypename,
            pap.create_time,
            pap.creator_id,
            pap.update_id,
            pap.old_ammeter_id,
            pap.ischangeammeter,
            pap.old_bill_power,
            pap.ybg_power,
            <if test="deployTo == 'sc'">
                psiv.stationcodeintid as stationcode5gr,
                psiv.stationname as stationname5gr,
            </if>
            pap.lumpenddate,
            pap.invoice_type,
            pap.demand_id,
            pap.deliver_id,
            pap.start_degree,
            pap.old_degree,
            pap.ammeter_id,
            pap.LIFNR as lifnr,
        pap.contract_id,
        pap.price_type,
        pap.unit_price,
        pap.floating_price,
        pap.include_tax,
        pap.ammeterIdNoPay
        from power_ammeterorprotocol_record pap
		LEFT JOIN power_electric_classification pec on pec.id=pap.electrotype
        <if test="deployTo == 'sc'">
            LEFT JOIN power_station_info psi on pap.stationcode=psi.id
            LEFT JOIN power_station_info_validity psiv on psiv.del_flag='0' and psiv.`status`='1' and psiv.stationcodeintid=psi.stationcodeintid
        </if>
    </sql>
    <select id="selectList" parameterType="AmmeterorprotocolRecord" resultMap="AmmeterorprotocolRecordResult">
        <include refid="selectVo"/>
        where 1=1
        <include refid="other-condition"/>
    </select>
    <select id="selectByPrimaryKey" parameterType="Map" resultMap="AmmeterorprotocolRecordResult">
        <include refid="selectVo1"/>
        where pap.del_flag = '0' and pap.id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </select>
    <select id="selectObjectBy" parameterType="Map" resultMap="AmmeterorprotocolRecordResult">
        SELECT
            papr.id,
            papr.ammeter_protocol_id ammeterprotocolid,
            papr.ammetername,
            papr.protocolname,
            papr.projectname,
            papr.ammeteruse,
            <if test="deployTo == 'sc'">
                psiv.stationcodeintid as stationcode5gr,
                psiv.stationname as stationname5gr,
            </if>
            papr.category
        FROM power_ammeterorprotocol_record papr
        RIGHT JOIN (
            SELECT max(id) id,MAX(create_time) create_time
            FROM power_ammeterorprotocol_record
            GROUP BY ammeter_protocol_id
        ) tmp ON papr.create_time = tmp.create_time and papr.id=tmp.id
        <if test="deployTo == 'sc'">
            LEFT JOIN power_station_info psi on papr.stationcode=psi.id
            LEFT JOIN power_station_info_validity psiv on psiv.del_flag='0' and psiv.`status`='1' and psiv.stationcodeintid=psi.stationcodeintid
        </if>
        where papr.del_flag != 1 and papr.status=1
        <if test="stationCode != null">
            and papr.stationcode = #{stationCode}
        </if>
        <if test="type == null || type== 0">
            and papr.category = 1
            <if test="name != null "> and papr.ammetername = #{name}</if>
            <if test="stationCode != null"> and papr.stationcode = #{stationCode}</if>
        </if>
        <if test="type== 1">
            and papr.category != 1
            <if test="name != null "> and papr.protocolname = #{name}</if>
        </if>
        <if test="projectName != null"> and papr.projectname = #{projectName}</if>
    </select>
    <select id="getUserByLoginId" resultType="com.sccl.modules.system.user.domain.User">
        select
            *
        from
        rmp.sys_user
        where login_id = #{loginId}
        and del_flag = '0'
        order by create_time desc limit 1
    </select>
    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from power_ammeterorprotocol_record where id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from power_ammeterorprotocol_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <!-- 删除协议id相关的数据 -->
    <delete id="deleteByAmmeterIdsDB" parameterType="String">
        delete from power_ammeterorprotocol_record where ammeter_protocol_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByDateDB" parameterType="Map">
        delete from power_ammeterorprotocol_record where bill_status != 2
        <if test="createTime != null">and create_time <![CDATA[>]]> #{createTime}</if>
        <if test="ammeterprotocolid != null">and ammeter_protocol_id = #{ammeterprotocolid}</if>
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </delete>

    <select id="getStatusByAmmeProtId" resultType="String">
        SELECT `status` FROM power_ammeterorprotocol_record
        WHERE del_flag = '0'
        and ammeter_protocol_id = #{ammeterprotocolid}
        ORDER BY create_time DESC LIMIT 0,1
    </select>
</mapper>
