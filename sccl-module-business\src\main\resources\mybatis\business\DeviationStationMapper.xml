<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.DeviationStationMapper">
    <resultMap type="com.sccl.modules.business.cost.domain.DeviationStation" id="DeviationStationResult">
        <id property="id" column="id"/>
        <result property="stationCode" column="stationCode"/>
        <result property="stationName" column="stationName"/>
        <result property="stationType" column="stationType"/>
        <result property="cityCode" column="cityCode"/>
        <result property="cityName" column="cityName"/>
        <result property="countyCode" column="countyCode"/>
        <result property="countyName" column="countyName"/>
        <result property="supplyWay" column="supplyWay"/>
        <result property="createdBy" column="createdBy"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectVo">
        select id,
            stationCode,
            stationName,
            stationType,
            cityCode,
            cityName,
            countyCode,
            countyName,
            supplyWay,
            createdBy,
            createTime,
            updateTime,
            del_flag
        from deviation_station
    </sql>

    <sql id="other-condition">
        <if test="id != null">and id = #{id}</if>
        <if test="stationCode != null">and stationCode = #{stationCode}</if>
        <if test="stationName != null">and stationName = #{stationName}</if>
        <if test="stationType != null">and stationType = #{stationType}</if>
        <if test="cityCode != null">and cityCode = #{cityCode}</if>
        <if test="cityName != null">and cityName = #{cityName}</if>
        <if test="countyCode != null">and countyCode = #{countyCode}</if>
        <if test="countyName != null">and countyName = #{countyName}</if>
        <if test="supplyWay != null">and supplyWay = #{supplyWay}</if>
        <if test="createdBy != null">and createdBy = #{createdBy}</if>
        <if test="createTime != null">and createTime = #{createTime}</if>
        <if test="updateTime != null">and updateTime = #{updateTime}</if>
        <if test="delFlag != null">and del_flag = #{delFlag}</if>
    </sql>

    <sql id="like-condition">
        <if test="id != null">and id like concat('%', #{id}, '%')</if>
        <if test="stationCode != null">and stationCode like concat('%', #{stationCode}, '%')</if>
        <if test="stationName != null">and stationName like concat('%', #{stationName}, '%')</if>
        <if test="stationType != null">and stationType like concat('%', #{stationType}, '%')</if>
        <if test="cityCode != null">and cityCode like concat('%', #{cityCode}, '%')</if>
        <if test="cityName != null">and cityName like concat('%', #{cityName}, '%')</if>
        <if test="countyCode != null">and countyCode like concat('%', #{countyCode}, '%')</if>
        <if test="countyName != null">and countyName like concat('%', #{countyName}, '%')</if>
        <if test="supplyWay != null">and supplyWay like concat('%', #{supplyWay}, '%')</if>
        <if test="createdBy != null">and createdBy like concat('%', #{createdBy}, '%')</if>
        <if test="createTime != null">and createTime like concat('%', #{createTime}, '%')</if>
        <if test="updateTime != null">and updateTime like concat('%', #{updateTime}, '%')</if>
        <if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
    </sql>


    <select id="selectByLike" parameterType="com.sccl.modules.business.cost.domain.DeviationStation" resultMap="DeviationStationResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="DeviationStationResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="DeviationStationResult">
        <include refid="selectVo"/>
        where del_flag = '0' and id = #{id}
    </select>

    <select id="count" parameterType="com.sccl.modules.business.cost.domain.DeviationStation" resultType="Integer">
        select count(*) from deviation_station
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>

    <select id="selectModle" resultMap="DeviationStationResult">
        <include refid="selectVo"/>
        <where>
            del_flag = 0
            <include refid="other-condition"/>
        </where>
    </select>


    <insert id="insert" parameterType="com.sccl.modules.business.cost.domain.DeviationStation" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into deviation_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,stationCode,stationName,stationType,cityCode,cityName,
            countyCode,countyName,supplyWay,
            createdBy,createTime,updateTime,del_flag
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{stationCode},
            #{stationName},
            #{stationType},
            #{cityCode},
            #{cityName},
            #{countyCode},
            #{countyName},
            #{supplyWay},
            #{createdBy},
            #{createTime},
            #{updateTime},
            #{delFlag}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into deviation_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            stationCode,
            stationName,
            stationType,
            cityCode,
            cityName,
            countyCode,
            countyName,
            supplyWay,
            createdBy,
            createTime,
            updateTime,
            del_flag
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.stationCode},
                #{item.stationName},
                #{item.stationType},
                #{item.cityCode},
                #{item.cityName},
                #{item.countyCode},
                #{item.countyName},
                #{item.supplyWay},
                #{item.createdBy},
                #{item.createTime},
                #{item.updateTime},
                #{item.delFlag}
            </trim>
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.sccl.modules.business.cost.domain.DeviationStation">
        update deviation_station
        <trim prefix="SET" suffixOverrides=",">
            stationCode = #{stationCode},
            stationName = #{stationName},
            stationType = #{stationType},
            cityCode = #{cityCode},
            cityName = #{cityName},
            countyCode = #{countyCode},
            countyName = #{countyName},
            supplyWay = #{supplyWay},
            createdBy = #{createdBy},
            createTime = #{createTime},
            updateTime = #{updateTime},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="com.sccl.modules.business.cost.domain.DeviationStation">
        update deviation_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="cityName != null">cityName = #{cityName},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="countyName != null">countyName = #{countyName},</if>
            <if test="supplyWay != null">supplyWay = #{supplyWay},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModelBatch" parameterType="com.sccl.modules.business.cost.domain.DeviationStation">
        update deviation_station
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="cityName != null">cityName = #{cityName},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="countyName != null">countyName = #{countyName},</if>
            <if test="supplyWay != null">supplyWay = #{supplyWay},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE deviation_station SET DEL_FLAG='1' where id = #{id}
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE deviation_station SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from deviation_station where id = #{id}
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from deviation_station where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="delAllDB">
        TRUNCATE TABLE deviation_station
    </delete>


    <insert id="audit" parameterType="com.sccl.modules.business.cost.vo.DeviationAuditVo">
        insert into deviation_station(stationCode,stationName,stationType,cityCode,cityName,countyCode,
        countyName,supplyWay,createdBy,createTime,updateTime,del_flag)
        select a.stationCode,
            a.stationName,
            trim(trailing '0' from a.stationType) stationType,
            a.cityCode,
            a.cityName,
            a.countyCode,
            a.countyName,
            a.energySupplyWay supplyWay,
            '-1' createdBy,
            #{createTime} createTime,
            #{updateTime} updateTime,
            '0' del_flag
        from meterinfo_all_jt a
        where a.status = 1
        and ifnull(a.del_flag,0) = 0
        group by a.cityCode,a.countyCode,a.stationCode
    </insert>
</mapper>