<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.ConsistencyAuditMapper">
    <select id="audit" resultType="com.sccl.modules.business.cost.vo.ConsistencyAuditTmpVo"
            parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly">
        select #{auditTime} auditTime,
               t.stationCode stationId,
               t.stationName,
               t.stationType,
               #{year} `year`,
               #{month} `month`,
               '1' dataType,
               t.bzdbsl,
               t.company,
               t.country,
               t.cityCode,
               t.countyCode,
               round(t.ywrjdl,2) ywrjdl,
               round(t.cwrjdl,2) cwrjdl,
               CONCAT(case when IFNULL(t.cwrjdl,0) = 0  then 0 else round(100*IFNULL(t.ywrjdl,0)/t.cwrjdl,2) end,'%') rate,
               (case when IFNULL(t.cwrjdl,0) = 0  then 0 else round(100*IFNULL(t.ywrjdl,0)/t.cwrjdl,2) end) rateD,
               t.startdate,
               t.enddate,
               9 status
        from (
            select a.company,
                a.country,
                a.cityCode,
                a.countyCode,
                a.stationCode,
                a.stationName,
                a.stationType,
                a.bzdbsl,
                a.cwrjdl,
                sum(c.energyData)/(DATEDIFF(a.enddate,a.startdate)+1) ywrjdl,
                a.startdate,
                a.enddate
            from(
                select a.company,
                    a.country,
                    a.cityCode,
                    a.countyCode,
                    a.stationCode,
                    a.stationName,
                    a.stationType,
                    min(a.start_date) startdate,
                    max(a.end_date) enddate,
                    count(distinct a.energy_meter_code) bzdbsl,
                    sum(a.cwrjdl)/count(distinct(a.writeoff_instance_code)) cwrjdl
                from(
                    select a.COMPANY_CODE company,
                        a.ORGID country,
                        f.cityCode,
                        f.countyCode,
                        f.stationCode,
                        f.stationName,
                        trim(trailing '0' from f.stationType) stationType,
                        round(sum(d.this_quantity_of_electricity)/sum(DATEDIFF(d.electricity_end_date,d.electricity_start_date)+1),2)as cwrjdl,
                        d.writeoff_instance_code,
                        d.energy_meter_code,
                        min(d.electricity_start_date) start_date,
                        max(d.electricity_end_date) end_date
                    from mss_accountbill a
                    inner join meter_info_db_bases b on b.billid = a.id
                    inner join (select max(c0.msg_id) mmsgid,c0.billid from meter_info_db_bases c0 group by c0.billid) c on c.mmsgid = b.msg_id and c.billid=b.billid
                    inner join writeoffinfodb d on b.billid=d.billid and b.energy_meter_code=d.energy_meter_code
                    inner join (select max(e0.msg_id) mmsgid,e0.billid from writeoffinfodb e0 group by e0.billid) e on d.msg_id=e.mmsgid and e.billid=d.billid
                    inner join meterinfo_all_jt f on f.energyMeterCode = d.energy_meter_code and f.stationType in('1110','1120','1130','1310','1320','1330','1210','1220')
                    and f.`status` = '1' and ifnull(f.del_flag,0) = 0
                    where a.PICKING_MODE in (1,7,9)
                    and a.`year`= #{year}
                    and a.BIZ_ENTRY_CODE = #{month}
                    group by d.energy_meter_code,d.writeoff_instance_code
                ) a group by a.countyCode,a.stationCode
            ) a
            left join fail_sync_collectmeter c on c.del_Flag = 0 and c.syncFlag = 1 and c.countyCode = a.countyCode
            and c.stationCode = a.stationcode and c.collectTime >= a.startdate and c.collectTime &lt;= a.enddate
            and not exists(select b.id from fail_sync_collectmeter b
                           where b.del_Flag = 0
                           and b.syncFlag = 1
                           and b.countyCode = c.countyCode
                           and b.stationCode = c.stationCode
                           and b.collectTime = c.collectTime
                           and b.id > c.id)
            group by a.countyCode,a.stationCode
        ) t
    </select>

    <select id="auditJz" resultType="com.sccl.modules.business.cost.vo.ConsistencyAuditTmpVo"
            parameterType="com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly">
        select #{auditTime} auditTime,
               t.stationCode stationId,
               t.stationName,
               t.stationType,
               #{year} `year`,
               #{month} `month`,
               '2' dataType,
               t.bzdbsl,
               t.company,
               t.country,
               t.cityCode,
               t.countyCode,
               round(t.ywrjdl,2) ywrjdl,
               round(t.cwrjdl,2) cwrjdl,
               CONCAT(case when IFNULL(t.cwrjdl,0) = 0  then 0 else round(100*IFNULL(t.ywrjdl,0)/t.cwrjdl,2) end,'%') rate,
               (case when IFNULL(t.cwrjdl,0) = 0  then 0 else round(100*IFNULL(t.ywrjdl,0)/t.cwrjdl,2) end) rateD,
               t.startdate,
               t.enddate,
               9 status
        from (
            select a.company,
                a.country,
                a.cityCode,
                a.countyCode,
                a.stationCode,
                a.stationName,
                a.stationType,
                a.bzdbsl,
                a.cwrjdl,
                sum(c.energyData)/(DATEDIFF(a.enddate,a.startdate)+1) ywrjdl,
                a.startdate,
                a.enddate
            from(
                select a.company,
                    a.country,
                    a.cityCode,
                    a.countyCode,
                    a.stationCode,
                    a.stationName,
                    a.stationType,
                    min(a.start_date) startdate,
                    max(a.end_date) enddate,
                    count(distinct a.energy_meter_code) bzdbsl,
                    sum(a.cwrjdl)/count(distinct(a.writeoff_instance_code)) cwrjdl
                from(
                    select a.COMPANY_CODE company,
                        a.ORGID country,
                        f.cityCode,
                        f.countyCode,
                        f.stationCode,
                        f.stationName,
                        trim(trailing '0' from f.stationType) stationType,
                        round(sum(d.this_quantity_of_electricity)/sum(DATEDIFF(d.electricity_end_date,d.electricity_start_date)+1),2)as cwrjdl,
                        d.writeoff_instance_code,
                        d.energy_meter_code,
                        min(d.electricity_start_date) start_date,
                        max(d.electricity_end_date) end_date
                    from mss_accountbill a
                    inner join meter_info_db_bases b on b.billid = a.id
                    inner join (select max(c0.msg_id) mmsgid,c0.billid from meter_info_db_bases c0 group by c0.billid) c on c.mmsgid = b.msg_id and c.billid=b.billid
                    inner join writeoffinfodb d on b.billid=d.billid and b.energy_meter_code=d.energy_meter_code
                    inner join (select max(e0.msg_id) mmsgid,e0.billid from writeoffinfodb e0 group by e0.billid) e on d.msg_id=e.mmsgid and e.billid=d.billid
                    inner join meterinfo_all_jt f on f.energyMeterCode = d.energy_meter_code and f.stationType in('1411','1412','1421','1422','1431','1432')
                    and f.`status` = '1' and ifnull(f.del_flag,0) = 0
                    where a.PICKING_MODE in (1,7,9)
                    and a.`year`= #{year}
                    and a.BIZ_ENTRY_CODE = #{month}
                    group by d.energy_meter_code,d.writeoff_instance_code
                ) a group by a.countyCode,a.stationCode
            ) a
            left join fail_sync_collectmeter c on c.del_Flag = 0 and c.syncFlag = 1 and c.countyCode = a.countyCode
            and c.stationCode = a.stationcode and c.collectTime >= a.startdate and c.collectTime &lt;= a.enddate
            and not exists(select b.id from fail_sync_collectmeter b
                           where b.del_Flag = 0
                           and b.syncFlag = 1
                           and b.countyCode = c.countyCode
                           and b.stationCode = c.stationCode
                           and b.collectTime = c.collectTime
                           and b.id > c.id)
            group by a.countyCode,a.stationCode
        ) t
    </select>

    <sql id="consistencyColumn">
        sum(case b.yf when '01' then round(100*b.consCnt/b.cnt,2) else null end) cons1,
        sum(case b.yf when '02' then round(100*b.consCnt/b.cnt,2) else null end) cons2,
        sum(case b.yf when '03' then round(100*b.consCnt/b.cnt,2) else null end) cons3,
        sum(case b.yf when '04' then round(100*b.consCnt/b.cnt,2) else null end) cons4,
        sum(case b.yf when '05' then round(100*b.consCnt/b.cnt,2) else null end) cons5,
        sum(case b.yf when '06' then round(100*b.consCnt/b.cnt,2) else null end) cons6,
        sum(case b.yf when '07' then round(100*b.consCnt/b.cnt,2) else null end) cons7,
        sum(case b.yf when '08' then round(100*b.consCnt/b.cnt,2) else null end) cons8,
        sum(case b.yf when '09' then round(100*b.consCnt/b.cnt,2) else null end) cons9,
        sum(case b.yf when '10' then round(100*b.consCnt/b.cnt,2) else null end) cons10,
        sum(case b.yf when '11' then round(100*b.consCnt/b.cnt,2) else null end) cons11,
        sum(case b.yf when '12' then round(100*b.consCnt/b.cnt,2) else null end) cons12
    </sql>
    <sql id="consistencyFromAndWhere">
        FROM power_consistency_anomaly a
        where a.del_Flag = '0'
        and a.`year` = #{year}
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="companys != null">
            and  a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
    </sql>
    <select id="consistency" resultType="com.sccl.modules.business.cost.vo.ConsistencyAuditVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyAuditSearchVo">
        select (case when b.cons1 is null and c.cons1 is null and d.cons1 is null then null else round((ifnull(b.cons1,0)/2 + ifnull(c.cons1,0)/4 + ifnull(d.cons1,0)/4),2) end) cons1,b.cons1 sta1,c.cons1 bld1,d.cons1 d1,
        (case when b.cons2 is null and c.cons2 is null and d.cons2 is null then null else round((ifnull(b.cons2,0)/2 + ifnull(c.cons2,0)/4 + ifnull(d.cons2,0)/4),2) end) cons2,b.cons2 sta2,c.cons2 bld2,d.cons2 d2,
        (case when b.cons3 is null and c.cons3 is null and d.cons3 is null then null else round((ifnull(b.cons3,0)/2 + ifnull(c.cons3,0)/4 + ifnull(d.cons3,0)/4),2) end) cons3,b.cons3 sta3,c.cons3 bld3,d.cons3 d3,
        (case when b.cons4 is null and c.cons4 is null and d.cons4 is null then null else round((ifnull(b.cons4,0)/2 + ifnull(c.cons4,0)/4 + ifnull(d.cons4,0)/4),2) end) cons4,b.cons4 sta4,c.cons4 bld4,d.cons4 d4,
        (case when b.cons5 is null and c.cons5 is null and d.cons5 is null then null else round((ifnull(b.cons5,0)/2 + ifnull(c.cons5,0)/4 + ifnull(d.cons5,0)/4),2) end) cons5,b.cons5 sta5,c.cons5 bld5,d.cons5 d5,
        (case when b.cons6 is null and c.cons6 is null and d.cons6 is null then null else round((ifnull(b.cons6,0)/2 + ifnull(c.cons6,0)/4 + ifnull(d.cons6,0)/4),2) end) cons6,b.cons6 sta6,c.cons6 bld6,d.cons6 d6,
        (case when b.cons7 is null and c.cons7 is null and d.cons7 is null then null else round((ifnull(b.cons7,0)/2 + ifnull(c.cons7,0)/4 + ifnull(d.cons7,0)/4),2) end) cons7,b.cons7 sta7,c.cons7 bld7,d.cons7 d7,
        (case when b.cons8 is null and c.cons8 is null and d.cons8 is null then null else round((ifnull(b.cons8,0)/2 + ifnull(c.cons8,0)/4 + ifnull(d.cons8,0)/4),2) end) cons8,b.cons8 sta8,c.cons8 bld8,d.cons8 d8,
        (case when b.cons9 is null and c.cons9 is null and d.cons9 is null then null else round((ifnull(b.cons9,0)/2 + ifnull(c.cons9,0)/4 + ifnull(d.cons9,0)/4),2) end) cons9,b.cons9 sta9,c.cons9 bld9,d.cons9 d9,
        (case when b.cons10 is null and c.cons10 is null and d.cons10 is null then null else round((ifnull(b.cons10,0)/2 + ifnull(c.cons10,0)/4 + ifnull(d.cons10,0)/4),2) end) cons10,b.cons10 sta10,c.cons10 bld10,d.cons10 d10,
        (case when b.cons11 is null and c.cons11 is null and d.cons11 is null then null else round((ifnull(b.cons11,0)/2 + ifnull(c.cons11,0)/4 + ifnull(d.cons11,0)/4),2) end) cons11,b.cons11 sta11,c.cons11 bld11,d.cons11 d11,
        (case when b.cons12 is null and c.cons12 is null and d.cons12 is null then null else round((ifnull(b.cons12,0)/2 + ifnull(c.cons12,0)/4 + ifnull(d.cons12,0)/4),2) end) cons12,b.cons12 sta12,c.cons12 bld12,d.cons12 d12
        from (select '0' id) a
        left join(select '0' company,
                    <include refid="consistencyColumn"/>
                  from(
                      select a.`month` yf,
                          count(1) cnt,
                          sum(case when a.rateD >= 80 and a.rateD &lt;= 120 then 1 else 0 end) consCnt
                      <include refid="consistencyFromAndWhere" />
                      and a.data_type = '2'
                      group by a.`month`
                  ) b
        ) b on b.company = a.id
        left join(select '0' company,
                    <include refid="consistencyColumn"/>
                  from(
                      select a.`month` yf,
                          count(1) cnt,
                          sum(case when a.rateD >= 90 and a.rateD &lt;= 110 then 1 else 0 end) consCnt
                      <include refid="consistencyFromAndWhere" />
                      and a.data_type = '1'
                      and a.stationType in (111,112,113,121,122)
                      group by a.`month`
                  ) b
        ) c on c.company = a.id
        left join(select '0' company,
                  <include refid="consistencyColumn"/>
                  from(
                      select a.`month` yf,
                      count(1) cnt,
                      sum(case when a.rateD >= 80 and a.rateD &lt;= 120 then 1 else 0 end) consCnt
                      <include refid="consistencyFromAndWhere" />
                      and a.data_type = '1'
                      and a.stationType in (131,132,133)
                      group by a.`month`
                  ) b
        ) d on d.company = a.id
    </select>

    <sql id="listColumn">
        round(sum(ROUND(100*b.consCnt/b.cnt,2))/count(b.yf),2) `avg`,
        sum(case b.yf when '01' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons1,
        sum(case b.yf when '01' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com1,
        sum(case b.yf when '02' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons2,
        sum(case b.yf when '02' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com2,
        sum(case b.yf when '03' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons3,
        sum(case b.yf when '03' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com3,
        sum(case b.yf when '04' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons4,
        sum(case b.yf when '04' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com4,
        sum(case b.yf when '05' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons5,
        sum(case b.yf when '05' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com5,
        sum(case b.yf when '06' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons6,
        sum(case b.yf when '06' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com6,
        sum(case b.yf when '07' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons7,
        sum(case b.yf when '07' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com7,
        sum(case b.yf when '08' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons8,
        sum(case b.yf when '08' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com8,
        sum(case b.yf when '09' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons9,
        sum(case b.yf when '09' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com9,
        sum(case b.yf when '10' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons10,
        sum(case b.yf when '10' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com10,
        sum(case b.yf when '11' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons11,
        sum(case b.yf when '11' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com11,
        sum(case b.yf when '12' then ROUND(100*b.consCnt/b.cnt,2) else null end) cons12,
        sum(case b.yf when '12' then ROUND(100*b.comCnt/(b.cnt-b.consCnt),2) else null end) com12
    </sql>
    <sql id="listColumnSeq">
        sum(case c.yf when '01' then ROUND(100*c.consCnt/c.cnt,2) else null end) seq1
    </sql>
    <sql id="listFromAndWhere">
        COUNT(1) cnt,
        <choose>
            <when test="flag == '2'.toString()">
                sum(case when a.rateD >= 80 and a.rateD &lt;= 120 then 1 else 0 end) consCnt,
            </when>
            <otherwise>
                sum(case when a.rateD >= 90 and a.rateD &lt;= 110 then 1 else 0 end) consCnt,
            </otherwise>
        </choose>
        sum(case a.`status` when 3 then 1 else 0 end) comCnt
        FROM power_consistency_anomaly a
        where a.del_Flag = '0'
        and a.data_type = #{flag}
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="companys != null">
            and  a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
    </sql>
    <select id="list" resultType="com.sccl.modules.business.cost.vo.ConsistencyAuditResultVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyAuditSearchVo">
        select t.company,t.companyName,
            ifnull(CONCAT(t.cons1,'%'),'——') cons1,
            ifnull(CONCAT(t.seq1,'%'),'——') seq1,
            ifnull(CONCAT(t.com1,'%'),'——') com1,
            ifnull(CONCAT(t.cons2,'%'),'——') cons2,
            ifnull(CONCAT(t.seq2,'%'),'——') seq2,
            ifnull(CONCAT(t.com2,'%'),'——') com2,
            ifnull(CONCAT(t.cons3,'%'),'——') cons3,
            ifnull(CONCAT(t.seq3,'%'),'——') seq3,
            ifnull(CONCAT(t.com3,'%'),'——') com3,
            ifnull(CONCAT(t.cons4,'%'),'——') cons4,
            ifnull(CONCAT(t.seq4,'%'),'——') seq4,
            ifnull(CONCAT(t.com4,'%'),'——') com4,
            ifnull(CONCAT(t.cons5,'%'),'——') cons5,
            ifnull(CONCAT(t.seq5,'%'),'——') seq5,
            ifnull(CONCAT(t.com5,'%'),'——') com5,
            ifnull(CONCAT(t.cons6,'%'),'——') cons6,
            ifnull(CONCAT(t.seq6,'%'),'——') seq6,
            ifnull(CONCAT(t.com6,'%'),'——') com6,
            ifnull(CONCAT(t.cons7,'%'),'——') cons7,
            ifnull(CONCAT(t.seq7,'%'),'——') seq7,
            ifnull(CONCAT(t.com7,'%'),'——') com7,
            ifnull(CONCAT(t.cons8,'%'),'——') cons8,
            ifnull(CONCAT(t.seq8,'%'),'——') seq8,
            ifnull(CONCAT(t.com8,'%'),'——') com8,
            ifnull(CONCAT(t.cons9,'%'),'——') cons9,
            ifnull(CONCAT(t.seq9,'%'),'——') seq9,
            ifnull(CONCAT(t.com9,'%'),'——') com9,
            ifnull(CONCAT(t.cons10,'%'),'——') cons10,
            ifnull(CONCAT(t.seq10,'%'),'——') seq10,
            ifnull(CONCAT(t.com10,'%'),'——') com10,
            ifnull(CONCAT(t.cons11,'%'),'——') cons11,
            ifnull(CONCAT(t.seq11,'%'),'——') seq11,
            ifnull(CONCAT(t.com11,'%'),'——') com11,
            ifnull(CONCAT(t.cons12,'%'),'——') cons12,
            ifnull(CONCAT(t.seq12,'%'),'——') seq12,
            ifnull(CONCAT(t.com12,'%'),'——') com12,
            ifnull(CONCAT(t.`avg`,'%'),'——') `avg`
        from(
            SELECT '' company,
                a.org_name companyName,
                '10' sort,
                b.`avg`,
                b.cons1,b.com1,c.seq1,
                b.cons2,b.com2,b.cons1 seq2,
                b.cons3,b.com3,b.cons2 seq3,
                b.cons4,b.com4,b.cons3 seq4,
                b.cons5,b.com5,b.cons4 seq5,
                b.cons6,b.com6,b.cons5 seq6,
                b.cons7,b.com7,b.cons6 seq7,
                b.cons8,b.com8,b.cons7 seq8,
                b.cons9,b.com9,b.cons8 seq9,
                b.cons10,b.com10,b.cons9 seq10,
                b.cons11,b.com11,b.cons10 seq11,
                b.cons12,b.com12,b.cons11 seq12
            FROM (select '0' id, '全省' org_name) a
            left join (
                select b.company,
                    <include refid="listColumn"/>
                from(
                    select '0' company,
                    a.`month` yf,
                    <include refid="listFromAndWhere"/>
                    and a.`year` = #{year}
                    GROUP BY a.`month`
                ) b group by b.company
            ) b on b.company = a.id
            left join (
                select '0' company,
                <include refid="listColumnSeq"/>
                from(
                    select '01' yf,
                        <include refid="listFromAndWhere"/>
                    and a.audit_time = #{seqStartYear}
                    GROUP BY a.`month`
                ) c
            ) c on c.company = a.id

            union all

            SELECT a.id company,
                a.org_name companyName,
                '11' sort,
                b.`avg`,
                b.cons1,b.com1,c.seq1,
                b.cons2,b.com2,b.cons1 seq2,
                b.cons3,b.com3,b.cons2 seq3,
                b.cons4,b.com4,b.cons3 seq4,
                b.cons5,b.com5,b.cons4 seq5,
                b.cons6,b.com6,b.cons5 seq6,
                b.cons7,b.com7,b.cons6 seq7,
                b.cons8,b.com8,b.cons7 seq8,
                b.cons9,b.com9,b.cons8 seq9,
                b.cons10,b.com10,b.cons9 seq10,
                b.cons11,b.com11,b.cons10 seq11,
                b.cons12,b.com12,b.cons11 seq12
            FROM (select a0.id,a0.org_name from rmp.sys_organizations a0 where a0.org_type = '1' and a0.parent_company_no = #{prov} and a0.del_flag = '0') a
            left join (
                select b.company,
                <include refid="listColumn"/>
                from(
                    select a.company,
                    a.`month` yf,
                    <include refid="listFromAndWhere"/>
                    and a.`year` = #{year}
                    group by a.company,a.`month`
                ) b group by b.company
            ) b on b.company = a.id
            left join (
                select c.company,
                <include refid="listColumnSeq"/>
                from(
                    select a.company,
                        '01' yf,
                    <include refid="listFromAndWhere"/>
                    and a.audit_time = #{seqStartYear}
                    group by a.company,a.`month`
                ) c group by c.company
            ) c on c.company = a.id
        ) t order by t.sort asc,t.`avg` desc
    </select>

    <select id="listCountry" resultType="com.sccl.modules.business.cost.vo.ConsistencyAuditResultVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyAuditSearchVo">
        select t.company,t.country,t.companyName,
        ifnull(CONCAT(t.cons1,'%'),'——') cons1,
        ifnull(CONCAT(t.seq1,'%'),'——') seq1,
        ifnull(CONCAT(t.com1,'%'),'——') com1,
        ifnull(CONCAT(t.cons2,'%'),'——') cons2,
        ifnull(CONCAT(t.seq2,'%'),'——') seq2,
        ifnull(CONCAT(t.com2,'%'),'——') com2,
        ifnull(CONCAT(t.cons3,'%'),'——') cons3,
        ifnull(CONCAT(t.seq3,'%'),'——') seq3,
        ifnull(CONCAT(t.com3,'%'),'——') com3,
        ifnull(CONCAT(t.cons4,'%'),'——') cons4,
        ifnull(CONCAT(t.seq4,'%'),'——') seq4,
        ifnull(CONCAT(t.com4,'%'),'——') com4,
        ifnull(CONCAT(t.cons5,'%'),'——') cons5,
        ifnull(CONCAT(t.seq5,'%'),'——') seq5,
        ifnull(CONCAT(t.com5,'%'),'——') com5,
        ifnull(CONCAT(t.cons6,'%'),'——') cons6,
        ifnull(CONCAT(t.seq6,'%'),'——') seq6,
        ifnull(CONCAT(t.com6,'%'),'——') com6,
        ifnull(CONCAT(t.cons7,'%'),'——') cons7,
        ifnull(CONCAT(t.seq7,'%'),'——') seq7,
        ifnull(CONCAT(t.com7,'%'),'——') com7,
        ifnull(CONCAT(t.cons8,'%'),'——') cons8,
        ifnull(CONCAT(t.seq8,'%'),'——') seq8,
        ifnull(CONCAT(t.com8,'%'),'——') com8,
        ifnull(CONCAT(t.cons9,'%'),'——') cons9,
        ifnull(CONCAT(t.seq9,'%'),'——') seq9,
        ifnull(CONCAT(t.com9,'%'),'——') com9,
        ifnull(CONCAT(t.cons10,'%'),'——') cons10,
        ifnull(CONCAT(t.seq10,'%'),'——') seq10,
        ifnull(CONCAT(t.com10,'%'),'——') com10,
        ifnull(CONCAT(t.cons11,'%'),'——') cons11,
        ifnull(CONCAT(t.seq11,'%'),'——') seq11,
        ifnull(CONCAT(t.com11,'%'),'——') com11,
        ifnull(CONCAT(t.cons12,'%'),'——') cons12,
        ifnull(CONCAT(t.seq12,'%'),'——') seq12,
        ifnull(CONCAT(t.com12,'%'),'——') com12,
        ifnull(CONCAT(t.`avg`,'%'),'——') `avg`
        from(
            <if test="admin != '3'.toString() and (country == null or country == '')">
                SELECT #{company} company,
                '' country,
                '全市' companyName,
                '10' sort,
                b.`avg`,
                b.cons1,b.com1,c.seq1,
                b.cons2,b.com2,b.cons1 seq2,
                b.cons3,b.com3,b.cons2 seq3,
                b.cons4,b.com4,b.cons3 seq4,
                b.cons5,b.com5,b.cons4 seq5,
                b.cons6,b.com6,b.cons5 seq6,
                b.cons7,b.com7,b.cons6 seq7,
                b.cons8,b.com8,b.cons7 seq8,
                b.cons9,b.com9,b.cons8 seq9,
                b.cons10,b.com10,b.cons9 seq10,
                b.cons11,b.com11,b.cons10 seq11,
                b.cons12,b.com12,b.cons11 seq12
                FROM (
                    select '0' country,
                    <include refid="listColumn"/>
                    from(
                        select a.`month` yf,
                        <include refid="listFromAndWhere"/>
                        and a.`year` = #{year}
                        GROUP BY a.`month`
                    ) b
                ) b
                left join (
                    select '0' country,
                    <include refid="listColumnSeq"/>
                    from(
                        select '01' yf,
                        <include refid="listFromAndWhere"/>
                        and a.audit_time = #{seqStartYear}
                        GROUP BY a.`month`
                    ) c
                ) c on c.country = b.country

                union all
            </if>

            SELECT b.company,
                b.country,
                concat(e.org_name,'-',d.org_name) companyName,
                '11' sort,
                b.`avg`,
                b.cons1,b.com1,c.seq1,
                b.cons2,b.com2,b.cons1 seq2,
                b.cons3,b.com3,b.cons2 seq3,
                b.cons4,b.com4,b.cons3 seq4,
                b.cons5,b.com5,b.cons4 seq5,
                b.cons6,b.com6,b.cons5 seq6,
                b.cons7,b.com7,b.cons6 seq7,
                b.cons8,b.com8,b.cons7 seq8,
                b.cons9,b.com9,b.cons8 seq9,
                b.cons10,b.com10,b.cons9 seq10,
                b.cons11,b.com11,b.cons10 seq11,
                b.cons12,b.com12,b.cons11 seq12
            FROM (
                select b.company,
                       b.country,
                <include refid="listColumn"/>
                from(
                    select a.company,
                           a.country,
                           a.`month` yf,
                    <include refid="listFromAndWhere"/>
                    and a.`year` = #{year}
                    group by a.company,a.country,a.`month`
                ) b group by b.company,b.country
            ) b
            left join (
                select c.company,
                       c.country,
                <include refid="listColumnSeq"/>
                from(
                    select a.company,
                    a.country,
                    '01' yf,
                    <include refid="listFromAndWhere"/>
                    and a.audit_time = #{seqStartYear}
                    group by a.company,a.country,a.`month`
                ) c group by c.company,c.country
            ) c on c.company = b.company and c.country = b.country
            left join rmp.sys_organizations d on d.id = b.country
            left join rmp.sys_organizations e on e.id = d.parent_company_no
        ) t order by t.sort asc,t.`avg` desc
    </select>

    <select id="details" resultType="com.sccl.modules.business.cost.vo.ConsistencyAuditXqVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyAuditXqSearchVo">
        SELECT sum(case t.yf when '01' then ROUND(100*t.consCnt/cnt,2) else null end) cons1,
            sum(case t.yf when '02' then ROUND(100*t.consCnt/cnt,2) else null end) cons2,
            sum(case t.yf when '03' then ROUND(100*t.consCnt/cnt,2) else null end) cons3,
            sum(case t.yf when '04' then ROUND(100*t.consCnt/cnt,2) else null end) cons4,
            sum(case t.yf when '05' then ROUND(100*t.consCnt/cnt,2) else null end) cons5,
            sum(case t.yf when '06' then ROUND(100*t.consCnt/cnt,2) else null end) cons6,
            sum(case t.yf when '07' then ROUND(100*t.consCnt/cnt,2) else null end) cons7,
            sum(case t.yf when '08' then ROUND(100*t.consCnt/cnt,2) else null end) cons8,
            sum(case t.yf when '09' then ROUND(100*t.consCnt/cnt,2) else null end) cons9,
            sum(case t.yf when '10' then ROUND(100*t.consCnt/cnt,2) else null end) cons10,
            sum(case t.yf when '11' then ROUND(100*t.consCnt/cnt,2) else null end) cons11,
            sum(case t.yf when '12' then ROUND(100*t.consCnt/cnt,2) else null end) cons12,
            sum(case t.yf when '01' then t.cnt else null end) cnt1,
            sum(case t.yf when '02' then t.cnt else null end) cnt2,
            sum(case t.yf when '03' then t.cnt else null end) cnt3,
            sum(case t.yf when '04' then t.cnt else null end) cnt4,
            sum(case t.yf when '05' then t.cnt else null end) cnt5,
            sum(case t.yf when '06' then t.cnt else null end) cnt6,
            sum(case t.yf when '07' then t.cnt else null end) cnt7,
            sum(case t.yf when '08' then t.cnt else null end) cnt8,
            sum(case t.yf when '09' then t.cnt else null end) cnt9,
            sum(case t.yf when '10' then t.cnt else null end) cnt10,
            sum(case t.yf when '11' then t.cnt else null end) cnt11,
            sum(case t.yf when '12' then t.cnt else null end) cnt12,
            sum(case t.yf when '01' then t.consCnt else null end) consCnt1,
            sum(case t.yf when '02' then t.consCnt else null end) consCnt2,
            sum(case t.yf when '03' then t.consCnt else null end) consCnt3,
            sum(case t.yf when '04' then t.consCnt else null end) consCnt4,
            sum(case t.yf when '05' then t.consCnt else null end) consCnt5,
            sum(case t.yf when '06' then t.consCnt else null end) consCnt6,
            sum(case t.yf when '07' then t.consCnt else null end) consCnt7,
            sum(case t.yf when '08' then t.consCnt else null end) consCnt8,
            sum(case t.yf when '09' then t.consCnt else null end) consCnt9,
            sum(case t.yf when '10' then t.consCnt else null end) consCnt10,
            sum(case t.yf when '11' then t.consCnt else null end) consCnt11,
            sum(case t.yf when '12' then t.consCnt else null end) consCnt12
        FROM(
            select a.`month` yf,
                COUNT(1) cnt,
                <choose>
                    <when test="flag == '2'.toString()">
                        sum(case when a.rateD >= 80 and a.rateD &lt;= 120 then 1 else 0 end) consCnt
                    </when>
                    <otherwise>
                        sum(case when a.rateD >= 90 and a.rateD &lt;= 110 then 1 else 0 end) consCnt
                    </otherwise>
                </choose>
            FROM power_consistency_anomaly a
            where a.del_Flag = '0'
            and a.`year` = #{year}
            and a.data_type = #{flag}
            <if test="company != null and company != ''">
                and a.company = #{company}
            </if>
            <if test="companys != null">
                and  a.company in
                <foreach item="company" collection="companys" open="(" separator="," close=")">
                    #{company}
                </foreach>
            </if>
            <if test="country != null and country != ''">
                and a.country = #{country}
            </if>
            <if test="countrys != null">
                and a.country in
                <foreach item="country" collection="countrys" open="(" separator="," close=")">
                    #{country}
                </foreach>
            </if>
            GROUP BY a.`month`
        ) t
    </select>

    <select id="detailsList" resultType="com.sccl.modules.business.cost.vo.ConsistencyAuditXqResultVo"
            parameterType="com.sccl.modules.business.cost.vo.ConsistencyAuditXqSearchVo">
        select a.stationName as station,
            a.station_id as resstationCode,
            (case when pec.id is null then ec.type_name else CONCAT(pec.type_name,'/',ec.type_name) end) stationType,
            (SELECT a0.org_name from rmp.sys_organizations a0 where a0.org_type = '1' and a0.id = a.company) companyName,
            (SELECT a0.org_name from rmp.sys_organizations a0 where a0.org_type = '2' and a0.id = a.country) countryName,
            a.startdate,
            a.enddate,
            a.ywrjdl,
            a.cwrjdl,
            a.rate,
            (case a.`status` when 3 then '是'
                             when 9 then (case a.data_type when '2' then (case when a.rateD >= 80 and a.rateD &lt;= 120 then '无需整改' else '否' end)
                                                           else (case when a.rateD >= 90 and a.rateD &lt;= 110 then '无需整改' else '否' end) end)
                             else '否' end) rect,
        (select a0.ywrjdl from power_consistency_anomaly_record a0
        where a0.del_flag = '0'
        and a0.`year` = #{year}
        and a0.`month` = #{month}
        and a0.inputDate = #{endMonth}
        and a0.data_type = #{flag}
        and a0.cityCode = a.cityCode
        and a0.countyCode = a.countyCode
        and a0.station_id = a.station_id
        order by a0.createTime desc limit 1) endMonth,
        (select a0.ywrjdl from power_consistency_anomaly_record a0
        where a0.del_flag = '0'
        and a0.`year` = #{year}
        and a0.`month` = #{month}
        and a0.data_type = #{flag}
        and a0.cityCode = a.cityCode
        and a0.countyCode = a.countyCode
        and a0.station_id = a.station_id
        order by a0.createTime desc limit 1) nextMonth
        from power_consistency_anomaly a
        left join power_electric_classification ec on ec.id = a.stationType
        left join power_electric_classification pec on pec.id = ec.parent_id
        where a.del_Flag = '0'
        and a.`year` = #{year}
        and a.`month` = #{month}
        and a.data_type = #{flag}
        <if test="company != null and company != ''">
            and a.company = #{company}
        </if>
        <if test="companys != null">
            and  a.company in
            <foreach item="company" collection="companys" open="(" separator="," close=")">
                #{company}
            </foreach>
        </if>
        <if test="country != null and country != ''">
            and a.country = #{country}
        </if>
        <if test="countrys != null">
            and a.country in
            <foreach item="country" collection="countrys" open="(" separator="," close=")">
                #{country}
            </foreach>
        </if>
        <if test="consFrom != null">
            and a.rateD >= #{consFrom}
        </if>
        <if test="consTo != null">
            and a.rateD &lt;= #{consTo}
        </if>
        <if test="key != null and key != ''">
            and a.stationName like concat('%', #{key}, '%')
        </if>
        <if test="resstationCode != null and resstationCode != ''">
            and a.station_id = #{resstationCode}
        </if>
        <if test="stationType != null and stationType != ''">
            and a.stationType = #{stationType}
        </if>
        ORDER BY a.rateD
    </select>
</mapper>