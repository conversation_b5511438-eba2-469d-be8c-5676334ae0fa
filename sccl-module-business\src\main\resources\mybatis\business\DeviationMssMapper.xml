<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.DeviationMssMapper">
    <resultMap type="com.sccl.modules.business.cost.domain.DeviationMss" id="DeviationMssResult">
        <id property="id" column="id"/>
        <result property="auditTime" column="audit_time"/>
        <result property="stationCode" column="stationCode"/>
        <result property="stationName" column="stationName"/>
        <result property="stationType" column="stationType"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="company" column="company"/>
        <result property="country" column="country"/>
        <result property="cityCode" column="cityCode"/>
        <result property="countyCode" column="countyCode"/>
        <result property="mssMoney" column="mssMoney"/>
        <result property="createdBy" column="createdBy"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectVo">
        select id,
            audit_time,
            stationCode,
            stationName,
            stationType,
            `year`,
            `month`,
            company,
            country,
            cityCode,
            countyCode,
            mssMoney,
            createdBy,
            createTime,
            updateTime,
            del_flag
        from deviation_mss
    </sql>

    <sql id="other-condition">
        <if test="id != null">and id = #{id}</if>
        <if test="auditTime != null">and audit_time = #{auditTime}</if>
        <if test="stationCode != null">and stationCode = #{stationCode}</if>
        <if test="stationName != null">and stationName = #{stationName}</if>
        <if test="stationType != null">and stationType = #{stationType}</if>
        <if test="year != null">and `year` = #{year}</if>
        <if test="month != null">and `month` = #{month}</if>
        <if test="company != null">and company = #{company}</if>
        <if test="country != null">and country = #{country}</if>
        <if test="cityCode != null">and cityCode = #{cityCode}</if>
        <if test="countyCode != null">and countyCode = #{countyCode}</if>
        <if test="mssMoney != null">and mssMoney = #{mssMoney}</if>
        <if test="createdBy != null">and createdBy = #{createdBy}</if>
        <if test="createTime != null">and createTime = #{createTime}</if>
        <if test="updateTime != null">and updateTime = #{updateTime}</if>
        <if test="delFlag != null">and del_flag = #{delFlag}</if>
    </sql>

    <sql id="like-condition">
        <if test="id != null">and id like concat('%', #{id}, '%')</if>
        <if test="auditTime != null">and audit_time like concat('%', #{auditTime}, '%')</if>
        <if test="stationCode != null">and stationCode like concat('%', #{stationCode}, '%')</if>
        <if test="stationName != null">and stationName like concat('%', #{stationName}, '%')</if>
        <if test="stationType != null">and stationType like concat('%', #{stationType}, '%')</if>
        <if test="year != null">and `year` like concat('%', #{year}, '%')</if>
        <if test="month != null">and `month` like concat('%', #{month}, '%')</if>
        <if test="company != null">and company like concat('%', #{company}, '%')</if>
        <if test="country != null">and country like concat('%', #{country}, '%')</if>
        <if test="cityCode != null">and cityCode like concat('%', #{cityCode}, '%')</if>
        <if test="countyCode != null">and countyCode like concat('%', #{countyCode}, '%')</if>
        <if test="mssMoney != null">and mssMoney like concat('%', #{mssMoney}, '%')</if>
        <if test="createdBy != null">and createdBy like concat('%', #{createdBy}, '%')</if>
        <if test="createTime != null">and createTime like concat('%', #{createTime}, '%')</if>
        <if test="updateTime != null">and updateTime like concat('%', #{updateTime}, '%')</if>
        <if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
    </sql>


    <select id="selectByLike" parameterType="com.sccl.modules.business.cost.domain.DeviationMss" resultMap="DeviationMssResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="DeviationMssResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="DeviationMssResult">
        <include refid="selectVo"/>
        where del_flag = '0' and id = #{id}
    </select>

    <select id="count" parameterType="com.sccl.modules.business.cost.domain.DeviationMss" resultType="Integer">
        select count(*) from deviation_mss
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>

    <select id="selectModle" resultMap="DeviationMssResult">
        <include refid="selectVo"/>
        <where>
            del_flag = 0
            <include refid="other-condition"/>
        </where>
    </select>


    <insert id="insert" parameterType="com.sccl.modules.business.cost.domain.DeviationMss" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into deviation_mss
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id, audit_time,stationCode,stationName,stationType,`year`,`month`,
            company,country,cityCode,countyCode,mssMoney,
            createdBy,createTime,updateTime,del_flag
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{auditTime},
            #{stationCode},
            #{stationName},
            #{stationType},
            #{year},
            #{month},
            #{company},
            #{country},
            #{cityCode},
            #{countyCode},
            #{mssMoney},
            #{createdBy},
            #{createTime},
            #{updateTime},
            #{delFlag}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into deviation_mss
        <trim prefix="(" suffix=")" suffixOverrides=",">
            audit_time,
            stationCode,
            stationName,
            stationType,
            `year`,
            `month`,
            company,
            country,
            cityCode,
            countyCode,
            mssMoney,
            createdBy,
            createTime,
            updateTime,
            del_flag
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.auditTime},
                #{item.stationCode},
                #{item.stationName},
                #{item.stationType},
                #{item.year},
                #{item.month},
                #{item.company},
                #{item.country},
                #{item.cityCode},
                #{item.countyCode},
                #{item.mssMoney},
                #{item.createdBy},
                #{item.createTime},
                #{item.updateTime},
                #{item.delFlag}
            </trim>
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.sccl.modules.business.cost.domain.DeviationMss">
        update deviation_mss
        <trim prefix="SET" suffixOverrides=",">
            audit_time = #{auditTime},
            stationCode = #{stationCode},
            stationName = #{stationName},
            stationType = #{stationType},
            `year` = #{year},
            `month` = #{month},
            company = #{company},
            country = #{country},
            cityCode = #{cityCode},
            countyCode = #{countyCode},
            mssMoney = #{mssMoney},
            createdBy = #{createdBy},
            createTime = #{createTime},
            updateTime = #{updateTime},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="com.sccl.modules.business.cost.domain.DeviationMss">
        update deviation_mss
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="company != null">company = #{company},</if>
            <if test="country != null">country = #{country},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="mssMoney != null">mssMoney = #{mssMoney},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModelBatch" parameterType="com.sccl.modules.business.cost.domain.DeviationMss">
        update deviation_mss
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="stationType != null">stationType = #{stationType},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="company != null">company = #{company},</if>
            <if test="country != null">country = #{country},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="mssMoney != null">mssMoney = #{mssMoney},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE deviation_mss SET DEL_FLAG='1' where id = #{id}
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE deviation_mss SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from deviation_mss where id = #{id}
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from deviation_mss where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="delByAuditTime">
        delete from deviation_mss
        where audit_time = #{auditTime}
    </delete>

    <insert id="audit" parameterType="com.sccl.modules.business.cost.vo.DeviationAuditVo">
        insert into deviation_mss(audit_time,stationCode,stationName,stationType,`year`,`month`,company,
        country,cityCode,countyCode,mssMoney,createdBy,createTime,updateTime,del_flag)
        select #{auditTime} audit_time,
            f.stationCode,
            f.stationName,
            trim(trailing '0' from f.stationType) stationType,
            #{year} `year`,
            #{month} `month`,
            a.COMPANY_CODE company,
            a.ORGID country,
            f.cityCode,
            f.countyCode,
            round(sum(ifnull(d.this_electricity_charge,0)-ifnull(d.this_electricity_tax,0)),4) as mssMoney,
            '-1' createdBy,
            #{createTime} createTime,
            #{updateTime} updateTime,
            '0' del_flag
        from mss_accountbill a
        inner join meter_info_db_bases b on b.billid = a.id
        inner join (select max(c0.msg_id) mmsgid,c0.billid from meter_info_db_bases c0 group by c0.billid) c on c.mmsgid = b.msg_id and c.billid=b.billid
        inner join writeoffinfodb d on b.billid=d.billid and b.energy_meter_code=d.energy_meter_code
        inner join (select max(e0.msg_id) mmsgid,e0.billid from writeoffinfodb e0 group by e0.billid) e on d.msg_id=e.mmsgid and e.billid=d.billid
        inner join meterinfo_all_jt f on f.energyMeterCode = d.energy_meter_code
        and f.`status` = '1' and ifnull(f.del_flag,0) = 0
        where a.PICKING_MODE in (1,7,9)
        and a.`year`= #{year}
        and a.BIZ_ENTRY_CODE &lt;= #{month}
        group by f.cityCode,f.countyCode,f.stationCode
    </insert>
</mapper>