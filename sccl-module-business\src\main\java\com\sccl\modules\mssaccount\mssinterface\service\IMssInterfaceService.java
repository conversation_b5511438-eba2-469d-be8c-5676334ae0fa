package com.sccl.modules.mssaccount.mssinterface.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo2;
import com.sccl.modules.mssaccount.mssinterface.domain.MssInterface;
import com.sccl.modules.mssaccount.mssinterface.domain.ViewOrgSapCostCenter;
import com.sccl.modules.uniflow.common.WFModel;

import java.util.List;
import java.util.Map;

/**
 * MSS财务接口 服务层
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
public interface IMssInterfaceService extends IBaseService<MssInterface>   {

//    //xml 解析
//    Map<String, Object> prasebillXML(String xml) throws Exception;
//
//    //xml 解析
//    Map<String, Object> praseReturnXML(String xml) throws Exception;

    //生成报账单
    void sendToMss(Long valueOf) throws Exception;
    void sendToMss2(Long valueOf) throws Exception;

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    public void uniflowCallBack(WFModel wfModel) throws Exception;

    // 新增报账单 查询报账人责任预算中心
    List<ViewOrgSapCostCenter> getFileorgCode(String hrLoginId) throws Exception;

    // 轮询 报账单接口 状态
    void getStausBybills();
    void getStausBybillsPro();

    // 轮询 报账单接口 状态
    void getStausBybills3();
    void getStausBybillsPro3();

    //送财辅失败的 重新送一次
    void sendToMssByError();

    //根据 财辅报账单 instanceCode 获取 sap凭证
    Map<String, Object> getSapMesByInstanceCode(String instanceCode) throws Exception;
    String syncEnergyMeterInfoscompany(Ammeterorprotocol ammeterorprotocol) throws Exception;

    void getStausBybillsSap();
    public String syncIncrementalMeter(List<? extends MeterInfo2> meterInfoByAmmeter) throws Exception;

    /**
     * 非电费流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
    public void nonElectricUniflowCallBack(WFModel wfModel) throws Exception;

    /**
     * 送财辅接口（非电费）
     * @param id
     * @throws Exception
     */
    void sendToMssNonelectric(Long id) throws Exception;

    void sendAccount(Long bilId);

    /**
     * 在网表计数据清单-新增申请-推送
     */
    void sendMeterInfo();

    /**
     * 业、财电量一致性稽核
     * @param billId 报账单id
     * @return
     */
    String checkBusMssCons(long billId);

    /**
     * 检查账单报账电表是否在铁塔转供清单中
     * @param billId 报账单id
     * @return
     */
    String checkTower(long billId);
}
