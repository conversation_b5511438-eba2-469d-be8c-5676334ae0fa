package com.sccl.modules.business.stationmss.service;

import com.sccl.framework.service.IBaseService;
import com.sccl.modules.business.stationmss.domain.StationMssMonth;

/**
 * 局站财务月日均电量表 服务层
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface IStationMssMonthService extends IBaseService<StationMssMonth>
{
    /**
     * 统计局站财务月日均电量表数据
     * @param tjsj 统计时间【yyyy-MM】
     */
    Integer statisticsAuto(String tjsj);

    /**
     * 同步局站财务日均电量到采集表
     * @param collectTime 采集时间【yyyyMMdd】
     */
    Integer doSyncCollectmeter(String collectTime);
}
