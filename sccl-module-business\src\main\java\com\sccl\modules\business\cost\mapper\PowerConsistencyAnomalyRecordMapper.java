package com.sccl.modules.business.cost.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.modules.business.cost.domain.PowerConsistencyAnomalyRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * 业财一致率稽核记录表 数据层
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Mapper
public interface PowerConsistencyAnomalyRecordMapper extends BaseMapper<PowerConsistencyAnomalyRecord>
{
    /**
     * 根据主键id数组批量更新
     * @param entity
     * @return
     */
    int updateForModelBatch(PowerConsistencyAnomalyRecord entity);
}