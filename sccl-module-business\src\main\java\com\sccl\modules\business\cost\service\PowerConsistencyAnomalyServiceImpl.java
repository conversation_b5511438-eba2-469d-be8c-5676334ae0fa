package com.sccl.modules.business.cost.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.business.cost.domain.PowerConsistencyAnomaly;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 业财一致率稽核表 服务层实现
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Service
@Slf4j
public class PowerConsistencyAnomalyServiceImpl extends BaseServiceImpl<PowerConsistencyAnomaly> implements IPowerConsistencyAnomalyService {

}
