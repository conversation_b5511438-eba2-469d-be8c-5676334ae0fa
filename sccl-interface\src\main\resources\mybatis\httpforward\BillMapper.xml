<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.httpforward.mapper.BillMapper">
    
    <select id="getAccountListByBilId" 
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.TowerAccountVo">
        SELECT
            a.toweraccountid,
            c.towerid,
            b.company,
            b.country,
            b.stationaddresscode,
            b.supplybureauammetercode,
            a.startdate,
            a.enddate,
            a.prevtotalreadings,
            a.curtotalreadings,
            a.multtimes,
            a.totalusedreadings,
            a.accountmoney,
            a.percent,
            a.taxamount,
            a.accountmoney-taxamount as price,
            c.city as towerCity,
            c.district as towerDistrict,
            c.towersitecode,
            c.payaccountnum,
            c.usestarttime,
            c.useendtime,
            c.usestartdegree,
            c.useenddegree,
            c.magnification,
            c.energyused,
            c.actualpay,
            c.apportionmentratio,
            c.telecomtax,
            c.telecomprice,
            (select writeoff_instance_code from mss_accountbill where ID = #{billId})  paymentOrderNumber
        FROM
            `power_account` a
                LEFT JOIN power_ammeterorprotocol b on a.ammeterid = b.id
                left join toweraccount c on a.toweraccountid = c.id
        WHERE a.pcid in (
            SELECT account_id FROM  mss_r_billitem_account  WHERE bill_id = #{billId}
        )
        and b.electrotype in (1411,1412)
    </select>
    
</mapper>