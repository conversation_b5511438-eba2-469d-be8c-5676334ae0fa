package com.sccl.httpforward;

import lombok.Data;


@Data
public class TowerAccountSendVo {

    private Long syncId;

    /**
     *  铁塔台账唯一ID
     */
    private String towerid;

    /**
     *  地市
     */
    private String city;

    /**
     *  区县
     */
    private String country;

    /**
     *  铁塔站址编码
     */
    private String towerSiteCode;

    /**
     *  缴费户号
     */
    private String paymentHouseholdID;

    /**
     *  支付单号
     */
    private String paymentOrderNumber;

    /**
     *  用电起时间
     */
    private String startTime;

    /**
     *  用电止时间
     */
    private String endTime;

    /**
     *  用电起度
     */
    private String startDegree;

    /**
     *  用电止度
     */
    private String endDegree;

    /**
     *  倍率
     */
    private String multiplier;

    /**
     *  总用电量
     */
    private String powerConsumption;

    /**
     *  实际支付电费
     */
    private String electricityBill;

    /**
     *  电信分摊比例
     */
    private String telecomAllocationRatio;

    /**
     *  缴费票据类型
     */
    private String paymentReceiptType;

    /**
     *  抄表照片
     */
    private String meterReadingPhoto;

    /**
     *  发票或收据
     */
    private String invoiceOrReceipt;

    /**
     *  分摊确认单
     */
    private String apportionmentConfirmation;

    /**
     *  电量明细清单
     */
    private String powerDetailList;

    /**
     *  电费分割单
     */
    private String electricityBillSplit;

    /**
     *  线损说明或合同截图
     */
    private String lineLossExplanationOrContract;

    /**
     *  税款
     */
    private String taxation;

    /**
     *  价款
     */
    private String price;

    /**
     *  流程状态
     */
    private String processStatus;

    /**
     *  电信备注
     */
    private String notes;

}
