package com.sccl.modules.mssaccount.mssinterface.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.sccl.common.lang.StringUtils;
import com.sccl.exception.BusinessException;
import com.sccl.framework.common.exception.base.BaseException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.web.domain.BaseEntity;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.autojob.util.convert.DateUtils;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.business.budgetreimbursementhistory.domain.BudgetReimbursementHistory;
import com.sccl.modules.business.budgetreimbursementhistory.mapper.BudgetReimbursementHistoryMapper;
import com.sccl.modules.business.meterInfoalljt.mapper.MeterinfoAllJtApplyMapper;
import com.sccl.modules.business.meterdatesfortwoc.domain.Meterdatesfortwoc;
import com.sccl.modules.business.meterdatesfortwoc.mapper.MeterdatesfortwocMapper;
import com.sccl.modules.business.meterinfo.domain.Meterinfo;
import com.sccl.modules.business.meterinfo.mapper.MeterinfoMapper;
import com.sccl.modules.business.meterotherdatesfortwoc.domain.MeterOtherDatesfortwoc;
import com.sccl.modules.business.meterotherdatesfortwoc.mapper.MeterOtherDatesfortwocMapper;
import com.sccl.modules.business.meterpollutiondatesfortwoc.domain.MeterPollutionDatesfortwoc;
import com.sccl.modules.business.meterpollutiondatesfortwoc.mapper.MeterPollutionDatesfortwocMapper;
import com.sccl.modules.business.mssaccountprepaid.domain.MssAccountPrepaid;
import com.sccl.modules.business.mssaccountprepaid.service.IMssAccountPrepaidService;
import com.sccl.modules.business.stationinfo.domain.StationInfo;
import com.sccl.modules.business.syncresult.mapper.SyncresultMapper;
import com.sccl.modules.business.twoc.domain.TwoCFlag;
import com.sccl.modules.business.twoc.domain.Twoc;
import com.sccl.modules.business.twoc.mapper.TwocMapper;
import com.sccl.modules.monitor.operlog.domain.OperLog;
import com.sccl.modules.monitor.operlog.mapper.OperLogMapper;
import com.sccl.modules.mssaccount.accountidc.domain.AccountIdc;
import com.sccl.modules.mssaccount.accountidc.mapper.AccountIdcMapper;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.domain.PowerWriteoffRewrite;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.mssaccount.mssaccountbill.service.IMssAccountbillService;
import com.sccl.modules.mssaccount.mssaccountbillitem.domain.MssAccountbillitem;
import com.sccl.modules.mssaccount.mssaccountbillpayinfo.domain.MssAccountbillpayinfo;
import com.sccl.modules.mssaccount.mssaccountclearitem.domain.MssAccountclearitem;
import com.sccl.modules.mssaccount.mssaccountclearitem.mapper.MssAccountclearitemMapper;
import com.sccl.modules.mssaccount.mssinterface.domain.*;
import com.sccl.modules.mssaccount.mssinterface.dto.BaseStationBillingDataAdjustmentDTO;
import com.sccl.modules.mssaccount.mssinterface.mapper.MssInterfaceMapper;
import com.sccl.modules.mssaccount.mssinterface.vo.CheckTowerVo;
import com.sccl.modules.mssaccount.mssinterface.vo.StationMssBussElectVo;
import com.sccl.modules.mssaccount.msssapinfomain.service.IMssSapinfomainService;
import com.sccl.modules.mssaccount.rbillitemaccount.mapper.RBillitemAccountMapper;
import com.sccl.modules.mssaccount.rbillitemaccount.service.IRBillitemAccountService;
import com.sccl.modules.oss.util.OperationLogHelper;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.uniflow.common.WFModel;
import com.sccl.modules.uniflow.wfprocinst.domain.WfProcInst;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftask.service.IWfTaskService;
import com.sccl.modules.util.WFConstants;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.dom4j.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.hutool.core.date.DateTime.now;
import static java.util.stream.Collectors.*;


/**
 * MSS财务接口 服务层实现
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
@Service
@Slf4j
public class MssInterfaceServiceImpl extends BaseServiceImpl<MssInterface> implements IMssInterfaceService {

    public static final String SYNCTYPEDELETE = "3";
    public static final String SYNCTYPECREATE = "1";
    public static final String SYNCTYPEUPDATE = "2";
    private static final Logger logger = LoggerFactory.getLogger(MssInterfaceServiceImpl.class);
    private static final int SYNCSUM = 5000;
    public static String[] BILLNAMES = new String[]{"报销", "挂账", "挂账支付", "预付", "预付冲销", "借款冲销", "前期预付冲销", "收款", "预估",
            "预估冲销", "调账"};
    private static String CHARSET = "UTF-8";
    public static ConcurrentHashMap<Long, BillExecuteState> executeMap = new ConcurrentHashMap<>();
    public static ConcurrentHashMap<Long, BillExecuteState> executeMap3 = new ConcurrentHashMap<>();
    public static ConcurrentHashMap<Long, BillPollState> pollMap = new ConcurrentHashMap<>();
    public static ExecutorService taskPool = Executors.newFixedThreadPool(10);
    public static ExecutorService taskPool3 = Executors.newFixedThreadPool(10);
    @Autowired
    MssClient mssClient;
    @Autowired
    MssJsonClient mssJsonClient;
    @Autowired
    RBillitemAccountMapper rBillitemAccountMapper;// 报账明细 台账 关联
    @Autowired
    AmmeterorprotocolMapper ammeterorprotocolMapper;
    @Autowired
    AccountIdcMapper accountIdcMapper;
    @Autowired
    private MeterinfoMapper meterinfoMapper;
    @Autowired
    private TwocMapper twocMapper;
    @Autowired
    private MssInterfaceServiceImpl mssInterfaceServiceImpl;
    @Autowired
    private MeterinfoAllJtApplyMapper meterinfoAllJtApplyMapper;
    @Value("${towerMapUrl}")
    private String ACCOUNT_CALLBACK_URL;
    @Value("${MssInterface.Account.USER}")
    private String ACCOUNT_CALLBACK_USER_ID;
    @Value("${MssInterface.Account.PASSWORD}")
    private String ACCOUNT_CALLBACK_PASSWORD;
    @Value("${MssInterface.MssClient.SOAPURL}")
    private String SOAPURL;// = "http://136.127.56.5:9001/mssproxy";// 测试地址
    @Value("${MssInterface.MssClient.SOAPURLTESTFORLN:}")
    private String SOAPURLTESTFORLN;// = "http://136.127.56.5:9001/mssproxy";// 测试地址
    @Value("${MssInterface.BaseInfo.RETRY}")
    private String RETRY;// = "1";
    @Value("${MssInterface.BaseInfo.S_PROVINCE}")
    private String S_PROVINCE;// = "26";
    @Value("${MssInterface.BaseInfo.S_SYSTEM}")
    private String S_SYSTEM;// = "LN-NH";
    @Value("${MssInterface.BaseInfo.T_PROVINCE}")
    private String T_PROVINCE;// = "26";
    @Value("${MssInterface.BaseInfo.T_SYSTEM}")
    private String T_SYSTEM;// = "CW-CFBZ-BFSS";
    @Value("${MssInterface.HrId.orgEndStr}")
    private String orgEndStr;// = "@LN";
    @Value("${MssInterface.processCode}")
    private String processCode;
    @Value("${MssInterface.serverName.sn_bill}")
    private String sn_bill;//能耗集成报账接口模式一	NH_SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff
    @Value("${MssInterface.serverName.sn_status}")
    private String sn_status;//能耗获取报账单状态	NH_SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus
    @Value("${MssInterface.serverName.sn_sap}")
    private String sn_sap;//能耗获取sap标准凭证信息	NH_SI_CF_Integrated_IN_Syn_OP_GetSapNumber
    @Value("${MssInterface.serverName.sn_org}")
    private String sn_org;//能耗获取报账组织信息	NH_SI_CF_Integrated_IN_Syn_OP_GetWriteoffBaseData
    @Autowired
    private BudgetReimbursementHistoryMapper budgetReimbursementHistoryMapper;
    @Autowired
    private OperLogMapper operLogMapper;
    @Autowired
    private MssAccountbillMapper billMapper;
    @Autowired
    private IMssAccountbillService mssAccountbillService;
    @Autowired
    private IRBillitemAccountService rBillitemAccountService;
    @Autowired
    private MssAccountclearitemMapper clearitemMapper;
    @Autowired
    private MssInterfaceMapper mssInterfaceMapper;
    @Autowired
    private IWfTaskService wfTaskService;
    @Autowired
    private IWfProcInstService wfProcInstService;
    @Autowired
    private IMssSapinfomainService mssSapinfomainService;
    @Autowired
    private com.sccl.modules.mssaccount.mssabccustomer.service.IMssAbccustomerService mssAbccustomerService;
    @Autowired
    private IMssAccountPrepaidService mssAccountPrepaidService;
    @Autowired
    private MeterdatesfortwocMapper meterdatesfortwocMapper;
    @Autowired
    private MeterOtherDatesfortwocMapper meterOtherDatesfortwocMapper;
    @Autowired
    private MeterPollutionDatesfortwocMapper meterPollutionDatesfortwocMapper;
    @Autowired
    private SyncresultMapper syncresultMapper;
    @Value("${sccl.deployTo}")
    private String deployTo;
    @Value("${spring.profiles.active}")
    private String envProfile;
    @Value("${scclTnterface.httpforwardUrl}")
    private String httpforwardUrl;
    private List<MeterEquipmentInfo2> meterEquipmentInfo2s;
    private List<CopyMeter> copyMeters;


    // 报账
    private static void test4() {
        MssInterfaceServiceImpl m = new MssInterfaceServiceImpl();
        String sendXML = "";
        Map<String, Object> map = null;
        try {
            String returnXML = m.getStringhttp(sendXML);
            System.out.println(returnXML);
            map = m.prasebillXML(returnXML);//报账解析
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSON(map).toString());
    }
    /*流程引擎回调结束*/
    /*流程引擎回调结束*/

    /// ///////////////////////////////////////////////////////////////////////////////
    /// ////////////////////////////////////////////////////////////////////////////////

    // 查询 获取sap标准凭证信息 组装xml参数 instanceCode 报账单号
    private static void test3() {
        MssInterfaceServiceImpl m = new MssInterfaceServiceImpl();
        String sendXML = "";
//        TYA02513070600011907007006
//        String sendXML = m.sendSAPXML("TYA02513220700011510000001",
        //        "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GetWriteoffBaseData");
        String returnXML = getStringhttp(sendXML);
        System.out.println(returnXML);
//        String returnXML = m.doMssHttp(sendXML);
        Map<String, Object> map = null;
        try {
            map = m.praseSAPXML(returnXML);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSON(map).toString());
    }

    // 查询报账单状态
    private static void test2() {
        MssInterfaceServiceImpl m = new MssInterfaceServiceImpl();
//        String sendXML = m.sendwriteoffInstanceCodeXML("", "3910671236539256832",
        //        "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus");
        String sendXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><soapenv:Envelope xmlns:soapenv=\"http://schemas" +
                ".xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://services.allcommonwriteoff.ws.dakar.eshore" +
                ".com/\"><soapenv:Header/><soapenv:Body><ser:OP_GainWriteoffInstStatus><I_REQUEST><BASEINFO><PMSGID" +
                "/><MSGID" +
                ">NH_20190909195000_89184</MSGID><RETRY>1</RETRY><SENDTIME>20190909195000</SENDTIME><SERVICENAME" +
                ">SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus</SERVICENAME><S_PROVINCE>13</S_PROVINCE" +
                "><S_SYSTEM>NH</S_SYSTEM><T_PROVINCE>13</T_PROVINCE><T_SYSTEM>CW-CFBZ-CSQN</T_SYSTEM></BASEINFO" +
                "><MESSAGE" +
                "><bizMessage><![CDATA[<requestMessage><items><item>" +
//                "<writeoffInstanceCode>TYA02513060600021908000001</writeoffInstanceCode>" +
                "<otherSystemMainId>3945845598665732096</otherSystemMainId></item></items></requestMessage" +
                ">]]></bizMessage></MESSAGE></I_REQUEST></ser:OP_GainWriteoffInstStatus></soapenv:Body></soapenv" +
                ":Envelope>";
        Map<String, Object> map = null;
        try {
            MssClient mssClient = new MssClient();
            String returnXML = getStringhttp(sendXML);
            System.out.println(returnXML);
            map = m.praseOP_GainWriteoffInstStatus(returnXML);// 四川
            if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
                List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("items");
                if (items != null && items.size() > 0) {
                    Map<String, Object> item = items.get(0);
                    System.out.println(JSON.toJSON(item).toString());
                    System.out.println(item.get("status").toString());
                }
            } else {
                System.out.println(JSON.toJSON(map.get("errorMsg")).toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
//        System.out.println(JSON.toJSON(map).toString());
    }

    private static String getStringhttp(String sendXML) {
        Map<String, Object> param = new HashMap<>();
        param.put("url", "http://***********:10001/PIproxy");
//            param.put("url", "http://************:8060/PIproxy");
        param.put("params", sendXML);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        return rest.postForObject("http://*************/energy-interface/httpforward/" + "doMss", requestEntity,
                String.class
        );
    }

    public static void test5() {

        MssInterfaceServiceImpl m = new MssInterfaceServiceImpl();
        String sendXML = m.sendXML("21021493@LN", "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GetWriteoffBaseData");
        Map<String, Object> param = new HashMap<>();
        param.put("url", "http://***********:10001/PIproxy");
        param.put("params", sendXML);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        rest.postForObject("http://localhost:8080/energy-interface/httpforward/doMss", requestEntity, String.class);
    }

    // 查询 报账人机构
    public void test1() {
        String resXML = "<SOAP:Envelope xmlns:SOAP='http://schemas.xmlsoap.org/soap/envelope/'><SOAP:Header/><SOAP:Body><ns2:OP_GainWriteoffInstStatusResponse xmlns:ns2='http://services.allcommonwriteoff.ws.dakar.eshore.com/' xmlns:S='http://schemas.xmlsoap.org/soap/envelope/'><E_RESPONSE><BASEINFO><MSGID>NH_20231020144009_92732</MSGID><PMSGID/><RETRY>1</RETRY><SENDTIME>20231020144009</SENDTIME><SERVICENAME>SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus</SERVICENAME><S_PROVINCE>13</S_PROVINCE><S_SYSTEM>CW-CFBZ-CSQN</S_SYSTEM><T_PROVINCE>13</T_PROVINCE><T_SYSTEM>NH</T_SYSTEM></BASEINFO><MESSAGE><bizMessage><![CDATA[<responseMessage>\n" +
                "  <TYPE>S</TYPE>\n" +
                "  <items>\n" +
                "    <item>\n" +
                "      <otherSystemMainId>4479675219119853568</otherSystemMainId>\n" +
                "      <status>2</status>\n" +
                "      <writeoffInstanceCode>TYA02513180000232310100020</writeoffInstanceCode>\n" +
                "      <actName>经办人</actName>\n" +
                "      <handlerAccount>********@SC</handlerAccount>\n" +
                "      <auditSum>450000.0</auditSum>\n" +
                "    </item>\n" +
                "  </items>\n" +
                "</responseMessage>]]></bizMessage></MESSAGE></E_RESPONSE></ns2:OP_GainWriteoffInstStatusResponse></SOAP:Body></SOAP:Envelope>\n";
        try {
            Map<String, Object> stringObjectMap = praseOP_GainWriteoffInstStatus(resXML);
            log.info("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 流程引擎回调（通过MQ）
     *
     * @param wfModel 流程消息对象
     */
//    @Override
    public void uniflowCallBack(WFModel wfModel) throws Exception {
//        System.out.println("-----------------lt:" + wfModel.toString());
        logger.debug("-----------------lt:" + wfModel.toString());
        if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {//更新流程Id  流程 提交
            try {
                doStartFlow(wfModel.getBusiId(), wfModel.getProcInstId());
                logger.debug("报账单新增" + wfModel.getBusiId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
                logger.error("提交流程失败:" + e.getMessage());
                throw new BaseException("提交流程失败:" + e.getMessage());//
            }
        } else if ("sys".equals(wfModel.getVariables().get("appointUserId"))) {
            try {// 送财辅接口
                sendToMss(Long.valueOf(wfModel.getBusiId()));
                logger.debug("送财辅接口" + wfModel.getBusiId());
            } catch (Exception e) {
                e.printStackTrace();
                doErrorFlow(wfModel.getBusiId());
                insertLog("提交财辅系统接口异常", "uniflowCallBack", e.getMessage());
                logger.error("提交财辅系统接口异常，提交失败" + e.getMessage());
                throw e;
                //throw new BaseException("提交财辅系统接口异常，提交失败" + e.getMessage());//
            }
        } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {//
//            流程完成后执行的回调  不在这里处理
//            改为系统自动轮询结果 getStausBybills
//            try {
//                doEndFlow(wfModel.getBusiId());
//            } catch (Exception e) {
//                e.printStackTrace();
//                throw new BaseException("提交流程失败:" + e.getMessage());//
//            }
        } else if ("TURNBACK_TO_START".equals(wfModel.getCallbackType())) {//流程 退回
            doExistFlow(wfModel.getBusiId());
        } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {//流程 终止
            doKillFlow(wfModel.getBusiId());
        }
    }

    // 记录 错误日志
    private void insertLog(String title, String method, String errorMes) {
        OperLog model = new OperLog();
        model.setOperName("mss");
        model.setTitle(title);
        model.setMethod(method);
        model.setErrorMsg(errorMes);
        model.setOperTime(new Date());

        operLogMapper.insert(model);
    }

    // 提交流程 执行方法
    public void doStartFlow(String busiId, Long processinstid) {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(2);//代办
        bill.setId(Long.valueOf(busiId));
        bill.setProcessinstid(processinstid);
        billMapper.updateForModel(bill);
        //根据 报账单  台账状态
        rBillitemAccountService.updateAccountsByBill(bill);
    }

    // 流程结束 执行方法
    public void doEndFlow(MssAccountbill bill) {
        billMapper.updateForModel(bill);//更新状态
        //根据 报账单  修改台账状态
        rBillitemAccountService.updateAccountsByBill(bill);
        // 如果 所有报账都完成 更新 归集单 为报账完成
        rBillitemAccountService.updatePreByBill(bill);
        if (bill.getStatus() == 8 && "-1".equals(bill.getIresult())) {
            // 退单 标识 退单 删除 台账 明细关联关系
            rBillitemAccountMapper.deleteRbillitemAccountByBillId(bill.getId());// 删除旧的关联关系
            // 推送数据接口 终止报账
            try {//支付类报账单送集团
                selectWriteoffDetailInfo(bill.getId(), "3");// 终止报账
            } catch (Exception e) {
                e.printStackTrace();
                insertLog("推送数据接口 终止报账，报账单:【" + bill.getId() + "】", "meterInfo", e.getMessage());
                logger.error("报账单" + bill.getId() + " 同步集团电费化小失败：" + e.getMessage());
            }
        }
    }

    // 从财辅失败 执行方法
    public void doErrorFlow(String busiId) {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(-3);//完成
        bill.setId(Long.valueOf(busiId));
        billMapper.updateForModel(bill);
    }

    // 流程 退回
    private void doExistFlow(String busiId) {
        // 修改状态
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(-2);//退回
        bill.setId(Long.valueOf(busiId));
        billMapper.updateForModel(bill);
    }

    // 流程 终止
    private void doKillFlow(String busiId) {
        // 修改状态
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(1);//草稿
        bill.setId(Long.valueOf(busiId));
        billMapper.updateForModel(bill);
    }

    @Override // 送财辅接口
    public void sendToMss(Long id) throws Exception {
        MssAccountbill bill = null;
        try {
            // 获取报账单 包含 明细 归集单 挑对 外部收款人 等
            bill = mssAccountbillService.getByid(id);
            checkMssAccountbill(bill);
            // 接口操作 普通报账
            String sendbillXML = sendBillXML(bill);
            //logger.debug(sendbillXML);
            //logger.info("sendbillXML " + sendbillXML);
            //System.out.println("sendToMss  doMssHttp start:");
            //logger.info("sendToMss  doMssHttp start:");
            String resbillXML = doMssHttp(sendbillXML);
            logger.info("接收到响应结果:{}", resbillXML);
            //System.out.println("sendToMss doMssHttp end:" + resbillXML);
            /*String resbillXML="<SOAP:Envelope xmlns:SOAP=\'http://schemas.xmlsoap
            .org/soap/envelope/\'><SOAP:Header/><SOAP:Body><ns2:OP_AutoCreateWriteoffResponse
                xmlns:ns2=\'http://services.allcommonwriteoff.ws.dakar.eshore.com/\' xmlns:S=\'http://schemas.xmlsoap
            .org/soap/envelope/\'><E_RESPONSE><BASEINFO><MSGID>NH_20210917120754_34801</MSGID><PMSGID/><RETRY>1
                </RETRY><SENDTIME>20210917120756</SENDTIME><SERVICENAME
                >SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff</SERVICENAME><S_PROVINCE>13</S_PROVINCE
                ><S_SYSTEM>CW
            -CFBZ-CSQN</S_SYSTEM><T_PROVINCE>13</T_PROVINCE><T_SYSTEM>NH</T_SYSTEM></BASEINFO><MESSAGE><bizMessage
                ><![CDATA[<responseMessage>"+
                    "<TYPE>S</TYPE>"+
                    "<items>"+
                    "<item>"+
                    "<otherSystemMainId>4198065837069959168</otherSystemMainId>"+
                    "<resultCode>1</resultCode>"+
                    "</item>"+
                    "</items>"+
                    "</responseMessage>]]></bizMessage></MESSAGE></E_RESPONSE></ns2:OP_AutoCreateWriteoffResponse
                    ></SOAP:Body></SOAP:Envelope>";
*/
            //System.out.println("sendToMss : insertXML start");
            logger.info("sendToMss : insertXML start");
            insertXML(id.toString(), sendbillXML, resbillXML, "sendBill");
            //System.out.println("sendToMss : insertXML end");
            Map<String, Object> map = prasebillXML(resbillXML);//ResultItem
            if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
                LinkedList<Map<String, Object>> items = (LinkedList<Map<String, Object>>) map.get("items");
                Map<String, Object> ResultItem = items.get(0);
                //  选项：1-调用成功，0-调用失败
                //说明：当bizMessage结构中的TYPE为S时，则触发的所有报账单数据校验通过，系统已接受等待生成报账单，
                // 当TYPE为E时，则触发的所有报账单数据校验不通过，
                // 对于ResultItem结构中的resultCode反馈为1的数据可重新触发，报账单数据校验通过，可以发起报账，
                // 于resultCode反馈为0的数据，可通过ResultItem结构中的errorMsg查看校验不通过的错误信息。
                if (ResultItem != null && "1".equals(ResultItem.get("resultCode"))) {
                    // 修改状态
                    bill.setId(id);
                    bill.setStatus(-4);//等待生成 此时可能没有报账单号
                    if (ResultItem.get("writeoffInstanceCode") != null)
                        bill.setWriteoffInstanceCode(ResultItem.get("writeoffInstanceCode").toString());//报账单号
                    billMapper.updateForModel(bill);
                    insertLog("送财辅成功" + bill.getId(), "sendToMss", null);
                    //logger.debug("送财辅成功:" + "【" + bill.getId() + "】");
                } else {
                    bill.setId(id);
                    bill.setStatus(-3);// 送财辅失败
                    billMapper.updateForModel(bill);
                    insertLog("送财辅失败 数据异常", "sendToMss", JSON.toJSON(map).toString());
                    logger.error("送财辅失败 数据异常:" + JSON.toJSON(map).toString() + "【" + bill.getId() + "】");
                }
            } else {
                bill.setId(id);
                bill.setStatus(-3);// 送财辅失败
                MssAccountbill mssAccountbill = mssAccountbillService.get(id);
                if (mssAccountbill != null && mssAccountbill.getStatus() == 2)// 避免 多次回调造成的 送财辅失败
                    billMapper.updateForModel(bill);
                logger.error("送财辅失败 接口异常:" + JSON.toJSON(map).toString() + "【" + bill.getId() + "】");
            }
        } catch (Exception e) {
            logger.info("sendToMss  doMssHttp Exception:{}" + e);
            bill = new MssAccountbill();
            bill.setId(id);
            bill.setStatus(-3);// 送财辅失败
            billMapper.updateForModel(bill);
            insertLog("送财辅失败 系统异常，报账单:【" + bill.getId() + "】", "sendToMss", e.getMessage());
            logger.error("送财辅失败 系统异常:" + e.getMessage() + "【" + bill.getId() + "】");
            throw e;
        }
    }

    public void sendToMss2(Long id) throws Exception {
        MssAccountbill bill = null;

        // 获取报账单 包含 明细 归集单 挑对 外部收款人 等
        bill = mssAccountbillService.getByid(id);
        checkMssAccountbill(bill);
        // 接口操作 普通报账
        String sendbillXML = sendBillXML(bill);
        //logger.debug(sendbillXML);
        //logger.info("sendbillXML " + sendbillXML);
        //System.out.println("sendToMss  doMssHttp start:");
        //logger.info("sendToMss  doMssHttp start:");
        logger.info("请求报文{}", sendbillXML);
        String resbillXML = doMssHttp2(sendbillXML);
        logger.info("接收到响应结果:{}", resbillXML);
        logger.info("开始解析响应报文");
        Map<String, Object> map = prasebillXML(resbillXML);//ResultItem
        logger.info("解析成功");
        if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
            LinkedList<Map<String, Object>> items = (LinkedList<Map<String, Object>>) map.get("items");
            Map<String, Object> ResultItem = items.get(0);
            //  选项：1-调用成功，0-调用失败
            //说明：当bizMessage结构中的TYPE为S时，则触发的所有报账单数据校验通过，系统已接受等待生成报账单，
            // 当TYPE为E时，则触发的所有报账单数据校验不通过，
            // 对于ResultItem结构中的resultCode反馈为1的数据可重新触发，报账单数据校验通过，可以发起报账，
            // 于resultCode反馈为0的数据，可通过ResultItem结构中的errorMsg查看校验不通过的错误信息。
            if (ResultItem != null && "1".equals(ResultItem.get("resultCode"))) {
                // 修改状态
                if (ResultItem.get("writeoffInstanceCode") != null)
                    bill.setWriteoffInstanceCode(ResultItem.get("writeoffInstanceCode").toString());//报账单号
                logger.info("财辅返回主单号{}", bill.getWriteoffInstanceCode());
            } else {
                logger.error("送财辅失败 数据异常:" + JSON.toJSON(map).toString() + "【" + bill.getId() + "】");
            }
        }

    }

    // 推送报账 验证
    public void checkMssAccountbill(MssAccountbill bill) {
        List<MssAccountbillitem> list = bill.getItem();
        BigDecimal sum = bill.getSum();
        BigDecimal inputTaxSum = bill.getInputTaxSum();
        if (inputTaxSum == null) {
            inputTaxSum = BigDecimal.ZERO;
        }
        BigDecimal totalmx = BigDecimal.valueOf(0);
        BigDecimal totalmxTax = BigDecimal.valueOf(0);
        for (MssAccountbillitem item : list) {
            totalmx = totalmx.add(item.getSum());//明细不含税 和
            if (item.getTaxAdjustSum() == null)
                item.setTaxAdjustSum(BigDecimal.ZERO);
            totalmxTax = totalmxTax.add(item.getTaxAdjustSum());//明细 税和
        }
        if (totalmx.abs().compareTo(sum.abs()) != 0) {
            throw new BaseException("明细不含税金额(和)【" + totalmx.doubleValue() + "】不等于【基本信息不含税金额】【" + sum.doubleValue() +
                    "】");
        }
        if (totalmxTax.abs().compareTo(inputTaxSum.abs()) != 0) {
            throw new BaseException("明细税额(和)【" + totalmxTax.doubleValue() + "】不等于【基本信息税额】【" + inputTaxSum.doubleValue() + "】");
        }
        // 验证挑对金额
        List<MssAccountclearitem> clearitem = bill.getClearitem();
        String type = bill.getBilltype().toString();
        if (type.equals("3") || type.equals("5") || type.equals("10")) {
            if (clearitem != null && clearitem.size() > 0) {
            } else
                throw new BaseException("没有挑对信息");
        }
        if (clearitem != null && clearitem.size() > 0) {
            BigDecimal totalck = BigDecimal.valueOf(0);
            for (MssAccountclearitem item : clearitem) {
                totalck = totalck.add(item.getPickingsum());//明细 和
            }
            if (totalck.abs().compareTo(totalmx.add(totalmxTax).abs()) != 0) {
                throw new BaseException("挑对金额(和)【" + totalck.doubleValue() + "】不等于 明细金额(和)【" + totalmx.add(totalmxTax).doubleValue() + "】");
            }
            if (totalck.abs().compareTo(sum.add(inputTaxSum).abs()) != 0) {
                throw new BaseException("挑对金额(和)【" + totalck.doubleValue() + "】不等于 报账单金额【" + sum.add(inputTaxSum).doubleValue() + "】");
            }
        }
/*        if ("sc".equals(deployTo) && !"6".equals(bill.getPickingMode().toString()) && ("8".equals(bill.getBilltype
().toString()) || "11".equals(bill.getBilltype().toString()))) {
            int itemSize = bill.getItem().size();
            if (clearitem == null) {
                throw new BaseException("收款/调账：没有挑对信息！");
            } else if (clearitem.size() != itemSize) {
                throw new BaseException("【报账明细条数和金额需与挑对条数和金额一致】收款/调账：(" + itemSize + ")条明细信息对应(" + clearitem.size() +
                 ")条挑对信息有误!");
            }
        }*/
    }

    @Override //轮询 送财辅失败的 重新送一次
    public void sendToMssByError() {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(-3);//送财辅失败的
        List<MssAccountbill> mssAccountbills = billMapper.selectListByAuto(bill);
        for (MssAccountbill mssbill : mssAccountbills) {
            try {
                sendToMss(mssbill.getId());//送财辅接口
            } catch (Exception e) {
                e.printStackTrace();
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                insertLog("送财辅接口报错，报账单:【" + mssbill.getId() + "】", "sendToMssByError", e.getMessage());
                logger.error("送财辅接口报错，报账单:【" + mssbill.getId() + "】" + e.getMessage() + sw.toString());
            }
        }
    }

    // 处理 轮询 接口 	获取报账单状态
    public void getStausBybills() {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(-4);// 等待生成
        // 查询 所有 送财辅中的报账单
        List<MssAccountbill> mssAccountbills = billMapper.selectListByAuto(bill);
        for (MssAccountbill mssbill : mssAccountbills) {
            try {
                handleMssInterface(mssbill);
            } catch (Exception e) {
                e.printStackTrace();
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                insertLog("报账轮询接口(查询报账单状态)报错，报账单:【" + mssbill.getId() + "】", "getStausBybills", e.getMessage());
                logger.error("报账轮询接口(查询报账单状态)报错，报账单:【" + mssbill.getId() + "】" + e.getMessage() + sw.toString());
            }
        }
    }

    public void getStausBybillsPro() {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(-4);// 等待生成
        // 查询 所有 送财辅中的报账单
        List<MssAccountbill> mssAccountbills = billMapper.selectListByAuto(bill);
        mssAccountbills.forEach(
                item -> {
                    if (item.Unfinsh(item.getId(), executeMap)) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(
                                () -> {
                                    try {
                                        executeMap.put(item.getId(), BillExecuteState.EXECUTE);
                                        log.info("{}开始轮询", item.getId());
                                        handleMssInterfacePro(item, executeMap);
                                        log.info("{}轮询成功", item.getId());
                                    } catch (Exception e) {
                                        executeMap.put(item.getId(), BillExecuteState.EXCEPTION);
                                        log.info("{}轮询失败", item.getId());
                                        throw new RuntimeException(e);
                                    }
                                }, taskPool
                        );
                        executeMap.put(item.getId(), BillExecuteState.EXIST);
                        log.info("{}加入线程池等待队列", item.getId());
                    }
                }
        );
    }

    @Override // 处理 轮询 接口 	获取报账单状态
    public void getStausBybillsSap() {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(3);// 生成报账单 未生成凭证的单子
        List<MssAccountbill> mssAccountbills = billMapper.selectListByAuto(bill);
        for (MssAccountbill mssbill : mssAccountbills) {
            try {
                handleMssInterface(mssbill);
                //handleMssInterface_test(mssbill);
            } catch (Exception e) {
                e.printStackTrace();
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                insertLog("报账轮询接口(查询报账单状态)报错，报账单:【" + mssbill.getId() + "】", "getStausBybills", e.getMessage());
                logger.error("报账轮询接口(查询报账单状态)报错，报账单:【" + mssbill.getId() + "】" + e.getMessage() + sw.toString());
            }
        }
    }


    // 处理 轮询的  报账单 一条条的处理
    public Map<String, Object> handleMssInterface(MssAccountbill mssbill) throws Exception {
        String writeoffInstanceCode = mssbill.getWriteoffInstanceCode();
        String billId = mssbill.getId().toString();

        //根据 报账编码 外围主单id 查询报账单状态
//        String serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        if ("sc".equals(deployTo)) {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        } else if ("ln".equals(deployTo)) {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        } else {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        }

        String sendXML = sendwriteoffInstanceCodeXML(writeoffInstanceCode, billId, sn_status);
        String resXML = doMssHttp(sendXML);
        //System.out.println("handleMssInterface resXML"+resXML);
        Map<String, Object> map = null;
        if ("sc".equals(deployTo)) {
            map = praseOP_GainWriteoffInstStatus(resXML);
        } else if ("ln".equals(deployTo)) {
            map = praseOP_GainWriteoffInstStatus(resXML);
        } else {
            map = praseOP_GetWriteoffBaseDataResponse(resXML);
        }
        insertXML(billId, sendXML, resXML, "getStatus");
        MssAccountbill bill = new MssAccountbill();
        if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
            bill.setId(mssbill.getId());
            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("items");
            Map<String, Object> item = items.get(0);

            // 能耗状态 状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,'退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单'

            if ("-4".equals(item.get("status"))) {//等待生成报账单中
                bill.setStatus(-4);
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
            } else if ("-3".equals(item.get("status"))) {//报账单发起失败
                bill.setStatus(-3);
                billMapper.updateForModel(bill);
            } else if ("-1".equals(item.get("status"))) {//报账单删除
//                bill.setStatus(-1);
                // 没有 财辅退单状态 财辅删除 直接对应能耗为财辅退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {


                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
            } else if ("-2".equals(item.get("status"))) {//财辅 退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {
                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
//                //更新抄表数据
//                mssInterfaceServiceImpl.selectWriteoffDetailInfo2(billId+"/update");
            } else if ("2".equals(item.get("status"))) {// 生成报账单成功

                bill.setStatus(3);//生成报帐单
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
                /* if ("2".equals(mssbill.getBizTypeCode()) || "1".equals(mssbill.getBizTypeCode())) {//支付类报账单送集团*/
                boolean budgetflag = false;
/*                List<MssAccountbillitem> mssbillitems=mssAccountbillService.getByid(mssbill.getId()).getItem();
                if (mssbillitems != null && mssbillitems.size() > 0) {
                    for (int i = 0; i <mssbillitems.size() ; i++) {
                        if ("1".equals(mssbillitems.get(i).getBudgetType()))
                            budgetflag=true;
                            break;
                    }
                }*/
                if ("1".equals(mssbill.getBilltype().toString()) || "2".equals(mssbill.getBilltype().toString()) ||
                        "3".equals(mssbill.getBilltype().toString()) || "5".equals(mssbill.getBilltype().toString()) || "6".equals(mssbill.getBilltype().toString()) || "7".equals(mssbill.getBilltype().toString()) || "8".equals(mssbill.getBilltype().toString()) || "9".equals(mssbill.getBilltype().toString()) || "10".equals(mssbill.getBilltype().toString()))
                    budgetflag = true;
                if (budgetflag) {//占成本类报账单送集团 *********/
                    try {
/*                        boolean senFlag1 = true;
                        boolean senFlag2 = true;
                        senFlag1 = billMapper.selectSendFalg1(billId);
                        senFlag2 = billMapper.selectSendFalg2(billId);
                        log.info(
                          "billid={}同步电表基础信息{}||同步电表用电信息{}",
                          billId, senFlag1 ? "执行" : "不执行", senFlag2 ? "执行" : "不执行"
                        );*/
                        // 新增 若是传输数据成功，则不再推送数据给集团，若是失败，则一直轮询传
                        // 判断方法 根据 报账单表（mss_accountbill）中的状态来判断
                        String iscanupdate = billMapper.getMssAccountbillStatusById(mssbill.getId());
                        // 若是为空，则说明是首次推送，则直接推送 1-标识推送成功
                        if (StrUtil.isBlank(iscanupdate) || !"1".equals(iscanupdate)) {
                            // 推送数据给集团
                            // 推送电表基础信息
                            syncEnergyMeterInfosBybill(mssbill.getId());//同步电表
                            selectWriteoffDetailInfo(mssbill.getId(), "1");// 同步报账单
                            //syncEnergyMeterInfosByequ(mssbill.getId());//同步集团能源平台

                            //上传集团基础信息、日均电量 add by qxm 2025-03-28
                            //停用上报  0709 已提前上报D类及基站 add by xuyi 7月启用d及基站站表清单
                            //transPowerData(bill.getId());
                        }
//                        //更新抄表数据
//                        mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId()+"/"+"create");
                        //同步成功更新同步状态 Iresult 为 5 标识推送成功
                        bill.setIresult("5");
                        billMapper.updateForModel(bill);
                        mssInterfaceMapper.updateAmmeterno(mssbill.getId());// 更新电表 标识已经推送

                    } catch (Exception e) {
                        e.printStackTrace();
                        insertLog("同步集团电费化小失败，报账单:【" + mssbill.getId() + "】", "meterInfo", e.getMessage());
                        logger.error("报账单" + mssbill.getId() + " 同步集团电费化小失败：" + e.getMessage());
                    }
                }
            } else if ("3".equals(item.get("status"))) {//已生成SAP凭证
//                bill.setStatus(4);

                bill.setStatus(7);// 完成
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode").toString());// 财辅报账单号
                // 获取凭证信息 并记录
                updatebillYearMonth(bill);
                doEndFlow(bill); // 更新状态
                //updateprepaid(bill);
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "完成");// 走完流程

                // 6/6新增 铁塔对账反馈报账完成的数据给铁塔系统接口
                sendAccount(mssbill.getId());
                //更新抄表数据
                //try {
                //    //mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId() + "/" + "create");
                //} catch (Exception e) {
                //    //System.out.println("同步抄表数据失败");
                //    //insertLog("同步集团抄表数据", "collectError", "同步的报账id" + mssbill.getId());
                //}
            }
        }

        return map;
    }

    public void transPowerData(Long billId) {
        CompletableFuture.runAsync(
                () ->transPowerDataRun(billId)
        );
    }

    private void transPowerDataRun(Long billId) {
        log.info("报账电表信息上传集团开始----");
        try {
            List<HashMap<String, Object>> baseInfoList = billMapper.getBaseInfoByMssBill(billId);
            if (CollectionUtil.isNotEmpty(baseInfoList)) {
                String budget = "";
                List<CollectMeter> collectMeterList = new ArrayList<>();
                List<MeterInfo3> meterInfoList = new ArrayList<>();
                for (HashMap<String, Object> baseInfo : baseInfoList) {
                    if (ObjectUtil.isEmpty(baseInfo.get("station_code")) ||
                            ObjectUtil.isEmpty("energy_meter_code"))
                        continue;
                    String meterCode = baseInfo.get("energy_meter_code").toString();
                    if (StrUtil.isNotBlank(meterCode)) {
                        //在meterinfo_all_jt中查询站点（优化为返回列表）
                        List<HashMap<String, Object>> meterInfoDbList = meterinfoMapper.getMeterInfoListByStationCode(meterCode);
                        MeterInfo3 meterInfo = createMeterinfo(baseInfo);
                        if (ObjectUtil.isEmpty(meterInfo)) {
                            continue;
                        }
                        if (CollectionUtil.isNotEmpty(meterInfoDbList)) {
                            // 对列表循环判断，检查是否有匹配的记录
                            boolean hasMatchingRecord = false;
                            HashMap<String, Object> matchedRecord = null;
                            for (HashMap<String, Object> old : meterInfoDbList) {
                                // 检查各字段是否匹配
                                if (Objects.equals(baseInfo.get("status"), old.get("status")) &&
                                        Objects.equals(baseInfo.get("station_status"), old.get("stationStatus")) &&
                                        Objects.equals(baseInfo.get("station_type"), old.get("stationType")) &&
                                        Objects.equals(baseInfo.get("type"), old.get("type")) &&
                                        Objects.equals(baseInfo.get("station_name"), old.get("stationName")) &&
                                        Objects.equals(baseInfo.get("station_code"), old.get("stationCode")) &&
                                        Objects.equals(baseInfo.get("energy_meter_name"), old.get("energyMeterName")) &&
                                        Objects.equals(baseInfo.get("county_code"), old.get("countyCode")) &&
                                        Objects.equals(baseInfo.get("county_name"), old.get("countyName"))) {
                                    hasMatchingRecord = true;
                                    matchedRecord = old;
                                    break; // 找到匹配记录，跳出循环
                                }
                            }
                            if (!hasMatchingRecord) {
                                //站点存在但信息有变更，上传集团更新站点信息
                                meterInfo.setType("2");
                                // 使用第一条记录的ID
                                meterInfo.setId(Long.parseLong(meterInfoDbList.get(0).get("id").toString()));
                            }
                        } else {
                            //站点不存在则上传集团新增站点信息
                            meterInfo.setType("1");
                        }
                        if (StrUtil.isNotBlank(meterInfo.getType()) &&
                                StrUtil.isNotBlank(meterInfo.getEnergyMeterCode()) &&
                                StrUtil.isNotBlank(meterInfo.getEnergyMeterName()) &&
                                StrUtil.isNotBlank(meterInfo.getStationCode()) &&
                                StrUtil.isNotBlank(meterInfo.getStationName()) &&
                                StrUtil.isNotBlank(meterInfo.getStationType())) {
                            meterInfoList.add(meterInfo);
                        }
                        //电量数据暂时不发
//                        if (StrUtil.isBlank(budget)) {
//                            budget = ObjectUtil.isNotEmpty(baseInfo.get("BUDGETSETNAME")) ? baseInfo.get("BUDGETSETNAME").toString() : "";
//                        }
//                        List<CollectMeter> itemList = genCollecMeterData(baseInfo);
//                        if (CollectionUtil.isNotEmpty(itemList)) {
//                            collectMeterList.addAll(itemList);
//                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(meterInfoList) && meterInfoList.size() > 0) {
                    //上传基础信息
                    log.info("需要上传基站信息数量----》{}", meterInfoList.size());
                    String res = mssJsonClient.syncEnergyMeterInfos2New(billId, meterInfoList);
                    log.info("上传基站信息返回结果----》{}", res);
                }
                //电量数据暂时不发
//                    if (CollectionUtil.isNotEmpty(collectMeterList) && collectMeterList.size() > 0) {
//                        log.info("需要传的数量----》{}", collectMeterList.size());
//                        try {
//                            mssInterfaceServiceImpl.selectCollectMeterInforsPlusProFixQxm(collectMeterList, budget);
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                            return AjaxResult.error("上传集团基础信息或日均电量出错----》" + e.getMessage());
//                        }
//                    }
            }
        }
        catch (Exception e) {
            log.info("上传集团基础信息或日均电量出错----》{}", e.getMessage());
            e.printStackTrace();
        }
        log.info("报账电表信息上传集团结束。");
    }

    private List<CollectMeter> genCollecMeterData(HashMap<String, Object> collectData) {
        List<CollectMeter> resultList = new ArrayList<>();
        if (ObjectUtil.isEmpty(collectData.get("station_code")) ||
                ObjectUtil.isEmpty(collectData.get("station_name")) ||
                ObjectUtil.isEmpty(collectData.get("startdate")) ||
                ObjectUtil.isEmpty(collectData.get("diff_days")) ||
                ObjectUtil.isEmpty(collectData.get("avg_data")) ||
                ObjectUtil.isEmpty(collectData.get("city_code")) ||
                ObjectUtil.isEmpty(collectData.get("county_code")) ||
                ObjectUtil.isEmpty(collectData.get("city_name")) ||
                ObjectUtil.isEmpty(collectData.get("county_name"))) {
            //空数据不上传
            return  resultList;
        }
        String stationCode = collectData.get("station_code").toString();
        String stationName = collectData.get("station_name").toString();
        String stationType = collectData.get("station_type").toString();
        String cityCode = collectData.get("city_code").toString();
        String countyCode = collectData.get("county_code").toString();
        String cityName = collectData.get("city_name").toString();
        String countyName = collectData.get("county_name").toString();
        Date startDate = DateUtils.parseDate(collectData.get("startdate").toString());
        Integer diffDays = Integer.parseInt(collectData.get("diff_days").toString());
        for (int i = 0; i < diffDays; i++) {
            Date newDate = DateUtils.addDays(startDate, i);
            if (newDate.compareTo(DateUtils.parseDate("2023-01-01")) < 0 ||
                    newDate.compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                //2023-01-01 日期之前不上传, 当前日期之后的采集时间不上传
                continue;
            }
            CollectMeter collectMeter = new CollectMeter();
            collectMeter.setCollectTime(DateUtils.formatDate(newDate, "yyyyMMdd"));
            collectMeter.setCityCode(cityCode);
            collectMeter.setCityName(cityName);
            collectMeter.setCountyCode(countyCode);
            collectMeter.setCountyName(countyName);
            collectMeter.setStationCode(stationCode);
            collectMeter.setStationName(stationName);
            collectMeter.setEnergyData(collectData.get("avg_data").toString());
            collectMeter.setDeviceData(getDeviceData(collectData.get("avg_data").toString(), stationType));
            resultList.add(collectMeter);
        }
        return resultList;
    }

    private String getDeviceData(String energyData, String stationType) {
        String result = "0";
        if (StrUtil.isBlank(energyData)) {
            return result;
        }
        BigDecimal ratio;
        BigDecimal data;
        switch (stationType) {
            case "1110":
                ratio = new BigDecimal(Math.random() * 0.2D + 1.6D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1120":
            case "1130":
                ratio = new BigDecimal(Math.random() * 0.2D + 1.5D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1210":
            case "1220":
                ratio = new BigDecimal(Math.random() * 0.1D + 1.4D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1310":
            case "1320":
            case "1330":
                ratio = new BigDecimal(Math.random() * 0.5D + 1.3D);
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
            case "1411":
            case "1412":
            case "1421":
            case "1422":
            case "1431":
            case "1432":
                ratio = new BigDecimal("1.3");
                data = new BigDecimal(energyData).divide(ratio, 2, RoundingMode.HALF_UP);
                result = data.toString();
                break;
        }
        return result;
    }

    private MeterInfo3 createMeterinfo(HashMap<String, Object> data) {
        if (ObjectUtil.isEmpty(data.get("city_code")) || ObjectUtil.isEmpty(data.get("county_code"))) {
            return null;
        }
        MeterInfo3 meterinfo = new MeterInfo3();
        meterinfo.setProvinceCode(ObjectUtil.isEmpty(data.get("province_code")) ? "" : data.get("province_code").toString());
        meterinfo.setCityCode(ObjectUtil.isEmpty(data.get("city_code")) ? "" : data.get("city_code").toString());
        meterinfo.setCityName(ObjectUtil.isEmpty(data.get("city_name")) ? "" : data.get("city_name").toString());
        meterinfo.setCountyCode(ObjectUtil.isEmpty(data.get("county_code")) ? "" : data.get("county_code").toString());
        meterinfo.setCountyName(ObjectUtil.isEmpty(data.get("county_name")) ? "" : data.get("county_name").toString());
        meterinfo.setStationCode(ObjectUtil.isEmpty(data.get("station_code")) ? "" : data.get("station_code").toString());
        meterinfo.setStationName(ObjectUtil.isEmpty(data.get("station_name")) ? "" : data.get("station_name").toString());
        meterinfo.setEnergyMeterCode(ObjectUtil.isEmpty(data.get("energy_meter_code")) ? "" : data.get("energy_meter_code").toString());
        meterinfo.setEnergyMeterName(ObjectUtil.isEmpty(data.get("energy_meter_name")) ? "" : data.get("energy_meter_name").toString());
        meterinfo.setEnergyType("45");
        meterinfo.setTypeStationCode("1");
        meterinfo.setEnergySupplyWay(ObjectUtil.isEmpty(data.get("energy_supply_way")) ? "" : data.get("energy_supply_way").toString());
        if ("1".equals(meterinfo.getEnergySupplyWay())) {
            //直供电，台账单价
            meterinfo.setContractPrice(ObjectUtil.isEmpty(data.get("unitpirce")) ? "" : data.get("unitpirce").toString());
        } else {
            //转供电，电表单价
            meterinfo.setContractPrice(ObjectUtil.isEmpty(data.get("pirce")) ? "" : data.get("pirce").toString());
        }
        if (StrUtil.isBlank(meterinfo.getContractPrice()) &&
                ObjectUtil.isNotEmpty(data.get("ammeterid")) &&
                StrUtil.isNotBlank(data.get("ammeterid").toString())) {
            //电价为空，通过最近台账获取电价
            BigDecimal price = billMapper.getAccountPrice(Long.parseLong(data.get("ammeterid").toString()));
            meterinfo.setContractPrice(price.toString());
        }
        meterinfo.setUsage(ObjectUtil.isEmpty(data.get("usagecopy")) ? "" : data.get("usagecopy").toString());
        meterinfo.setStatus(ObjectUtil.isEmpty(data.get("status")) ? "" : data.get("status").toString());
        meterinfo.setStationStatus(ObjectUtil.isEmpty(data.get("station_status")) ? "" : data.get("station_status").toString());
        meterinfo.setStationLocation(ObjectUtil.isEmpty(data.get("station_location")) ? "" : data.get("station_location").toString());
        String stationType = ObjectUtil.isEmpty(data.get("station_type")) ? "" : data.get("station_type").toString();
        meterinfo.setStationType(org.apache.commons.lang.StringUtils.left(stationType + "0000", 4));
        meterinfo.setLargeIndustrialElectricityFlag(ObjectUtil.isEmpty(data.get("large_industrial_electricity_flag")) ? "" : data.get("large_industrial_electricity_flag").toString());
        meterinfo.setPowerGridEnergyMeterCode(ObjectUtil.isEmpty(data.get("power_grid_energy_meter_code")) ? "" : data.get("power_grid_energy_meter_code").toString());
        meterinfo.setSiteCode(meterinfo.getStationCode());
        return meterinfo;
    }

    /**
     * 电信报账接口新增报账流程回调
     */
    public void sendAccount(Long bilId) {
        // 根据报账单id查询对应的台账
        List<TowerAccountVo> accountVoList = billMapper.getAccountListByBilId(bilId);
        if (CollectionUtil.isNotEmpty(accountVoList)) {
            List<TowerAccountSendVo> dataList = new ArrayList<>();
            accountVoList.forEach(node -> {
                TowerAccountSendVo towerAccountSendVo = new TowerAccountSendVo();
                towerAccountSendVo.setTowerid(node.getTowerid());
                // 若是铁塔id不为空，则使用铁塔对账中的数据
                towerAccountSendVo.setCity(StrUtil.isBlank(node.getTowerid()) ?
                        node.getCompany() : node.getTowerCity());
                towerAccountSendVo.setCountry(StrUtil.isBlank(node.getTowerid()) ?
                        node.getCountry() : node.getTowerDistrict());
                towerAccountSendVo.setTowerSiteCode(StrUtil.isBlank(node.getTowerid()) ?
                        node.getStationaddresscode() : node.getTowersitecode());
                towerAccountSendVo.setPaymentHouseholdID(StrUtil.isBlank(node.getTowerid()) ?
                        node.getSupplybureauammetercode() : node.getPayaccountnum());
                towerAccountSendVo.setPaymentOrderNumber(StrUtil.isBlank(node.getTowerid()) ?
                        bilId.toString() : node.getPaymentOrderNumber());
                towerAccountSendVo.setStartTime(StrUtil.isBlank(node.getTowerid()) ?
                        node.getStartdate() : node.getUsestarttime());
                towerAccountSendVo.setEndTime(StrUtil.isBlank(node.getTowerid()) ?
                        node.getEnddate() : node.getUseendtime());
                towerAccountSendVo.setStartDegree(StrUtil.isBlank(node.getTowerid()) ?
                        node.getPrevtotalreadings().toString() : node.getUsestartdegree());
                towerAccountSendVo.setEndDegree(StrUtil.isBlank(node.getTowerid()) ?
                        node.getCurtotalreadings().toString() : node.getUseenddegree());
                towerAccountSendVo.setMultiplier(StrUtil.isBlank(node.getTowerid()) ?
                        node.getMulttimes().toString() : node.getMagnification());
                towerAccountSendVo.setPowerConsumption(StrUtil.isBlank(node.getTowerid()) ?
                        node.getTotalusedreadings().toString() : node.getEnergyused());
                towerAccountSendVo.setElectricityBill(StrUtil.isBlank(node.getTowerid()) ?
                        node.getAccountmoney().toString() : node.getActualpay());
                towerAccountSendVo.setTelecomAllocationRatio(StrUtil.isBlank(node.getTowerid()) ?
                        node.getPercent().toString() : node.getApportionmentratio());
                towerAccountSendVo.setTaxation(StrUtil.isBlank(node.getTowerid()) ?
                        node.getTaxamount().toString() : node.getTelecomtax());
                towerAccountSendVo.setPrice(StrUtil.isBlank(node.getTowerid()) ?
                        node.getPrice().toString() : node.getTelecomprice());
                towerAccountSendVo.setProcessStatus("已报帐完成");
                towerAccountSendVo.setNotes("已报帐完成");
                dataList.add(towerAccountSendVo);
            });

            Map<String, Object> param = new HashMap<>();
            param.put("params", dataList);
            RestTemplate rest = new RestTemplate();
            rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
            logger.info("电信报账接口新增报账流程回调:开始上报" + httpforwardUrl);
            String res = rest.postForObject(httpforwardUrl + "sendToTowerAccount", requestEntity, String.class);
            logger.info("电信报账接口新增报账流程回调的上报结果:" + res);
            //保存结果
            saveTowerAccountSend(dataList, res);
        }
    }

    private void saveTowerAccountSend(List<TowerAccountSendVo> dataList, String res) {
        try {
            String syncFlag;
            if (res.contains("\"code\":\"0\"")) {
                //成功
                syncFlag = "1";
            } else {
                //失败
                syncFlag = "0";
            }
            List<TowerAccountSendSync> saveList = dataList.stream().map(TowerAccountSendVo -> {
                TowerAccountSendSync towerAccountSendSync = new TowerAccountSendSync();
                BeanUtils.copyProperties(TowerAccountSendVo, towerAccountSendSync);
                towerAccountSendSync.setId(IdGenerator.getNextId());
                towerAccountSendSync.setTitle("电信报账接口新增报账流程回调上报结果");
                towerAccountSendSync.setSendResult(syncFlag);
                towerAccountSendSync.setReceiveMsg(res);
                towerAccountSendSync.setCreateTime(new Date());
                towerAccountSendSync.setDelFlag("0");
                return towerAccountSendSync;
            }).collect(toList());
            syncresultMapper.saveTowerAccountSendList(saveList);
            logger.info("电信报账接口新增报账流程回调上报结果保存成功，数据条数：{}", dataList.size());
        } catch (Exception e) {
            logger.info("电信报账接口新增报账流程回调上报结果保存报错：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    public Map<String, Object> handleMssInterfacePro(MssAccountbill mssbill, ConcurrentHashMap<Long, BillExecuteState> executeMap) throws Exception {
        String writeoffInstanceCode = mssbill.getWriteoffInstanceCode();
        String billId = mssbill.getId().toString();

        //根据 报账编码 外围主单id 查询报账单状态
//        String serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        if ("sc".equals(deployTo)) {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        } else if ("ln".equals(deployTo)) {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        } else {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        }

        String sendXML = sendwriteoffInstanceCodeXML(writeoffInstanceCode, billId, sn_status);
        String resXML = doMssHttp(sendXML);
        //System.out.println("handleMssInterface resXML"+resXML);
        Map<String, Object> map = null;
        if ("sc".equals(deployTo)) {
            map = praseOP_GainWriteoffInstStatus(resXML);
        } else if ("ln".equals(deployTo)) {
            map = praseOP_GainWriteoffInstStatus(resXML);
        } else {
            map = praseOP_GetWriteoffBaseDataResponse(resXML);
        }
        insertXML(billId, sendXML, resXML, "getStatus");
        MssAccountbill bill = new MssAccountbill();
        if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
            bill.setId(mssbill.getId());
            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("items");
            Map<String, Object> item = items.get(0);

            // 能耗状态 状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,'退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单'

            if ("-4".equals(item.get("status"))) {//等待生成报账单中
                bill.setStatus(-4);
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusFail);
            } else if ("-3".equals(item.get("status"))) {//报账单发起失败
                bill.setStatus(-3);
                billMapper.updateForModel(bill);
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
            } else if ("-1".equals(item.get("status"))) {//报账单删除
//                bill.setStatus(-1);
                // 没有 财辅退单状态 财辅删除 直接对应能耗为财辅退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {


                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
            } else if ("-2".equals(item.get("status"))) {//财辅 退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {
                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
//                //更新抄表数据
//                mssInterfaceServiceImpl.selectWriteoffDetailInfo2(billId+"/update");
            } else if ("2".equals(item.get("status"))) {// 生成报账单成功
                bill.setStatus(3);//生成报帐单
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
                /* if ("2".equals(mssbill.getBizTypeCode()) || "1".equals(mssbill.getBizTypeCode())) {//支付类报账单送集团*/
                boolean budgetflag = false;
/*                List<MssAccountbillitem> mssbillitems=mssAccountbillService.getByid(mssbill.getId()).getItem();
                if (mssbillitems != null && mssbillitems.size() > 0) {
                    for (int i = 0; i <mssbillitems.size() ; i++) {
                        if ("1".equals(mssbillitems.get(i).getBudgetType()))
                            budgetflag=true;
                            break;
                    }
                }*/
                if ("1".equals(mssbill.getBilltype().toString()) || "2".equals(mssbill.getBilltype().toString()) ||
                        "3".equals(mssbill.getBilltype().toString()) || "5".equals(mssbill.getBilltype().toString()) || "6".equals(mssbill.getBilltype().toString()) || "7".equals(mssbill.getBilltype().toString()) || "8".equals(mssbill.getBilltype().toString()) || "9".equals(mssbill.getBilltype().toString()) || "10".equals(mssbill.getBilltype().toString()))
                    budgetflag = true;
                if (budgetflag) {//占成本类报账单送集团 *********/
                    try {
/*                        boolean senFlag1 = true;
                        boolean senFlag2 = true;
                        senFlag1 = billMapper.selectSendFalg1(billId);
                        senFlag2 = billMapper.selectSendFalg2(billId);
                        log.info(
                          "billid={}同步电表基础信息{}||同步电表用电信息{}",
                          billId, senFlag1 ? "执行" : "不执行", senFlag2 ? "执行" : "不执行"
                        );*/
                        syncEnergyMeterInfosBybill(mssbill.getId());//同步电表
                        selectWriteoffDetailInfo(mssbill.getId(), "1");// 同步报账单
//                        //更新抄表数据
//                        mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId()+"/"+"create");
                        //同步成功更新同步状态 Iresult 为 5 标识推送成功
                        bill.setIresult("5");
                        billMapper.updateForModel(bill);
                        mssInterfaceMapper.updateAmmeterno(mssbill.getId());// 更新电表 标识已经推送

                    } catch (Exception e) {
                        e.printStackTrace();
                        insertLog("同步集团电费化小失败，报账单:【" + mssbill.getId() + "】", "meterInfo", e.getMessage());
                        logger.error("报账单" + mssbill.getId() + " 同步集团电费化小失败：" + e.getMessage());
                    }
                }
            } else if ("3".equals(item.get("status"))) {//已生成SAP凭证
//                bill.setStatus(4);

                bill.setStatus(7);// 完成
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode").toString());// 财辅报账单号
                // 获取凭证信息 并记录
                updatebillYearMonth(bill);
                doEndFlow(bill); // 更新状态
                //updateprepaid(bill);
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "完成");// 走完流程
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
                //更新抄表数据
                //try {
                //    //mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId() + "/" + "create");
                //} catch (Exception e) {
                //    //System.out.println("同步抄表数据失败");
                //    //insertLog("同步集团抄表数据", "collectError", "同步的报账id" + mssbill.getId());
                //}
            }
        }

        return map;
    }

    public Map<String, Object> handleMssInterfaceTest(MssAccountbill mssbill, ConcurrentHashMap<Long, BillExecuteState> executeMap) throws Exception {
        Thread.sleep(1000L);
        if (mssbill.getId() == 4290531672128131072L) {
            Thread.sleep(100_1000L);
        }

        if (mssbill.getId() == 4501829639894872064L || mssbill.getId() == 4499933154316005376L) {
            //模拟获取财辅状态成功
            executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
        } else if (mssbill.getId() == 4502541029957853184L) {
            throw new RuntimeException();
        } else {
            executeMap.put(mssbill.getId(), BillExecuteState.GetStatusFail);
        }

        return null;
    }

    public Map<String, Object> handleMssInterface2(MssAccountbill mssbill) throws Exception {
        String writeoffInstanceCode = mssbill.getWriteoffInstanceCode();
        String billId = mssbill.getId().toString();

        //System.out.println("handleMssInterface resXML"+resXML);
        Map<String, Object> map = new HashMap<>();
        map.put("TYPE", "s");
        MssAccountbill bill = new MssAccountbill();
        if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
            bill.setId(mssbill.getId());
            Map<String, Object> item = new HashMap<>();
            item.put("status", "2");

            // 能耗状态 状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,'退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单'

            if ("-4".equals(item.get("status"))) {//等待生成报账单中
                bill.setStatus(-4);
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
            } else if ("-3".equals(item.get("status"))) {//报账单发起失败
                bill.setStatus(-3);
                billMapper.updateForModel(bill);
            } else if ("-1".equals(item.get("status"))) {//报账单删除
//                bill.setStatus(-1);
                // 没有 财辅退单状态 财辅删除 直接对应能耗为财辅退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {


                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
            } else if ("-2".equals(item.get("status"))) {//财辅 退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {
                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
//                //更新抄表数据
//                mssInterfaceServiceImpl.selectWriteoffDetailInfo2(billId+"/update");
            } else if ("2".equals(item.get("status"))) {// 生成报账单成功
                bill.setStatus(3);//生成报帐单
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
                /* if ("2".equals(mssbill.getBizTypeCode()) || "1".equals(mssbill.getBizTypeCode())) {//支付类报账单送集团*/
                boolean budgetflag = false;
/*                List<MssAccountbillitem> mssbillitems=mssAccountbillService.getByid(mssbill.getId()).getItem();
                if (mssbillitems != null && mssbillitems.size() > 0) {
                    for (int i = 0; i <mssbillitems.size() ; i++) {
                        if ("1".equals(mssbillitems.get(i).getBudgetType()))
                            budgetflag=true;
                            break;
                    }
                }*/
                if ("1".equals(mssbill.getBilltype().toString()) || "2".equals(mssbill.getBilltype().toString()) ||
                        "3".equals(mssbill.getBilltype().toString()) || "5".equals(mssbill.getBilltype().toString()) || "6".equals(mssbill.getBilltype().toString()) || "7".equals(mssbill.getBilltype().toString()) || "8".equals(mssbill.getBilltype().toString()) || "9".equals(mssbill.getBilltype().toString()) || "10".equals(mssbill.getBilltype().toString()))
                    budgetflag = true;
                if (budgetflag) {//占成本类报账单送集团 *********/
                    try {
                        boolean senFlag1 = true;
                        boolean senFlag2 = true;
                        senFlag1 = billMapper.selectSendFalg1(billId);
                        senFlag2 = billMapper.selectSendFalg2(billId);
                        log.info(
                                "billid={}同步电表基础信息{}||同步电表用电信息{}",
                                billId, senFlag1 ? "执行" : "不执行", senFlag2 ? "执行" : "不执行"
                        );
                        if (senFlag1) {
                            syncEnergyMeterInfosBybill(mssbill.getId());//同步电表
                        }
                        if (senFlag2) {
                            selectWriteoffDetailInfo(mssbill.getId(), "1");// 同步报账单
                        }

//                        //更新抄表数据
//                        mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId()+"/"+"create");
                        //同步成功更新同步状态 Iresult 为 5 标识推送成功
                        bill.setIresult("5");
                        billMapper.updateForModel(bill);
                        mssInterfaceMapper.updateAmmeterno(mssbill.getId());// 更新电表 标识已经推送

                    } catch (Exception e) {
                        e.printStackTrace();
                        insertLog("同步集团电费化小失败，报账单:【" + mssbill.getId() + "】", "meterInfo", e.getMessage());
                        logger.error("报账单" + mssbill.getId() + " 同步集团电费化小失败：" + e.getMessage());
                    }
                }
            } else if ("3".equals(item.get("status"))) {//已生成SAP凭证
//                bill.setStatus(4);

                bill.setStatus(7);// 完成
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode").toString());// 财辅报账单号
                // 获取凭证信息 并记录
                updatebillYearMonth(bill);
                doEndFlow(bill); // 更新状态
                //updateprepaid(bill);
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "完成");// 走完流程

                //更新抄表数据
                try {
                    mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId() + "/" + "create");
                } catch (Exception e) {
                    //System.out.println("同步抄表数据失败");
                    insertLog("同步集团抄表数据", "collectError", "同步的报账id" + mssbill.getId());
                }
            }
        }

        return map;
    }

    public Map<String, Object> handleMssInterface_test(MssAccountbill mssbill) throws Exception {

        updateprepaid(mssbill);


        Map<String, Object> map = null;


        return map;
    }

    // 获取凭证信息 并记录 年月
    public void updatebillYearMonth(MssAccountbill bill) {
        try {
            Map<String, Object> map = getSapMesByInstanceCode(bill.getWriteoffInstanceCode());
            if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
                LinkedList<Map<String, Object>> items = (LinkedList<Map<String, Object>>) map.get("items");

                if (items != null && items.size() > 0) {
                    Map<String, Object> item = items.get(0);
                    try {
                        bill.setYear(item.get("accountYear").toString());//记录 年月 账期
                        bill.setBizEntryCode(item.get("accountMonth").toString());
                        //System.out.println("sapCertificateCode:" + item.get("sapCertificateCode").toString());
                        bill.setSapCertificateCode(item.get("sapCertificateCode").toString());

                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        bill.setSapRemarkDate(sdf.parse(item.get("sapCertificateDate").toString()));
                    } catch (Exception e) {
                        insertLog("同步集团电费化小失败，报账单:【" + bill.getId() + "】", "meterInfo", e.getMessage());
                        logger.error("报账单" + bill.getId() + " 同步集团电费化小失败：" + e.getMessage());
                    }
                    billMapper.updateForModel(bill);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            insertLog("获取凭证失败，报账单:【" + bill.getId() + "】", "getSapMesByInstanceCode", e.getMessage());
            logger.error("报账单" + bill.getId() + " 获取凭证失败：" + e.getMessage());
            throw new BaseException("获取凭证失败，报账单:【" + bill.getId() + "】");
        }
    }

    // 记录 接口 xml 信息
    private void insertXML(String billId, String sendXML, String resXML, String inftype) {
        MssInterface model = new MssInterface();
        model.setInftype(inftype);
        model.setMiid(IdGenerator.getNextId());
        model.setMssno(billId);
        model.setSendxml(sendXML);
        model.setReturnxml(resXML);
        model.setEnddate(new Date());
        this.insert(model);
    }

    // 流程 送结束
    public synchronized void executeCompleteTask(Long processinstid, User user, String advice) throws Exception {
        WfTask wfTask = new WfTask();
        wfTask.setProcInstId(processinstid.toString());// 流程实例id
        List<WfTask> wfTasks = wfTaskService.selectList(wfTask);
        if (wfTasks != null && wfTasks.size() > 0) {
//            System.out.println(Arrays.toString(wfTasks.toArray()));
            WFModel wfModel = new WFModel();
            wfModel.setProcInstId(processinstid);
            wfModel.setProcTaskId(wfTasks.get(0).getId());
            wfModel.setWfTask(wfTasks.get(0));
            Map<String, Object> map = new HashMap<>();
            map.put("applyUserId", user.getId());
            map.put("procTaskId", wfTasks.get(0).getId());
            map.put("shardKey", wfTasks.get(0).getShardKey());
            map.put("procInstId", processinstid);
            map.put("handlerId", 2);
            map.put("advice", advice);
            map.put("nodeId", WFConstants.NEXT_NODE_送结束);
            wfTaskService.completeTask(user, map);// 走完流程 会回调 uniflowCallBack 的 PROCESS_COMPLETED
        }
    }

    public User setUserForFlow() {
        User user = new User();
        user.setId(0L);
        user.setLoginId("sys");
        user.setUserName("系统自动");
        List<IdNameVO> depts = new ArrayList<>();
        IdNameVO e = new IdNameVO();
        e.setId("0");
        e.setName("系统自动");
        depts.add(e);
        user.setDepartments(depts);
        return user;
    }

    // 终止流程 只允许对拟稿环节待办进行终止 不能送送终止
    public void killFlow(String processinstid, User user) throws Exception {
        if (StringUtils.isNotEmpty(processinstid)) {
            WfTask wfTask = new WfTask();
            wfTask.setProcInstId(processinstid);// 流程实例id
            List<WfTask> wfTasks = wfTaskService.selectList(wfTask);

            if (wfTasks != null && wfTasks.size() > 0) {
                Map<String, Object> param = new HashMap<>();
                param.put("procTaskId", wfTasks.get(0).getId());
                param.put("procInstId", processinstid);
                param.put("shardKey", "0000");
                param.put("stop", "sys");
                WfProcInst wfProcInst = wfProcInstService.get(Long.valueOf(processinstid));
                Long applyUserId = wfProcInst.getApplyUserId();
                user.setId(applyUserId); //只允许终止自己发起的待办
                JSONObject jsonObject = wfProcInstService.stopTask(user, param);
                //System.out.println(jsonObject.toJSONString());
            }
        }
    }

    // 获取 报账人 组织机构信息
    @Override
    public List<ViewOrgSapCostCenter> getFileorgCode(String hrLoginId) throws Exception {
        if (StringUtils.isNotEmpty(hrLoginId)) {
//            List<RUserViewOrg> rUserViewOrgs = mssInterfaceMapper.selectRUserViewOrg(new RUserViewOrg(hrLoginId,
//            null));
//            if (rUserViewOrgs != null && rUserViewOrgs.size() > 0) {
//                String orgId = rUserViewOrgs.get(0).getOrgId();
//                if (!orgId.equals(orgCode)) {
//                    RUserViewOrg rUserViewOrg = new RUserViewOrg(hrLoginId, orgCode);
//                    // 更新组织 - 成本中心 对应关系
//                    mssInterfaceMapper.updateRUserViewOrg(rUserViewOrg);
//                }
//            } else {
//                RUserViewOrg rUserViewOrg = new RUserViewOrg(hrLoginId, orgCode);
//                // 保存组织 - 成本中心 对应关系
//                mssInterfaceMapper.insertRUserViewOrg(rUserViewOrg);
//            }
            // 通过 orgCode(成本中心) 查询 view_org 获取 责任中心列表 A、B
//            if ("sc".equals(deployTo)) {//  四川 通过 dblink 查询
//                DynamicDataSourceContextHolder.setDB("ODSM");
//                list = mssInterfaceMapper.selectOrgSapCenterByOrgCodeSC(orgCode);
//                DynamicDataSourceContextHolder.setDB("ECM");
//            } else {
//                list = mssInterfaceMapper.selectOrgSapCenterByOrgCode(orgCode);
//            }
            // 还是 通过同步全量数据
            // 查询接口
            List<ViewOrgSapCostCenter> list = null;
            String[] orgCode = getWriteoffBaseData(hrLoginId);
            //String[] orgCode = {"1312000026", "", ""}; // 测试
            if (orgCode != null && orgCode.length > 0) {
                if ("sc".equals(deployTo)) {//  四川  查询 表不同
                    list = mssInterfaceMapper.selectOrgSapCenterByOrgCodeSC(orgCode);
                } else {
                    list = mssInterfaceMapper.selectOrgSapCenterByOrgCode(orgCode);
                }
            }
            return list;
        } else {
            throw new BaseException("，查询的 hr 编码为空！");
        }
    }

    // 查询 组织机构
    private String[] getWriteoffBaseData(String hrLoginId) throws Exception {
//        String sname="SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff";
//        if ("sc".equals(deployTo)) {
//            sname="SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff";
//        } else if ("ln".equals(deployTo)) {
//            sname="NH_SI_CF_Integrated_IN_Syn_OP_GetWriteoffBaseData";
//        } else {
//            sname="SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff";
//        }
        //         接口操作 1
        String sendXML = sendXML(hrLoginId + orgEndStr, sn_org);
        // 执行接口
        String returnXML = doMssHttp(sendXML);
        LinkedHashMap<String, Object> map = praseOrgXML(returnXML);
        if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
            LinkedList<Map<String, Object>> item = (LinkedList<Map<String, Object>>) map.get("items");
            Map<String, Object> stringObjectMap = item.get(0);
            LinkedList<Map<String, Object>> orgAndRoles = (LinkedList<Map<String, Object>>)
                    stringObjectMap.get("orgAndRoles");
            if (orgAndRoles != null && orgAndRoles.size() > 0) {
                String[] orgs = new String[orgAndRoles.size()];
                for (int i = 0; i < orgAndRoles.size(); i++) {
                    orgs[i] = orgAndRoles.get(i).get("orgCode").toString();
                }
                return orgs;
            } else {
                return null;
            }
        } else {
            insertXML(hrLoginId, sendXML, returnXML, "getOrg");
            throw new BaseException("未查询到责任中心！");
        }
    }

    //根据 财辅报账单 instanceCode 获取 sap凭证
    @Override
    public Map<String, Object> getSapMesByInstanceCode(String instanceCode) throws Exception {
//        String sname = "SI_CF_Integrated_IN_Syn_OP_GetSapNumber";
//        if ("sc".equals(deployTo)) {
//            sname = "SI_CF_Integrated_IN_Syn_OP_GetSapNumber";
//        } else if ("ln".equals(deployTo)) {
//            sname = "NH_SI_CF_Integrated_IN_Syn_OP_GetSapNumber";
//        } else {
//            sname = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GetWriteoffBaseData";
//        }
        String sendXML = sendSAPXML(instanceCode, sn_sap);
        String returnXML = doMssHttp(sendXML);
        Map<String, Object> map = praseSAPXML(returnXML);
        if (!"S".equals(map.get("TYPE")) && !"s".equals(map.get("TYPE"))) {
            insertXML(instanceCode, sendXML, returnXML, "getSap");// 查询失败记录
        }
        mssSapinfomainService.saveSap(map);// 查询成功 记录 sap 凭证
        return map;
    }

    /*送接口 组装xml参数开始*/

    /// /////////////////////////////////////////////////////////////////////////
    /// ////////////////////////////////////////////////////////////////////////
    // 送 xml 接口 报账单 组装xml参数
    public String sendBillXML(MssAccountbill bill) {
//        String sname = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff";
//        if ("sc".equals(deployTo)) {
//            sname = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff";
//        } else if ("ln".equals(deployTo)) {
//            sname = "NH_SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff";// 辽宁 到时候 修改
//        } else {
//            sname = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff";
//        }
        String baseinfo = setBaseinfo(sn_bill);
        String requestMessage = setRequestMessage(bill);
        String res = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
                "xmlns:ser=\"http://services.allcommonwriteoff.ws.dakar.eshore.com/\">" +
                "<soapenv:Header/>" +
                "<soapenv:Body>" +
                "<ser:OP_AutoCreateWriteoff>" +
                "<I_REQUEST>" +
                baseinfo +
                "<MESSAGE>" +
                "<bizMessage>" +
                requestMessage +
                "</bizMessage>" +
                "</MESSAGE>" +
                "</I_REQUEST>" +
                "</ser:OP_AutoCreateWriteoff>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";
        res = res.replace("null", "");
        return res;
    }

    private String setRequestMessage(MssAccountbill bill) {
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        StringBuilder item = new StringBuilder();
        //bill 8193CDA4-F72C-DB96-E050-10AC802F649A 外围系统主单
        item.append("<otherSystemMainId>").append(bill.getId()).append("</otherSystemMainId>");//外围系统主单
//        item.append("<account>").append(bill.getFillInAccount()).append("</account>");
        // 改为 hrloginid 放到 guid
        String guid = bill.getGuid();
        if ("ln".equals(deployTo)) {
            guid = guid + orgEndStr;
        }
        item.append("<account>").append(guid).append("</account>");
        item.append("<fillInName>").append(bill.getFillInName()).append("</fillInName>");
        item.append("<fillInOrgCode>").append(bill.getFillInCostCenterId()).append("</fillInOrgCode>");
        //可以通过“获取报账组织信息”接口获取
        item.append("<sapCompayCode>").append(bill.getCompanyNameTxt()).append("</sapCompayCode>");
        item.append("<economicItemCode>").append(bill.getAccountCode()).append("</economicItemCode>");//经济事项编码 可由业务提供
        if (bill.getEnergytype() == null)
            item.append("<economicItemName>").append("电费").append("</economicItemName>");//经济事项名称
        if (bill.getEnergytype() != null && bill.getEnergytype().intValue() == 1)
            item.append("<economicItemName>").append("燃料费").append("</economicItemName>");//经济事项名称
        if (bill.getEnergytype() != null && bill.getEnergytype().intValue() == 2)
            item.append("<economicItemName>").append("水费").append("</economicItemName>");//经济事项名称
        if (bill.getEnergytype() != null && bill.getEnergytype().intValue() == 3)
            item.append("<economicItemName>").append("取暖费").append("</economicItemName>");//经济事项名称
        item.append("<paymentType>").append(bill.getPaymentType()).append("</paymentType>");//收支方式
        item.append("<happenDate>").append(bill.getHappenDate()).append("</happenDate>");//费用发生日 YYYY-MM-DD
        item.append("<budgetSet>").append(bill.getBudgetsetname()).append("</budgetSet>");//报账期间 YYYY-MM
        item.append("<bizType>").append(bill.getBizTypeCode()).append("</bizType>");//业务类型 选项：0-列账，1-付款,2-列并付
        if ("0".equals(bill.getBizTypeCode())) {
            item.append("<isPayment>").append("0").append("</isPayment>");//是否付款	非必须	0-否，1-是，2-其他********新增
        } else {
            item.append("<isPayment>").append("1").append("</isPayment>");//是否付款	非必须	0-否，1-是，2-其他********新增
        }
        item.append("<isStaffPayment>").append(bill.getIsStaffPayment()).append("</isStaffPayment>");//是否员工代垫
        double sum = bill.getSum() == null ? 0 : bill.getSum().doubleValue();
        double inputTaxSum = bill.getInputTaxSum() == null ? 0 : bill.getInputTaxSum().doubleValue();
        item.append("<sum>").append(nf.format(sum + inputTaxSum)).append("</sum>");//
        if ("sc".equals(deployTo)) {
            String accountsedate = "-";
            try {
                accountsedate = rBillitemAccountMapper.selectAccountsedate(bill.getId());
            } catch (Exception e) {
            }
            if (!"-".equals(accountsedate))
                item.append("<desc>").append(bill.getAbstractValue() + "【" + BILLNAMES[bill.getBilltype().intValue() - 1] + "】").append("【" + accountsedate + "】").append("</desc>");
            else
                item.append("<desc>").append(bill.getAbstractValue() + "【" + BILLNAMES[bill.getBilltype().intValue() - 1] + "】").append("</desc>");
        } else
            item.append("<desc>").append(bill.getAbstractValue()).append("</desc>");
        item.append("<pickingMode>").append(bill.getPickingMode()).append("</pickingMode>");
        item.append("<invoiceType>").append(bill.getInvoiceType()).append("</invoiceType>");//票据类型
        item.append("<isNeedImage>").append("1").append("</isNeedImage>");//是否需要手工扫描 0-否，1-是
        item.append("<isRealGift>").append(bill.getIsExistKindGift()).append("</isRealGift>");//是否存在实物赠送
        item.append("<businessHappenTimeFlag>").append(bill.getBusihappendtimeflag()).append(
                "</businessHappenTimeFlag>");//业务发生时间点标志
        item.append("<realGiftSum>").append(bill.getKindGiftSum() == null ? 0 :
                nf.format(bill.getKindGiftSum().doubleValue())).append(
                "</realGiftSum>");//是否存在实物赠送
        item.append("<realGiftTaxSum>").append(bill.getKindGiftTaxSum() == null ? 0 :
                nf.format(bill.getKindGiftTaxSum().doubleValue())).append(
                "</realGiftTaxSum>");//实物赠送税额
        item.append("<isInputTax>").append(bill.getIsInputTax()).append("</isInputTax>");//是否涉及进项税转出
        if ("1".equals(bill.getIsInputTax())) {//是否进项税转出为否时，进项税转出税额和进项税转出业务类型不需要传值
            item.append("<inputTaxTurnSum>").append(bill.getInputTaxTurnSum() == null ? 0 :
                    nf.format(bill.getInputTaxTurnSum().doubleValue())).append("</inputTaxTurnSum>");//进项税转出税额
            item.append("<inputTaxTurnBizType>").append(bill.getInputTaxTurnBizType()).append("</inputTaxTurnBizType" +
                    ">");//进项税转出业务类型
        }
        item.append("<isEmergency>").append(bill.getIsEmergency()).append("</isEmergency>");//是否加急
        if (StringUtils.isNotEmpty(bill.getContractno())) {
            item.append("<contractNo>").append(bill.getContractno()).append("</contractNo>");
            item.append("<contractName>").append(bill.getContractName()).append("</contractName>");
        } else {
            item.append("<contractNo/>").append("<contractName/>");
        }
        //1为一单清
        //0和空为非一单清 // 税额拆分到明细当中
        item.append("<singleClearFlag>").append("1").append("</singleClearFlag>");
        String paytype = "1";
        //if (StringUtils.isNotEmpty(bill.getSupplierCode()) && bill.getSupplierCode().startsWith("G")) {//
        // (1-总账、2-供应商、3-客户)
  /*
            paytype = "2";
        } else {
            paytype = "3";
        }*/
        if (StringUtils.isNotEmpty(bill.getSuppliertype())) {// 报账单新增时控制
            if ("1".equals(bill.getSuppliertype())) {
                paytype = "2";
            } else {
                paytype = "3";
            }
        }
/*        if ("sc".equals(deployTo) && "8".equals(bill.getBilltype().toString())) {
            //收款-其他收款，报账明细里面选用途为“经营管理用”的时候，accountType 是传供应商
            if ("6".equals(bill.getPickingMode().toString()) && bill.getItem() != null && bill.getItem().size() > 0
            && "1".equals(bill.getItem().get(0).getUsageId())) {
                paytype = "2";
            } else {
                paytype = "3";//收款 固定为 客户
            }
        }*/
        StringBuilder relateSupplier = new StringBuilder();
        if (StringUtils.isNotEmpty(bill.getSupplierCode())) {
            relateSupplier.append("<relateSuppliers><relateSupplier>");
            relateSupplier.append("<supplierCode>").append(bill.getSupplierCode()).append("</supplierCode>");
            relateSupplier.append("<supplierName>").append(bill.getSupplierName()).append("</supplierName>");
            relateSupplier.append("<accountType>").append(paytype).append("</accountType>");
            relateSupplier.append("<sum>").append(sum + inputTaxSum).append("</sum>");
            // 拆分税后 inputTaxSum invoiceSum 无需填写
//            relateSupplier.append("<inputTaxSum>").append(bill.getInputTaxSum() == null ? 0 : bill.getInputTaxSum()
//            .doubleValue()).append("</inputTaxSum>");//没有找到字段
//            relateSupplier.append("<invoiceSum>").append(bill.getSum() == null ? 0 : bill.getSum().doubleValue())
            //            .append("</invoiceSum>");
            relateSupplier.append("</relateSupplier></relateSuppliers>");
            // 供应商结束
        }

        StringBuilder lineItems = new StringBuilder();//明细
        List<MssAccountbillitem> lineItemlist = bill.getItem();

        if ("ln".equals(deployTo) && "8".equals(bill.getBilltype().toString())) {
            //辽宁收款 U889 不传供应商判断
            for (MssAccountbillitem bitem : lineItemlist) {
                if ("8".equals(bitem.getUsageId())) {
                    relateSupplier = new StringBuilder();
                    break;
                }
            }
        }

        for (MssAccountbillitem bitem : lineItemlist) {
            lineItems.append("<lineItem>");
            lineItems.append("<otherSystemDetailId>").append(bitem.getId()).append("</otherSystemDetailId>");
            if ("1".equals(bitem.getUsageId())) {// 经营管理用	1
                lineItems.append("<usageCode>").append("U165").append("</usageCode>");
            } else if ("2".equals(bitem.getUsageId())) { //支付代垫外单位或员工电费	2
                lineItems.append("<usageCode>").append("U166").append("</usageCode>");
            } else if ("3".equals(bitem.getUsageId())) {//收到外单位或员工还代垫电费	3
                lineItems.append("<usageCode>").append("U167").append("</usageCode>");
            } else if ("4".equals(bitem.getUsageId())) {//职工宿舍用	4
                lineItems.append("<usageCode>").append("U168").append("</usageCode>");
            } else if ("5".equals(bitem.getUsageId())) {//进项税	5
                lineItems.append("<usageCode>").append("U888").append("</usageCode>");
            } else if ("6".equals(bitem.getUsageId())) {//转供电电费	6
                lineItems.append("<usageCode>").append("U402").append("</usageCode>");
            } else if ("7".equals(bitem.getUsageId())) {//U514 包干电费	7
                lineItems.append("<usageCode>").append("U514").append("</usageCode>");
            } else if ("8".equals(bitem.getUsageId())) {//U889 进项税（集成）
                lineItems.append("<usageCode>").append("U889").append("</usageCode>");
            } else if ("11".equals(bitem.getUsageId())) {//U889 进项税（集成）
                lineItems.append("<usageCode>").append("U647").append("</usageCode>");
            } else if ("12".equals(bitem.getUsageId())) {//U889 进项税（集成）
                lineItems.append("<usageCode>").append("U649").append("</usageCode>");
            } else if ("13".equals(bitem.getUsageId())) {//U889 进项税（集成）
                lineItems.append("<usageCode>").append("U999").append("</usageCode>");
            } else if ("14".equals(bitem.getUsageId())) {//U889 进项税（集成）
                lineItems.append("<usageCode>").append("U648").append("</usageCode>");
            } else if ("20".equals(bitem.getUsageId())) {//U889 进项税（集成）
                lineItems.append("<usageCode>").append("U179").append("</usageCode>");
            } else if ("30".equals(bitem.getUsageId())) {// U169 支付代垫外单位或员工水费
                lineItems.append("<usageCode>").append("U169").append("</usageCode>");
            } else if ("31".equals(bitem.getUsageId())) {// U170 收到外单位或员工还代垫水费
                lineItems.append("<usageCode>").append("U170").append("</usageCode>");
            } else if ("32".equals(bitem.getUsageId())) {// U171 代征污水处理费
                lineItems.append("<usageCode>").append("U171").append("</usageCode>");
            } else if ("33".equals(bitem.getUsageId())) {// U172 代征垃圾处理费
                lineItems.append("<usageCode>").append("U172").append("</usageCode>");
            } else {//
                lineItems.append("<usageCode>").append("U165").append("</usageCode>");
            }
            if ("sc".equals(deployTo)) {
                lineItems.append("<orderNo>").append(bitem.getOrderNo()).append("</orderNo>");
                if (bitem.getUsageId().equals("2") && !("11".equals(bill.getBilltype().toString())))   //代垫增加统御科目210128
                //                   if(!bitem.getReconciliationAccountCode().isEmpty())
                {
                    lineItems.append("<preAccountCode>").append(bitem.getReconciliationAccountCode()).append(
                            "</preAccountCode>");
                    lineItems.append("<preAccountName>").append(bitem.getReconciliationAccountName()).append(
                            "</preAccountName>");
                }
                if ("11".equals(bill.getBilltype().toString())) {
/*                    lineItems.append("<preAccountCode>").append(bitem.getReconciliationAccountCode()).append
("</preAccountCode>")
                    lineItems.append("<preAccountName>").append(bitem.getReconciliationAccountName()).append
                ("</preAccountName>");*/
                    if (StringUtils.isNotEmpty(bitem.getDebitAccountCode())) {
                        //(1-总账、2-供应商、3-客户) 此处固定为 3
                        if (StringUtils.isNotEmpty(bitem.getReservedOne()))
                            lineItems.append("<debitAccountType>").append(bitem.getReservedOne()).append(
                                    "</debitAccountType>");
                        else {
                            if (bitem.getDebitAccountCode().length() > 0 && bitem.getDebitAccountCode().charAt(0) == 'G') {
                                lineItems.append("<debitAccountType>").append("2").append("</debitAccountType>");
                            } else {
                                lineItems.append("<debitAccountType>").append("3").append("</debitAccountType>");
                            }

                        }
                        lineItems.append("<debitAccountCode>").append(bitem.getDebitAccountCode()).append(
                                "</debitAccountCode>");
                        lineItems.append("<debitAccountName>").append(bitem.getDebitAccountName()).append(
                                "</debitAccountName>");
                    }
                }
            }
            lineItems.append("<usageName>").append(bitem.getUsageName()).append("</usageName>");
            lineItems.append("<budgetType>").append(bitem.getBudgetType()).append("</budgetType>");
            if ("ln".equals(deployTo) && "8".equals(bill.getBilltype().toString()) && "8".equals(bitem.getUsageId())) {//辽宁收款 U889进项税（集成）
                lineItems.append("<priceSum>").append(bitem.getTaxAdjustSum() == null ? 0 :
                        nf.format(bitem.getTaxAdjustSum().doubleValue())).append("</priceSum>");
                lineItems.append("<inputTaxSum>").append("0").append("</inputTaxSum>");
            } else {
                lineItems.append("<priceSum>").append(bitem.getSum() == null ? 0 :
                        nf.format(bitem.getSum().doubleValue())).append(
                        "</priceSum>");
                lineItems.append("<inputTaxSum>").append(bitem.getTaxAdjustSum() == null ? 0 :
                        nf.format(bitem.getTaxAdjustSum().doubleValue())).append("</inputTaxSum>");
            }
            lineItems.append("<sum>").append(nf.format(bitem.getTaxAdjustSum() == null ? 0 :
                    bitem.getTaxAdjustSum().doubleValue() + (bitem.getSum() == null ? 0 : bitem.getSum().doubleValue()))).append("</sum>");
            lineItems.append("<desc>").append(bitem.getAbstractValue()).append("</desc>");
            //  CW0686  转供电  CW1000 能耗费  CW5500 进项税 成本 23年修改为1011 24年 燃料费 CW1001  水费 CW1012  取暖费 CW1013
            if ("1000".equals(bitem.getBudgetItemId())) {// 生产用	1budgetItemId
                if (bill.getEnergytype() == null) {
                    lineItems.append("<budgetItemCode>").append("CW1011").append("</budgetItemCode>");
                    lineItems.append("<budgetItemName>").append("能源费").append("</budgetItemName>");
                }
                if (bill.getEnergytype() != null && bill.getEnergytype().intValue() == 1) {
                    lineItems.append("<budgetItemCode>").append("CW1001").append("</budgetItemCode>");
                    lineItems.append("<budgetItemName>").append("燃料费").append("</budgetItemName>");
                }
                if (bill.getEnergytype() != null && bill.getEnergytype().intValue() == 2) {
                    lineItems.append("<budgetItemCode>").append("CW1012").append("</budgetItemCode>");
                    lineItems.append("<budgetItemName>").append("水费").append("</budgetItemName>");
                }
                if (bill.getEnergytype() != null && bill.getEnergytype().intValue() == 3) {
                    lineItems.append("<budgetItemCode>").append("CW1013").append("</budgetItemCode>");
                    lineItems.append("<budgetItemName>").append("取暖费").append("</budgetItemName>");
                }
            } else if ("1145".equals(bitem.getBudgetItemId())) { //管理用
                lineItems.append("<budgetItemCode>").append("CW1145").append("</budgetItemCode>");
                lineItems.append("<budgetItemName>").append("能源费").append("</budgetItemName>");
            } else if ("2000".equals(bitem.getBudgetItemId())) {//铁塔用 3
                lineItems.append("<budgetItemCode>").append("CW0686").append("</budgetItemCode>");
                lineItems.append("<budgetItemName>").append("转供电—铁塔公司").append("</budgetItemName>");
            } else {
                lineItems.append("<budgetItemCode>").append("CW5500").append("</budgetItemCode>");
                lineItems.append("<budgetItemName>").append("进项税 成本").append("</budgetItemName>");
            }

            lineItems.append("<budgetOrgCode>").append(bitem.getResponseCenterCode()).append("</budgetOrgCode>");
            lineItems.append("<budgetOrgName>").append(bitem.getResponseCenterName()).append("</budgetOrgName>");
            lineItems.append("<sapCostCenterCode>").append(bitem.getCostCenterCode()).append("</sapCostCenterCode>");
            lineItems.append("<sapCostCenterName>").append(bitem.getCostCenterName()).append("</sapCostCenterName>");
            lineItems.append("<count>").append(bitem.getAmount()).append("</count>");
            lineItems.append("<unit>度</unit>");//增加计量单位度
            lineItems.append("<price>").append(bitem.getPrice() == null ? 0 :
                    nf.format(bitem.getPrice().doubleValue())).append("</price>");
/*            if (StringUtils.isNotEmpty(bitem.getDebitAccountCode())) {
                //(1-总账、2-供应商、3-客户) 此处固定为 3
                lineItems.append("<debitAccountType>").append("3").append("</debitAccountType>");
                lineItems.append("<debitAccountCode>").append(bitem.getDebitAccountCode()).append
                ("</debitAccountCode>");
                lineItems.append("<debitAccountName>").append(bitem.getDebitAccountName()).append
                ("</debitAccountName>");
            }*/
            lineItems.append("</lineItem>");
        }
        StringBuilder payMentItems = new StringBuilder();//外部收款人
        List<MssAccountbillpayinfo> payinfo = bill.getPayinfo();// 供应商
        //是否员工代垫（0为否、1为是） 是员工代垫 不传银行信息 避免接口错误
        if (payinfo != null && payinfo.size() > 0 && "0".equals(bill.getIsStaffPayment())) {
            payMentItems.append("<payMentItems>");
            for (MssAccountbillpayinfo pay : payinfo) {
                payMentItems.append("<payMentItem>");
                payMentItems.append("<employeeBankAccount>").append(pay.getEmployeebankac()).append(
                        "</employeeBankAccount>");
                payMentItems.append("<employeeName>").append(pay.getEmployeename()).append("</employeeName>");
                // 因为 bankAddress 不存在 此处 为支行全称 前 四个字 总行简称
                payMentItems.append("<bank>").append(pay.getBank().substring(0, 4)).append("</bank>");
                //避免数据同步异常造成的接口异常 此处不传值 pay.getBankcode()
                if (StringUtils.isNotEmpty(pay.getRowno()) && pay.getRowno().matches("^[0-9]*$")) // 数据因为数据同步问题 截取 Rowno
                    payMentItems.append("<bankCode>").append(pay.getRowno().substring(0, 3)).append
                            ("</bankCode" + ">");
                //payMentItems.append("<bankCode>").append(pay.getRowno()).append("</bankCode" + ">");
                payMentItems.append("<payeeCode>").append(pay.getPayeecode()).append("</payeeCode>");
//                String paytype = "1";
//                if (pay.getPayeetype().equals("K")) {//(1-总账、2-供应商、3-客户)
//                    paytype = "2";
//                } else if (pay.getPayeetype().equals("D")) {
//                    paytype = "3";
//                }
//                1-对外对公，3-对外对私,2-对内
                if (StringUtils.isNotEmpty(pay.getAccountname())) {
                    String replaceAll = pay.getAccountname().replaceAll("\r|\n", "");
                    if ("对公".equals(replaceAll))
                        payMentItems.append("<accountType>").append(1).append("</accountType>");
                    else if ("对私".equals(replaceAll))
                        payMentItems.append("<accountType>").append(3).append("</accountType>");
                    else //默认为对公
                        payMentItems.append("<accountType>").append(1).append("</accountType>");
                } else //默认为对公
                    payMentItems.append("<accountType>").append(1).append("</accountType>");
                payMentItems.append("<payeeType>").append(pay.getPayeetype()).append("</payeeType>");
                payMentItems.append("<rowno>").append(pay.getRowno()).append("</rowno>");
                // 因为 bankAddress 不存在 此处 为支行全称
                payMentItems.append("<bankAddress>").append(pay.getBank()).append("</bankAddress>");
                payMentItems.append("<province>").append(pay.getProvince()).append("</province>");
                payMentItems.append("<city>").append(pay.getCity()).append("</city>");
                payMentItems.append("<sum>").append(pay.getSum() == null ? 0 : nf.format(pay.getSum().doubleValue())).append("</sum>");
                payMentItems.append("</payMentItem>");
            }
            payMentItems.append("</payMentItems>");
        }
        StringBuilder openItemPickings = new StringBuilder();// 挑对
        List<MssAccountclearitem> clearitems = bill.getClearitem();
        String sapCostCenterCode = bill.getItem().get(0).getCostCenterCode();
        String profitCenterGroupCode = "";
        if (StringUtils.isNotEmpty(sapCostCenterCode)) {
            profitCenterGroupCode = sapCostCenterCode.substring(0, 5);
        }
        if (clearitems != null && clearitems.size() > 0) {
            openItemPickings.append("<openItemPickings>");
            for (MssAccountclearitem clearitem : clearitems) {
                openItemPickings.append("<openItemPicking>");
                openItemPickings.append("<sapCertificateCode>").append(clearitem.getSapcertificatecode()).append(
                        "</sapCertificateCode>");
                openItemPickings.append("<sapItemMun>").append(clearitem.getSapitemmun()).append("</sapItemMun>");
                openItemPickings.append("<tYear>").append(clearitem.getTyear()).append("</tYear>");
                openItemPickings.append("<companyCode>").append(clearitem.getCompanycode()).append("</companyCode>");
                openItemPickings.append("<accountCode>").append(clearitem.getAccountcode()).append("</accountCode>");
//                if (StringUtils.isNotEmpty(clearitem.getAccountcode()) && !clearitem.getAccountcode().startsWith
//                ("G")) {//(1-总账、2-供应商、3-客户)
//                    // 是G开头的不一定是供应商
//                    paytype = "3";// 不是G开头的一定是客户
//                }
                //选输(1-总账、2-供应商、3-客户),有科目时必须
                openItemPickings.append("<accountType>").append(paytype).append("</accountType>");
                openItemPickings.append("<otherSystemDetailId>").append(clearitem.getOthersystemdetailid()).append(
                        "</otherSystemDetailId>");
                openItemPickings.append("<pickingSum>").append(clearitem.getPickingsum() == null ? 0 :
                        nf.format(clearitem.getPickingsum().doubleValue())).append("</pickingSum>");
                //收款、调账、挂账支付 报账金额送挑对金额
                if ("3".equals(bill.getBilltype().toString()) || "8".equals(bill.getBilltype().toString()) || "11".equals(bill.getBilltype().toString()))
                    openItemPickings.append("<writeoffSum>").append(clearitem.getPickingsum() == null ? 0 :
                            nf.format(clearitem.getPickingsum().doubleValue())).append("</writeoffSum>");
                else
                    openItemPickings.append("<writeoffSum>").append(clearitem.getWriteoffsum() == null ? 0 :
                            nf.format(clearitem.getWriteoffsum().doubleValue())).append("</writeoffSum>");
                openItemPickings.append("<inputTaxSum>").append(clearitem.getInputtaxsum() == null ? 0 :
                        nf.format(clearitem.getInputtaxsum().doubleValue())).append("</inputTaxSum>");
                openItemPickings.append("<isLastWriteoff>").append(clearitem.getIslastwriteoff()).append(
                        "</isLastWriteoff>");
                openItemPickings.append("<profitCenterGroupCode>").append(profitCenterGroupCode).append(
                        "</profitCenterGroupCode>");
                openItemPickings.append("</openItemPicking>");
            }
            openItemPickings.append("</openItemPickings>");
        }

        String beforeHandSum = "0";//事前审批金额
        String processSuffixName = null;
        if ("ln".equals(deployTo)) // 辽宁 新能耗标识
            processSuffixName = "新能耗费集成";
        if ("sc".equals(deployTo)) {
            //省本部流程，送4400-FSJJSX_13，分公司流程才送FSJJSX_13
            if ("1000922".equals(bill.getCompanyCode())) {
                processCode = "4400-FSJJSX_13";
            } else {
                processCode = "FSJJSX_13";
            }
        }
        /*<![CDATA[*/
        String res = "<![CDATA[<requestMessage>" +
                "<processCode>" + processCode + "</processCode>" +
                "<processSuffixName>" + processSuffixName + "</processSuffixName>" +
                "<writeoffItems>" +
                "<item>" +
                item.toString() +
                relateSupplier.toString() +
                "<lineItems>" +
                lineItems.toString() +
                "</lineItems>" +
                payMentItems.toString() +
                openItemPickings.toString() +
                "</item>" +
                "</writeoffItems>" +
                "<beforeAuditCode/>" +
                "<beforeHandSum>" + beforeHandSum + "</beforeHandSum>" +
                "</requestMessage>]]>";
        res = res.replace("null", "");
        return res;
    }

    // 送 xml 接口 组织机构 组装xml参数
    // SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GetWriteoffBaseData
    private String sendXML(String account, String SERVICENAME) {
        String baseinfo = setBaseinfo(SERVICENAME);
        String OP_GetWriteoffBaseData = "<ser:OP_GetWriteoffBaseData>" +
                "<I_REQUEST>" +
                baseinfo +
                "<MESSAGE>" +
                "<bizMessage>" +
                "<![CDATA[<requestMessage>" +
                "<items>" +
                "<item>" +
                "<account>" + account + "</account>" +
                "</item>" +
                "</items>" +
                "</requestMessage>]]>" +
                "</bizMessage>" +
                "</MESSAGE>" +
                "</I_REQUEST>" +
                "</ser:OP_GetWriteoffBaseData>";
        return GetWriteoffBaseData(OP_GetWriteoffBaseData);
    }

    // 获取 报账单状态 组装xml参数
    public String sendwriteoffInstanceCodeXML(String writeoffInstanceCode, String billId, String SERVICENAME) {
        if (StringUtils.isEmpty(SERVICENAME)) {
            SERVICENAME = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GetWriteoffBaseData";
        }
        String baseinfo = setBaseinfo(SERVICENAME);
        String ser = "OP_GetWriteoffBaseData";
        if ("sc".equals(deployTo)) {
            ser = "OP_GainWriteoffInstStatus";
        } else if ("ln".equals(deployTo)) {
//            ser = "NH_SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";// 辽宁 到时候 修改
            ser = "OP_GainWriteoffInstStatus";// 辽宁 到时候 修改
        } else {
            ser = "OP_GainWriteoffInstStatus";
        }
        String OP_GetWriteoffBaseData = "<ser:" + ser + ">" +
                "<I_REQUEST>" +
                baseinfo +
                "<MESSAGE>" +
                "<bizMessage>" +
                "<![CDATA[<requestMessage>" +
                "<items>" +
                "<item>" +
                "<writeoffInstanceCode>" + writeoffInstanceCode + "</writeoffInstanceCode>" +// 报账单号
                "<otherSystemMainId>" + billId + "</otherSystemMainId>" +
                "</item>" +
                "</items>" +
                "</requestMessage>]]>" +
                "</bizMessage>" +
                "</MESSAGE>" +
                "</I_REQUEST>" +
                "</ser:" + ser + ">";
        return GetWriteoffBaseData(OP_GetWriteoffBaseData);
    }

    // 获取 SAP 	获取sap标准凭证信息 组装xml参数 instanceCode 报账单号
    public String sendSAPXML(String instanceCode, String SERVICENAME) {
        if (StringUtils.isEmpty(SERVICENAME)) {
            SERVICENAME = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GetWriteoffBaseData";
        }
        String ser = "OP_GetSapNumber";
        if ("sc".equals(deployTo)) {
            ser = "OP_GetSapNumber";
        } else if ("ln".equals(deployTo)) {
            ser = "OP_GetSapNumber";// 辽宁的到时候 修改
        } else {
            ser = "OP_GetWriteoffBaseData";
        }
        String baseinfo = setBaseinfo(SERVICENAME);
        String OP_GetWriteoffBaseData = "<ser:" + ser + ">" +
                "<I_REQUEST>" +
                baseinfo +
                "<MESSAGE>" +
                "<bizMessage>" +
                "<![CDATA[<requestMessage>" +
                "<items>" +
                "<item>" +
                "<instanceCode>" + instanceCode + "</instanceCode>" +//报账单号
                "</item>" +
                "</items>" +
                "</requestMessage>]]>" +
                "</bizMessage>" +
                "</MESSAGE>" +
                "</I_REQUEST>" +
                "</ser:" + ser + ">";
        return GetWriteoffBaseData(OP_GetWriteoffBaseData);
    }

    private String GetWriteoffBaseData(String OP_GetWriteoffBaseData) {
        String res = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
                "xmlns:ser=\"http://services.allcommonwriteoff.ws.dakar.eshore.com/\">" +
                "<soapenv:Header/>" +
                "<soapenv:Body>" +
                OP_GetWriteoffBaseData +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";
        res = res.replace("null", "");
        return res;
    }

    private String setBaseinfo(String SERVICENAME) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String time = sdf.format(new Date());
        String MSGID = S_SYSTEM + "_" + time + "_" + (int) ((Math.random() * 9 + 1) * 10000);
        String res = "<BASEINFO>" +
                "<PMSGID/>" +
                "<MSGID>" + MSGID + "</MSGID>" +
                "<RETRY>" + RETRY + "</RETRY>" +
                "<SENDTIME>" + time + "</SENDTIME>" +
                "<SERVICENAME>" + SERVICENAME + "</SERVICENAME>" +
                "<S_PROVINCE>" + S_PROVINCE + "</S_PROVINCE>" +
                "<S_SYSTEM>" + S_SYSTEM + "</S_SYSTEM>" +
                "<T_PROVINCE>" + T_PROVINCE + "</T_PROVINCE>" +
                "<T_SYSTEM>" + T_SYSTEM + "</T_SYSTEM>" +
                "</BASEINFO>";
        res = res.replace("null", "");
        return res;
    }

    /*解析 xml 结束*/
    ////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////

    /*送接口 组装参数结束*/

    /// /////////////////////////////////////////////////////////////////////////
    /// ////////////////////////////////////////////////////////////////////////
    //    http://136.127.56.5:9001/mssproxy 测试地址
    public String doMssHttp(String soapXml) throws Exception {
        if ("sc".equals(deployTo)) {
            Map<String, Object> param = new HashMap<>();
            param.put("url", SOAPURL);
            param.put("params", soapXml);
//            RestTemplate rest = new RestTemplate();
            RestTemplate rest = new RestTemplate();
            rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(CHARSET)));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
            //System.out.println("httpforwardUrl doMss="+httpforwardUrl);
            logger.info("httpforwardUrl doMss=" + httpforwardUrl);
            return rest.postForObject(httpforwardUrl + "doMss", requestEntity, String.class);
        } else {
            String result = mssClient.callCF(SOAPURL, soapXml);// 测试地址
            result = StringEscapeUtils.unescapeHtml(result);
            //        result = result.replaceAll(">/n", ">");
            return result;
        }

    }

    public String doMssHttp2(String soapXml) throws Exception {
        if ("ln".equals(deployTo)) {
            String result = mssClient.callCF2(SOAPURLTESTFORLN, soapXml);// 测试地址
            result = StringEscapeUtils.unescapeHtml(result);
            //        result = result.replaceAll(">/n", ">");
            return result;
        }
        return "当前省份不是辽宁";

    }

    /*解析 xml 开始*/

    /// /////////////////////////////////////////////////////////////////////////
    /// ////////////////////////////////////////////////////////////////////////
    // 解析 报账xml
    public Map<String, Object> prasebillXML(String returnXML) throws Exception {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        Element responseMessage = commonPraseXML(returnXML, map, "OP_AutoCreateWriteoffResponse");
        Element TYPE = responseMessage.element("TYPE");
        map.put("TYPE", TYPE.getStringValue());
        if (TYPE != null && "S".equals(TYPE.getStringValue())) {
            List<Element> items = responseMessage.element("items").elements();
            List<Map<String, Object>> itemmaps = new LinkedList<>();
            for (Element item : items) {
                itemmaps.add(setMap(item));
            }
            map.put("items", itemmaps);
        } else {
            Element items = responseMessage.element("items");
            if (items != null) {
                List<Element> itemss = items.elements();
                if (itemss != null) {
                    List<Map<String, Object>> itemmaps = new LinkedList<>();
                    for (Element item : itemss) {
                        itemmaps.add(setMap(item));
                    }
                    map.put("items", itemmaps);
                }
            }
            if (responseMessage.element("errorMsg") != null) {
                map.put("errorMsg", responseMessage.element("errorMsg").getTextTrim());
            } else {
                map.put("errorMsg", "");
            }
        }
        return map;
    }

    // 解析 OP_GetWriteoffBaseDataResponse xml (查询类型的接口)
    public Map<String, Object> praseOP_GetWriteoffBaseDataResponse(String returnXML) throws Exception {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        Element responseMessage = commonPraseXML(returnXML, map, "OP_GetWriteoffBaseDataResponse");
        Element TYPE = responseMessage.element("TYPE");
        map.put("TYPE", TYPE.getStringValue());
        if (TYPE != null && "S".equals(TYPE.getStringValue())) {
            List<Element> items = responseMessage.element("items").elements();
            List<Map<String, Object>> itemmaps = new LinkedList<>();
            for (Element item : items) {
                itemmaps.add(setMap(item));
            }
            map.put("items", itemmaps);
        } else {
            if (responseMessage.element("errorMsg") != null) {
                map.put("errorMsg", responseMessage.element("errorMsg").getTextTrim());
            } else {
                map.put("errorMsg", "");
            }
        }
        return map;
    }

    // 四川 解析 报账状态 OP_GainWriteoffInstStatusResponse xml (查询类型的接口)
    public Map<String, Object> praseOP_GainWriteoffInstStatus(String returnXML) throws Exception {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        Element responseMessage = commonPraseXML(returnXML, map, "OP_GainWriteoffInstStatusResponse");
        Element TYPE = responseMessage.element("TYPE");
        map.put("TYPE", TYPE.getStringValue());
        if (TYPE != null && "S".equals(TYPE.getStringValue())) {
            List<Element> items = responseMessage.element("items").elements();
            List<Map<String, Object>> itemmaps = new LinkedList<>();
            for (Element item : items) {
                itemmaps.add(setMap(item));
            }
            map.put("items", itemmaps);
        } else {
            if (responseMessage.element("errorMsg") != null) {
                map.put("errorMsg", responseMessage.element("errorMsg").getTextTrim());
            } else {
                map.put("errorMsg", "");
            }
        }
        return map;
    }

    // 解析 praseSAPXML 获取sap凭证 OP_GetWriteoffBaseDataResponse xml (查询类型的接口)
    public Map<String, Object> praseSAPXML(String returnXML) throws Exception {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        String name = "OP_GetSapNumberResponse";
        if ("sc".equals(deployTo)) {
            name = "OP_GetSapNumberResponse";
        } else if ("ln".equals(deployTo)) {
            name = "OP_GetSapNumberResponse"; // 辽宁 记得修改
        } else {
            name = "OP_GetWriteoffBaseDataResponse";
        }
        Element responseMessage = commonPraseXML(returnXML, map, name);
        Element TYPE = responseMessage.element("TYPE");
        map.put("TYPE", TYPE.getStringValue());
        if (TYPE != null && "S".equals(TYPE.getStringValue())) {
            List<Element> items = responseMessage.element("items").elements();
            List<Map<String, Object>> itemmaps = new LinkedList<>();
            for (Element item : items) {
                LinkedHashMap<String, Object> itemMap = setMap(item);
                List<Element> certificateDetailItems = item.element("certificateDetailItems").elements();
                List<Map<String, Object>> detailItemmaps = new LinkedList<>();
                for (Element detailItem : certificateDetailItems) {
                    detailItemmaps.add(setMap(detailItem));
                }
                itemMap.put("certificateDetailItems", detailItemmaps);
                itemmaps.add(itemMap);

            }
            map.put("items", itemmaps);
        } else {
            if (responseMessage.element("errorMsg") != null) {
                map.put("errorMsg", responseMessage.element("errorMsg").getTextTrim());
            } else {
                map.put("errorMsg", "");
            }
        }
        return map;
    }

    // 解析 接口返回数据 xxxxxx@LN  OP_GetWriteoffBaseDataResponse 根据账号 查询成本中心
    //SERVICENAME SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff
    private LinkedHashMap<String, Object> praseOrgXML(String returnXML) throws Exception {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        Element responseMessage = commonPraseXML(returnXML, map, "OP_GetWriteoffBaseDataResponse");
        Element TYPE = responseMessage.element("TYPE");
        map.put("TYPE", TYPE.getStringValue());
        if (TYPE != null && "S".equals(TYPE.getStringValue())) {
            List<Element> items = responseMessage.element("items").elements();
            List<Map<String, Object>> itemmaps = new LinkedList<>();
            for (Element item : items) {
                LinkedHashMap<String, Object> itemMap = setMap(item);
                List<Element> orgAndRoles = item.element("orgAndRoles").elements();
                List<Map<String, Object>> orgAndRolesmaps = new LinkedList<>();
                for (Element orgAndRole : orgAndRoles) {
                    orgAndRolesmaps.add(setMap(orgAndRole));
                }
                itemMap.put("orgAndRoles", orgAndRolesmaps);
                itemmaps.add(itemMap);
            }
            map.put("items", itemmaps);
        } else {
            if (responseMessage.element("errorMsg") != null) {
                map.put("errorMsg", responseMessage.element("errorMsg").getTextTrim());
            } else {
                map.put("errorMsg", "");
            }
        }
        return map;
    }

    // 公用 base 解析方法
    private Element commonPraseXML(String returnXML, LinkedHashMap<String, Object> map, String WriteoffResponse) throws DocumentException {
        Element envelope = DocumentHelper.parseText(returnXML).getRootElement();

        Element body = envelope.element("Body");
        Element OP_AutoCreateWriteoffResponse = body.element(WriteoffResponse);
        //OP_GetWriteoffBaseDataResponse // 获取组织机构解析 获取报账单状态解析
        //OP_AutoCreateWriteoffResponse // 报账单送财辅 解析
        Element E_RESPONSE = OP_AutoCreateWriteoffResponse.element("E_RESPONSE");

        Element BASEINFO = E_RESPONSE.element("BASEINFO");
        map.put("BASEINFO", setMap(BASEINFO));

        Element bizMessage = E_RESPONSE.element("MESSAGE").element("bizMessage");
        //获取 CDATA 数据
        String bizMessagexml = bizMessage.getTextTrim();
        String responseMessagexml = bizMessagexml.replace("<![CDATA[", "");
        responseMessagexml = bizMessagexml.replace("]]>", "");
        Document dom = DocumentHelper.parseText(responseMessagexml);
        return dom.getRootElement();
    }

    // 遍历所有 子节点
    private LinkedHashMap<String, Object> setMap(Element BASEINFO) {
        LinkedHashMap<String, Object> baseinfo = new LinkedHashMap<>();
        List<Element> listElement = BASEINFO.elements();
        if (listElement != null) {
            for (Element e : listElement) {//遍历所有一级子节点
//            System.out.println(e.getName() + ":" + e.getTextTrim());
                baseinfo.put(e.getName(), e.getTextTrim());
            }
        }
        return baseinfo;
    }

    public void getNodes(Element node) {
        //当前节点的名称、文本内容和属性
        System.out.println("当前节点名称：" + node.getName());
        System.out.println("当前节点的内容：" + node.getTextTrim());
        List<Attribute> listAttr = node.attributes();//当前节点的所有属性的list
        for (Attribute attr : listAttr) {//遍历当前节点的所有属性
            String name = attr.getName();//属性名称
            String value = attr.getValue();//属性的值
            System.out.println("属性名称：" + name + "属性值：" + value);
        }
        //递归遍历当前节点所有的子节点
        List<Element> listElement = node.elements();//所有一级子节点的list
        for (Element e : listElement) {//遍历所有一级子节点
            getNodes(e);//递归
        }
    }


    /**
     * 能耗系统同步集成报账单关联的电表相关信息。包括新增、修改、作废。
     */
    public String selectWriteoffDetailInfo(Long billId, String type) throws Exception {
        String s = "";
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        long startTime = System.currentTimeMillis();
        MssAccountbill mssAccountbill = billMapper.selectByPrimaryKey(billmap);
        if (mssAccountbill != null /*&& ("2".equals(mssAccountbill.getBizTypeCode()) || "1".equals(mssAccountbill
        .getBizTypeCode()))*/) {//支付类报账单送集团
            List<WriteoffInfo> billList = new ArrayList<>();
            List<WriteoffDetailInfo> list = null;
            List<WriteoffDetailInfo> listhis = null;//增加删除电表编号修改
            WriteoffInfo writeoffInfo = new WriteoffInfo();
            writeoffInfo.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfo.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
//            writeoffInfo.setType("1");// 1 新增, 3作废
            writeoffInfo.setType(type);
            writeoffInfo.setPickingMode(mssAccountbill.getPickingMode());
            // 查两个台账表 用uion all连接

            if ("8".equals(mssAccountbill.getBilltype().toString()))
                list = mssInterfaceMapper.selectWriteoffDetailInforecovery(billId);
            else
                list = mssInterfaceMapper.selectWriteoffDetailInfo(billId);
            doAccountidcWriteoffDetailInfo(list, billId);//处理IDC设备耗电量拆分
            WriteoffInfo writeoffInfohis = new WriteoffInfo(); //增加删除电表编号修改
            writeoffInfohis.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfohis.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
            writeoffInfohis.setType("3");// 1 新增, 3作废 固定位作废
            writeoffInfohis.setPickingMode(mssAccountbill.getPickingMode());

/*            if ("8".equals(mssAccountbill.getBilltype().toString()))
                listhis = mssInterfaceMapper.selectWriteoffDetailInforecoveryhis(billId);
            else
                listhis = mssInterfaceMapper.selectWriteoffDetailInfohis(billId);*/
            if ((list != null && list.size() > 0) || (listhis != null && listhis.size() > 0)) {
                //辽宁 收款两条明细 去重
//                List<WriteoffDetailInfo> listnew = distinctWriteoffs(list);
//                writeoffInfo.setWriteoffDetailInfos(listnew);
                if ((list != null && list.size() > 0)) {
                    writeoffInfo.setWriteoffDetailInfos(list);
                    billList.add(writeoffInfo);
                }
                if (listhis != null && listhis.size() > 0) {
                    writeoffInfohis.setWriteoffDetailInfos(listhis);
                    billList.add(writeoffInfohis);
                }
                s = mssJsonClient.syncWriteoffInfos(billList, billId);

            }
        }
        long endTime = System.currentTimeMillis();
        log.info("selectWriteoffDetailInfo runtime:{}", endTime - startTime);

        return s;
    }

    public String selectWriteoffDetailInfoAddPcids(Long billId, String type, List<String> pcids) throws Exception {
        String s = "";
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        long startTime = System.currentTimeMillis();
        MssAccountbill mssAccountbill = billMapper.selectByPrimaryKey(billmap);
        if (mssAccountbill != null /*&& ("2".equals(mssAccountbill.getBizTypeCode()) || "1".equals(mssAccountbill
        .getBizTypeCode()))*/) {//支付类报账单送集团
            List<WriteoffInfo> billList = new ArrayList<>();
            List<WriteoffDetailInfo> list = null;
            List<WriteoffDetailInfo> listhis = null;//增加删除电表编号修改
            WriteoffInfo writeoffInfo = new WriteoffInfo();
            writeoffInfo.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfo.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
//            writeoffInfo.setType("1");// 1 新增, 3作废
            writeoffInfo.setType(type);
            writeoffInfo.setPickingMode(mssAccountbill.getPickingMode());
            // 查两个台账表 用uion all连接

            if ("8".equals(mssAccountbill.getBilltype().toString()))
                list = mssInterfaceMapper.selectWriteoffDetailInforecoveryAddPcids(billId);
            else
                list = mssInterfaceMapper.selectWriteoffDetailInfoAddPcids(billId);
            doAccountidcWriteoffDetailInfo(list, billId);//处理IDC设备耗电量拆分
            WriteoffInfo writeoffInfohis = new WriteoffInfo(); //增加删除电表编号修改
            writeoffInfohis.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfohis.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
            writeoffInfohis.setType("3");// 1 新增, 3作废 固定位作废
            writeoffInfohis.setPickingMode(mssAccountbill.getPickingMode());

/*            if ("8".equals(mssAccountbill.getBilltype().toString()))
                listhis = mssInterfaceMapper.selectWriteoffDetailInforecoveryhis(billId);
            else
                listhis = mssInterfaceMapper.selectWriteoffDetailInfohis(billId);*/
            if ((list != null && list.size() > 0) || (listhis != null && listhis.size() > 0)) {
                //辽宁 收款两条明细 去重
//                List<WriteoffDetailInfo> listnew = distinctWriteoffs(list);
//                writeoffInfo.setWriteoffDetailInfos(listnew);
                if ((list != null && list.size() > 0)) {
                    writeoffInfo.setWriteoffDetailInfos(list);
                    billList.add(writeoffInfo);
                }
                if (listhis != null && listhis.size() > 0) {
                    writeoffInfohis.setWriteoffDetailInfos(listhis);
                    billList.add(writeoffInfohis);
                }
                s = mssJsonClient.syncWriteoffInfosAddPcids(billList, billId, pcids);

            }
        }
        long endTime = System.currentTimeMillis();
        log.info("selectWriteoffDetailInfo runtime:{}", endTime - startTime);

        return s;
    }

    /**
     * 能耗系统同步集成报账单关联的电表相关信息。包括新增、修改、作废。
     */
    public String selectWriteoffDetailInfoPro(Long billId, String type) throws Exception {
        String s = "";
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        long startTime = System.currentTimeMillis();
        MssAccountbill mssAccountbill = billMapper.selectByPrimaryKey(billmap);
        if (mssAccountbill != null) {//支付类报账单送集团
            List<WriteoffInfo> billList = new ArrayList<>();
            List<WriteoffDetailInfo> list = null;
            List<WriteoffDetailInfo> listhis = null;//增加删除电表编号修改
            WriteoffInfo writeoffInfo = new WriteoffInfo();
            writeoffInfo.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfo.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
//            writeoffInfo.setType("1");// 1 新增, 3作废
            writeoffInfo.setType(type);
            writeoffInfo.setPickingMode(mssAccountbill.getPickingMode());
            // 查两个台账表 用uion all连接

            if ("8".equals(mssAccountbill.getBilltype().toString()))
                list = mssInterfaceMapper.selectWriteoffDetailInforecovery(billId);
            else
                list = mssInterfaceMapper.selectWriteoffDetailInfo(billId);
            doAccountidcWriteoffDetailInfo(list, billId);//处理IDC设备耗电量拆分
            WriteoffInfo writeoffInfohis = new WriteoffInfo(); //增加删除电表编号修改
            writeoffInfohis.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfohis.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
            writeoffInfohis.setType("3");// 1 新增, 3作废 固定位作废
            writeoffInfohis.setPickingMode(mssAccountbill.getPickingMode());

            if ((list != null && list.size() > 0) || (listhis != null && listhis.size() > 0)) {
                if ((list != null && list.size() > 0)) {
                    writeoffInfo.setWriteoffDetailInfos(list);
                    billList.add(writeoffInfo);
                }
                if (listhis != null && listhis.size() > 0) {
                    writeoffInfohis.setWriteoffDetailInfos(listhis);
                    billList.add(writeoffInfohis);
                }
                s = mssJsonClient.syncWriteoffInfosPro(billList, billId);

            }
        }

        return s;
    }

    /**
     * 抄表数据
     *
     * @param billId
     * @param type
     * @return
     * @throws Exception
     */
    public String selectWriteoffDetailInfo2(String updateMsg) throws Exception {
        String s = "";

        String[] strings = updateMsg.split("/");
        Long billId = Long.valueOf(strings[0]);
        String type = "1";
        String msg = strings[1];
        if ("delete".equals(msg)) {
            type = "3";
        } else if ("update".equals(msg)) {
            type = "2";
        }
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        MssAccountbill mssAccountbill = billMapper.selectByPrimaryKey(billmap);
        if (mssAccountbill != null /*&& ("2".equals(mssAccountbill.getBizTypeCode()) || "1".equals(mssAccountbill
        .getBizTypeCode()))*/) {//支付类报账单送集团
            List<WriteoffInfo2> billList = new ArrayList<>();
            List<WriteoffDetailInfo2> list = null;
            List<WriteoffDetailInfo2> listhis = null;//增加删除电表编号修改
            WriteoffInfo2 writeoffInfo = new WriteoffInfo2();
            writeoffInfo.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfo.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
//            writeoffInfo.setType("1");// 1 新增, 3作废
            writeoffInfo.setType(type);
            String pickingMode = mssAccountbill.getPickingMode();
            writeoffInfo.setPickingMode(pickingMode);
            // 查两个台账表 用uion all连接

            if ("8".equals(mssAccountbill.getBilltype().toString()))
                list = mssInterfaceMapper.selectWriteoffDetailInforecovery2(billId);
            else
                list = mssInterfaceMapper.selectWriteoffDetailInfo2(billId);
            //依据pickingMode 进行数据处理
            list.stream().forEach(
                    writeoffDetailInfo2 -> {
                        if ("6".equals(pickingMode)) {
                            String s1 = bigdecimalNegative(writeoffDetailInfo2.getThisQuantityOfElectricity());
                            writeoffDetailInfo2.setThisQuantityOfElectricity(s1);
                            String s2 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityCharge());
                            writeoffDetailInfo2.setThisElectricityCharge(s2);
                            String s3 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityPrice());
                            writeoffDetailInfo2.setThisElectricityPrice(s3);
                            String s4 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityTax());
                            writeoffDetailInfo2.setThisElectricityTax(s4);

                        }
                        if ("0".equals(pickingMode)) {
                            String s1 = bigdecimalNegative(writeoffDetailInfo2.getPowerConsumption());
                            writeoffDetailInfo2.setPowerConsumption(s1);
                        }
                    }
            );
            doAccountidcWriteoffDetailInfo2(list, billId);//处理IDC设备耗电量拆分
            WriteoffInfo2 writeoffInfohis = new WriteoffInfo2(); //增加删除电表编号修改
            writeoffInfohis.setOtherSystemMainId(mssAccountbill.getId().toString());
            writeoffInfohis.setWriteoffInstanceCode(mssAccountbill.getWriteoffInstanceCode());
            writeoffInfohis.setType("3");// 1 新增, 3作废 固定位作废
            writeoffInfohis.setPickingMode(mssAccountbill.getPickingMode());
            if ("8".equals(mssAccountbill.getBilltype().toString()))
                listhis = mssInterfaceMapper.selectWriteoffDetailInforecoveryhis2(billId);
            else
                listhis = mssInterfaceMapper.selectWriteoffDetailInfohis2(billId);
            if ((list != null && list.size() > 0) || (listhis != null && listhis.size() > 0)) {
                //辽宁 收款两条明细 去重
//                List<WriteoffDetailInfo> listnew = distinctWriteoffs(list);
//                writeoffInfo.setWriteoffDetailInfos(listnew);
                if ((list != null && list.size() > 0)) {
                    writeoffInfo.setWriteoffDetailInfos(list);
                    billList.add(writeoffInfo);
                }
                if (listhis != null && listhis.size() > 0) {
                    writeoffInfohis.setWriteoffDetailInfos(listhis);
                    billList.add(writeoffInfohis);
                }
                logger.info("synccopyMeter:" + JSON.toJSON(billList));
                //logger.debug("syncWriteoffInfos:" + JSON.toJSON(billList));
                s = s + "#request List#:" + JSON.toJSON(billList);
                //System.out.println("synccopyMeter:" + s);
                s = mssJsonClient.syncWriteoffInfos2(billList);
                insertLog("能耗系统 同步集团能源平台抄表数据" + billId, "copyMeter", s);
                logger.debug("synccopyMeterInfosRes:" + s);
                s = s + "#respone List#:" + JSON.toJSON(billList);
            }
        }
        return s;
    }

    /**
     * 双碳电表实际用电数据生成
     *
     * @param updateMsg
     * @return
     * @throws Exception
     */
    public String syncCopyMeterInforTwoC(String updateMsg, String checkEnergemetercode, boolean checkFlag) throws Exception {
        String s = "";

        String[] strings = updateMsg.split("/");
        Long billId = Long.valueOf(strings[0]);
        String type = "1";
        String msg = strings[1];
        if ("delete".equals(msg)) {
            type = "3";
        } else if ("update".equals(msg)) {
            type = "2";
        }
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        MssAccountbill mssAccountbill = billMapper.selectByPrimaryKey(billmap);
        if (mssAccountbill != null) {//支付类报账单送集团
            List<WriteoffDetailInfo2> list = null;
            List<WriteoffDetailInfo2> listhis = null;//增加删除电表编号修改
            String pickingMode = mssAccountbill.getPickingMode();

            if ("8".equals(mssAccountbill.getBilltype().toString())) {
                list = mssInterfaceMapper.selectWriteoffDetailInforecovery3(billId);
            } else {
                list = mssInterfaceMapper.selectWriteoffDetailInfo3(billId);
            }

            if (checkFlag) {
                boolean b1 = list.stream().anyMatch(
                        item -> item.getEnergyMeterCode().equals(checkEnergemetercode)
                );
                if (b1) {
                    list.forEach(
                            item -> {
                                item.setCcoer(billId + "");
                            }
                    );
                    OperationLogHelper
                            .getInstance()
                            .save("验证电表用电数据1", "meterdatesfortwoc",
                                    JSON.toJSONString(list), true);
                }
            }

            //依据pickingMode 进行数据处理
            list.stream().forEach(
                    writeoffDetailInfo2 -> {
                        if ("6".equals(pickingMode)) {
                            String s1 = bigdecimalNegative(writeoffDetailInfo2.getThisQuantityOfElectricity());
                            writeoffDetailInfo2.setThisQuantityOfElectricity(s1);
                            String s2 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityCharge());
                            writeoffDetailInfo2.setThisElectricityCharge(s2);
                            String s3 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityPrice());
                            writeoffDetailInfo2.setThisElectricityPrice(s3);
                            String s4 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityTax());
                            writeoffDetailInfo2.setThisElectricityTax(s4);

                        }
                        if ("0".equals(pickingMode)) {
                            String s1 = bigdecimalNegative(writeoffDetailInfo2.getPowerConsumption());
                            writeoffDetailInfo2.setPowerConsumption(s1);
                        }
                    }
            );

            doAccountidcWriteoffDetailInfo2(list, billId);//处理IDC设备耗电量拆分

            if (checkFlag) {
                boolean b2 = list.stream().anyMatch(
                        item -> item.getEnergyMeterCode().equals(checkEnergemetercode)
                );
                if (b2) {
                    list.forEach(
                            item -> {
                                item.setCcoer(billId + "");
                            }
                    );
                    OperationLogHelper
                            .getInstance()
                            .save("验证电表用电数据2", "meterdatesfortwoc",
                                    JSON.toJSONString(list), true);
                }
            }

            if ("8".equals(mssAccountbill.getBilltype().toString())) {
                listhis = mssInterfaceMapper.selectWriteoffDetailInforecoveryhis3(billId);
            } else {
                listhis = mssInterfaceMapper.selectWriteoffDetailInfohis3(billId);
            }

            if (checkFlag) {
                boolean b3 = listhis.stream().anyMatch(
                        item -> item.getEnergyMeterCode().equals(checkEnergemetercode)
                );
                if (b3) {
                    listhis.forEach(
                            item -> {
                                item.setCcoer(billId + "");
                            }
                    );
                    OperationLogHelper
                            .getInstance()
                            .save("验证电表用电数据3", "meterdatesfortwoc",
                                    JSON.toJSONString(listhis), true);
                }
            }

            //转换
            List<Meterdatesfortwoc> infodb = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                List<Meterdatesfortwoc> infoDbList1 = list.stream().map(
                        Meterdatesfortwoc::convert
                ).collect(toList());
                infodb.addAll(infoDbList1);
            }

            if (CollectionUtils.isNotEmpty(listhis)) {
                List<Meterdatesfortwoc> infoDbList2 = listhis.stream().map(
                        Meterdatesfortwoc::convert
                ).collect(toList());
                infodb.addAll(infoDbList2);
            }
            infodb.forEach(
                    item -> {
                        item.setSyncflag(0);
                        item.setCreatetime(new Date());
                    }
            );

            if (checkFlag) {
                boolean b = infodb.stream().anyMatch(
                        item -> item.getEnergymetercode().equals(checkEnergemetercode)
                );
                if (b) {
                    String billtype = mssAccountbill.getBilltype().toString();

                    infodb.forEach(
                            item -> {
                                item.setBillcode(billId + "");
                            }
                    );
                    OperationLogHelper
                            .getInstance()
                            .save("验证电表用电数据4", "meterdatesfortwoc",
                                    JSON.toJSONString(infodb), true);
                }
            }
            logger.info("数据准备完成");
            //向meterdatesfortwoc 插入数据
            if (CollectionUtils.isNotEmpty(infodb)) {
                int n = meterdatesfortwocMapper.insertList(infodb);
                logger.info("插入了双碳电表实际用电数据{},条", n);
            }
        }
        return s;
    }

    /**
     * 双碳电表实际用电数据 按月生成
     *
     * @param "wjs"
     * @param updateMsg
     * @param checkEnergemetercode
     * @param checkFlag
     * @return
     * @throws Exception
     */
    public String syncCopyMeterInforTwoCByMonth(String updateMsg, String checkEnergemetercode, boolean checkFlag) throws Exception {
        String s = "";

        String[] strings = updateMsg.split("/");
        String time = strings[0];
        String type = "1";
        String msg = strings[1];
        if ("delete".equals(msg)) {
            type = "3";
        } else if ("update".equals(msg)) {
            type = "2";
        }
        log.info("将历史数据物理删除");
        billMapper.updatemeterdatesfortwoc(time);

        //拿到对应月份的报账单id
        List<MssAccountbill> accountbills = billMapper.selectQuery(time);
        List<Long> ids = accountbills.stream().map(BaseEntity::getId).collect(toList());
        ids.forEach(
                billId ->
                {
                    Map<String, Object> billmap = new HashMap<>();
                    billmap.put("id", billId);
                    MssAccountbill mssAccountbill = billMapper.selectByPrimaryKey(billmap);
                    if (mssAccountbill != null) {//支付类报账单送集团
                        List<WriteoffDetailInfo2> list = null;
                        List<WriteoffDetailInfo2> listhis = null;//增加删除电表编号修改
                        String pickingMode = mssAccountbill.getPickingMode();

                        if ("8".equals(mssAccountbill.getBilltype().toString())) {
                            list = mssInterfaceMapper.selectWriteoffDetailInforecovery3(billId);
                        } else {
                            list = mssInterfaceMapper.selectWriteoffDetailInfo3(billId);
                        }

                        if (checkFlag) {
                            boolean b1 = list.stream().anyMatch(
                                    item -> item.getEnergyMeterCode().equals(checkEnergemetercode)
                            );
                            if (b1) {
                                list.forEach(
                                        item -> {
                                            item.setCcoer(billId + "");
                                        }
                                );
                                OperationLogHelper
                                        .getInstance()
                                        .save("验证电表用电数据1", "meterdatesfortwoc",
                                                JSON.toJSONString(list), true);
                            }
                        }
                        //依据pickingMode 进行数据处理
                        list.stream().forEach(
                                writeoffDetailInfo2 -> {
                                    if ("6".equals(pickingMode)) {
                                        String s1 =
                                                bigdecimalNegative(writeoffDetailInfo2.getThisQuantityOfElectricity());
                                        writeoffDetailInfo2.setThisQuantityOfElectricity(s1);
                                        String s2 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityCharge());
                                        writeoffDetailInfo2.setThisElectricityCharge(s2);
                                        String s3 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityPrice());
                                        writeoffDetailInfo2.setThisElectricityPrice(s3);
                                        String s4 = bigdecimalNegative(writeoffDetailInfo2.getThisElectricityTax());
                                        writeoffDetailInfo2.setThisElectricityTax(s4);

                                    }
                                    if ("0".equals(pickingMode)) {
                                        String s1 = bigdecimalNegative(writeoffDetailInfo2.getPowerConsumption());
                                        writeoffDetailInfo2.setPowerConsumption(s1);
                                    }
                                }
                        );

                        doAccountidcWriteoffDetailInfo2(list, billId);//处理IDC设备耗电量拆分

                        if (checkFlag) {
                            boolean b2 = list.stream().anyMatch(
                                    item -> item.getEnergyMeterCode().equals(checkEnergemetercode)
                            );
                            if (b2) {
                                list.forEach(
                                        item -> {
                                            item.setCcoer(billId + "");
                                        }
                                );
                                OperationLogHelper
                                        .getInstance()
                                        .save("验证电表用电数据2", "meterdatesfortwoc",
                                                JSON.toJSONString(list), true);
                            }
                        }

                        if ("8".equals(mssAccountbill.getBilltype().toString())) {
                            listhis = mssInterfaceMapper.selectWriteoffDetailInforecoveryhis3(billId);
                        } else {
                            listhis = mssInterfaceMapper.selectWriteoffDetailInfohis3(billId);
                        }

                        if (checkFlag) {
                            boolean b3 = listhis.stream().anyMatch(
                                    item -> item.getEnergyMeterCode().equals(checkEnergemetercode)
                            );
                            if (b3) {
                                listhis.forEach(
                                        item -> {
                                            item.setCcoer(billId + "");
                                        }
                                );
                                OperationLogHelper
                                        .getInstance()
                                        .save("验证电表用电数据3", "meterdatesfortwoc",
                                                JSON.toJSONString(listhis), true);
                            }
                        }

                        //转换
                        List<Meterdatesfortwoc> infodb = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<Meterdatesfortwoc> infoDbList1 = list.stream().map(
                                    Meterdatesfortwoc::convert
                            ).collect(toList());
                            infodb.addAll(infoDbList1);
                        }

                        if (CollectionUtils.isNotEmpty(listhis)) {
                            List<Meterdatesfortwoc> infoDbList2 = listhis.stream().map(
                                    Meterdatesfortwoc::convert
                            ).collect(toList());
                            infodb.addAll(infoDbList2);
                        }
                        logger.info("数据准备完成");
                        //向meterdatesfortwoc 插入数据
                        if (CollectionUtils.isNotEmpty(infodb)) {
                            infodb.forEach(
                                    item -> item.setSyncflag(0)
                            );
                            infodb.forEach(
                                    item -> {
                                        item.setSyncflag(0);
                                        item.setCreatetime(new Date());
                                    }
                            );
                            int n = meterdatesfortwocMapper.insertList(infodb);
                            if (checkFlag) {
                                boolean b = infodb.stream().anyMatch(
                                        item -> item.getEnergymetercode().equals(checkEnergemetercode)
                                );
                                if (b) {
                                    String billtype = mssAccountbill.getBilltype().toString();

                                    infodb.forEach(
                                            item -> {
                                                item.setBillcode(billId + "");
                                            }
                                    );
                                    OperationLogHelper
                                            .getInstance()
                                            .save("验证电表用电数据4", "meterdatesfortwoc",
                                                    JSON.toJSONString(infodb), true);
                                }
                            }

                            logger.info("插入了双碳电表实际用电数据{},条", n);
                        }
                    }
                }
        );

        log.info("根据电表类型 电表股份存续更新");
        meterdatesfortwocMapper.updateGroupTypeBitch(time);
        return s;
    }

    /**
     * 双碳电表实际用电数据 按账期生成
     *
     * @param "wjs"
     * @param updateMsg
     * @param checkEnergemetercode
     * @param checkFlag
     * @return
     * @throws Exception
     */
    public String WriteMeterInfo(String budget) throws Exception {

        log.info("将历史数据物理删除");
        billMapper.updatemeterinfo(budget);

        //拿到对应月份的报账单id
        List<MssAccountbill> accountbills = billMapper.selectQuery(budget);
        List<Long> ids = accountbills.stream().map(BaseEntity::getId).collect(toList());
        ids.forEach(
                billId ->
                {
                    log.info("开始收集billid={}的电表基础信息", billId);
                    String s = "";
                    Map<String, Object> billmap = new HashMap<>();
                    billmap.put("id", billId);
                    MssAccountbill bill = billMapper.selectByPrimaryKey(billmap);
                    Ammeterorprotocol m = new Ammeterorprotocol();
                    m.setParentCode(S_PROVINCE);//设置 存放省份编码
                    m.setCountry(Long.valueOf(bill.getFillInCostCenterId()));//设置财辅的组织编码
                    m.setElectrotype(billId);//用Electrotype 存放报账单id 进行查询
                    List<MeterInfo> meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter(m);

                    //处理包含IDC的电表
                    doAccountidcMeterInfo(meterInfoByAmmeter, billId);
                    log.info("数据转换");
                    List<MeterInfoDb> dbs = meterInfoByAmmeter.stream().map(
                            meterInfo -> {
                                MeterInfoDb db = new MeterInfoDb();
                                BeanUtils.copyProperties(meterInfo, db);
                                db.setBillid(String.valueOf(billId));
                                db.setBudget(budget);
                                return db;
                            }
                    ).collect(toList());
                    if (CollectionUtils.isNotEmpty(dbs)) {
                        int n = meterdatesfortwocMapper.insertListMeterinfoDb(dbs);
                        log.info("billid={} 插入了电表基础信息{}条", bill, n);
                    }
                }
        );
        return String.format("%s账期电表基础数据汇总完毕，请到meter_info_db表查看", budget);
    }


    private String bigdecimalNegative(String thisQuantityOfElectricity) {
        BigDecimal bigDecimal = new BigDecimal(thisQuantityOfElectricity);
        if (bigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            return thisQuantityOfElectricity;
        }
        return bigDecimal.abs().multiply(new BigDecimal("-1")).toString();
    }

    private List<WriteoffDetailInfo> distinctWriteoffs(List<WriteoffDetailInfo> list) {
        List<WriteoffDetailInfo> listnew = new ArrayList<>();
        for (WriteoffDetailInfo d : list) {
            boolean flag = true;
            for (WriteoffDetailInfo dnew : listnew) {
                if (dnew.getEnergyMeterCode().equals(d.getEnergyMeterCode())) {
                    Double aDouble =
                            Double.valueOf(dnew.getThisElectricityCharge()) + Double.valueOf(d.getThisElectricityCharge());
                    dnew.setThisElectricityCharge(aDouble.toString());
                    aDouble =
                            Double.valueOf(dnew.getThisQuantityOfElectricity()) + Double.valueOf(d.getThisQuantityOfElectricity());
                    dnew.setThisQuantityOfElectricity(aDouble.toString());
                    flag = false;
                }
            }
            if (flag)
                listnew.add(d);
        }
        return listnew;
    }

    /**
     * 能耗系统同步集成报账单关联的电表相关信息。包括新增、修改、作废。
     */
    public String syncEnergyMeterInfos(Long id) throws Exception {
        String s = "";
        Ammeterorprotocol m = new Ammeterorprotocol();
        m.setParentCode(S_PROVINCE);//设置 存放省份编码
        m.setCountry(0L);
        m.setId(id);
        List<MeterInfo> meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter(m);
//        logger.debug("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
        if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
            s = s + "#request List#:" + JSON.toJSON(meterInfoByAmmeter);
            s = mssJsonClient.syncEnergyMeterInfos(meterInfoByAmmeter);
            //insertLog("能耗系统同步集成电表" + id, "meterInfo", s);
            s = s + "#respone List#:" + JSON.toJSON(meterInfoByAmmeter);
            logger.debug("syncEnergyMeterInfosRes:" + s);
        }
        return s;
    }

    public String syncEnergyMeterInfoscompany(Ammeterorprotocol ammeterorprotocol) throws Exception {
        String s = "";
        Ammeterorprotocol m = new Ammeterorprotocol();
        m.setParentCode(S_PROVINCE);//设置 存放省份编码
        m.setCountry(0L);
        m.setCompany(ammeterorprotocol.getCompany());
        List<MeterInfo> meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfosend(m);
//        logger.debug("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
        if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
            //System.out.println("meterInfoByAmmeter.size():" + meterInfoByAmmeter.size());
            int size = meterInfoByAmmeter.size();
            int num = size / 50 + 1;
            boolean result = size % 50 == 0;
            List<MeterInfo> subList_1;
            for (int i = 0; i < num; i++) {

                if (i == num - 1) {
                    if (result) {
                        break;
                    }
                    subList_1 = meterInfoByAmmeter.subList(50 * i, size);
                } else {
                    subList_1 = meterInfoByAmmeter.subList(50 * i, 50 * (i + 1));
                }

                //s = mssJsonClient.syncEnergyMeterInfos(meterInfoByAmmeter);
                s = mssJsonClient.syncEnergyMeterInfos(subList_1);

                insertLog("能耗系统同步集成电表", "meterInfo", s);
                //s = s + "#List#:" + JSON.toJSON(meterInfoByAmmeter);
                //s = s + "#List#:" + JSON.toJSON(subList_1);
                //System.out.println("call respsone:" + s);
                logger.debug("syncEnergyMeterInfosRes:" + s);
                //subList_1.clear();
            }

        }
        return s;
    }

    //用Electrotype 存放报账单id 进行查询
    public String syncEnergyMeterInfosBybill(Long billId) throws Exception {
        String s = "";
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        MssAccountbill bill = billMapper.selectByPrimaryKey(billmap);
        Ammeterorprotocol m = new Ammeterorprotocol();
        m.setParentCode(S_PROVINCE);//设置 存放省份编码
        m.setCountry(Long.valueOf(bill.getFillInCostCenterId()));//设置财辅的组织编码
        m.setElectrotype(billId);//用Electrotype 存放报账单id 进行查询

        String companyCode = bill.getCompanyCode();
        log.info("报账单轮询，当前报账单对应的分公司id为: {}", companyCode);
        Long orgId = bill.getOrgid();
        log.info("报账单轮询，当前报账单对应的部门id为: {}", orgId);
        if (StrUtil.isBlank(companyCode) || ObjectUtil.isNull(orgId)) {
            throw new BusinessException("报账单轮询中存在companyCode或orgId为空，无法进行机构的判断！");
        }
        Long companyId = Long.valueOf(companyCode);
        // 是否是新版对应的机构id 成都武侯、蒲江区县 或者攀枝花、内江地市
        List<Long> newList = Arrays.asList(1000819L, 1000876L, 1008766L, 1008960L);
        log.info("报账单轮询，当前报账单对应的机构code为,companyId：{},orgId: {}", companyId, orgId);
        // 新增对市州的判断，若是对呀市州则走新版本，否则走老版本
        List<MeterInfo> meterInfoByAmmeter = new ArrayList<>();
        //if(newList.contains(companyId) || newList.contains(orgId)){
        log.info("报账单轮询，当前报账单对应的机构符合试点机构，进入新版查询");
        meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter(m);
        log.info("报账单轮询，当前报账单对应的机构符合试点机构，新版查询结束");
/*        } else {
            log.info("报账单轮询，当前报账单对应的机构非试点机构，进入老版查询");
            meterInfoByAmmeter = ammeterorprotocolMapper.getOldVerMeterInfoByAmmeter(m);
            log.info("报账单轮询，当前报账单对应的机构非试点机构，老版查询结束");
        }*/

        //处理包含IDC的电表
        doAccountidcMeterInfo(meterInfoByAmmeter, billId);
        //logger.info("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
        if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
            s = s + "#resquest List#:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call resquest: "+s);
            s = mssJsonClient.syncEnergyMeterInfosAddBillid(meterInfoByAmmeter, billId);
            //insertLog("能耗系统同步集成电表" + billId, "meterInfo", s);
            s =
                    s + "#respone-List#: size=" + meterInfoByAmmeter.size() + " json:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call respone: " + s);
            //logger.debug("syncEnergyMeterInfosRes:" + s);
        }
        return s;
    }


    @SneakyThrows
    public String baseStationBillingDataAdjustment(BaseStationBillingDataAdjustmentDTO dto) {
        log.info("{}推送", dto.getRewrite() ? "恢复" : "修改");
        // 清空备份表
        new MeterInfoPushBak().delete(new QueryWrapper<>());
        long pushNum = 0;
        String resultMsg = "";
        // 查询数据
        // 分批查询 power_writeoffrewrite.billId
        List<PowerWriteoffRewrite> groupByBillIdList = new PowerWriteoffRewrite().selectList(new LambdaQueryWrapper<PowerWriteoffRewrite>()
                .select(PowerWriteoffRewrite::getAccountbillid)
                .groupBy(PowerWriteoffRewrite::getAccountbillid)
        );
        log.info("groupByBillId-total:{}", groupByBillIdList.size());
        // 循环推送
        for (PowerWriteoffRewrite rewriteBillId : groupByBillIdList) {
            Long billId = rewriteBillId.getAccountbillid();
            log.info("推送billId:{}", billId);
            pushNum++;
            List<PowerWriteoffRewrite> list = new PowerWriteoffRewrite().selectList(new LambdaQueryWrapper<PowerWriteoffRewrite>()
                    .eq(PowerWriteoffRewrite::getAccountbillid, billId)
            );
            // 电表编号&开始时间&结束时间
            List<MeterInfo> meterInfoByAmmeter = syncresultMapper.selectMeterInfoDbBases(billId, dto.getBaseCreateTime());
            if (CollUtil.isEmpty(meterInfoByAmmeter)) {
                log.warn("无报账记录:{}，更改无效", billId);
                continue;
            }

            // 修改要推送的数据
            for (PowerWriteoffRewrite rewrite : list) {
                // 根据power_writeoffrewrite.billId 关联查询 meter_info_db_bases billid
                for (MeterInfo meterInfo : meterInfoByAmmeter) {

                    if (BooleanUtil.isTrue(dto.getRewrite())) {
                        // 恢复原始报账数据
                    } else {
                        // 匹配旧站址，替换新站址
                        if (rewrite.getOldstationcode().equals(meterInfo.getStationCode())) {
                            if (rewrite.getMetercode().equals(meterInfo.getEnergyMeterCode())) {
                                // 数据需要还原，在没有新的推送之前进行备份
                                this.meterInfoPushBak(meterInfo, billId);
                                // 备份完成
                                meterInfo.setStationCode(rewrite.getNewstationcode());
                                meterInfo.setSiteCode(rewrite.getNewstationcode());
                                log.info("推送新电表信息:{}", meterInfo);
                            }
                        }
                    }
                }
            }
            // 生产环境才推送
            if ("sc".equals(envProfile)) {
                resultMsg = mssJsonClient.syncEnergyMeterInfosAddBillid(meterInfoByAmmeter, billId);
            }
            log.info("推送次数:{},结果:{}", pushNum, resultMsg);
            Runtime.getRuntime().gc();
        }
        log.info("推送数据结束");
        return resultMsg;
    }

    /**
     * 基站报账数据调整原数据
     *
     * @param meterInfo 被推送的数据
     * @param billId
     */
    private void meterInfoPushBak(MeterInfo meterInfo, Long billId) {
        log.info("备份-基站报账数据调整原数据：{}", meterInfo);
        MeterInfoPushBak meterInfoPushBak = new MeterInfoPushBak();
        BeanUtils.copyProperties(meterInfo, meterInfoPushBak);
        meterInfoPushBak.setBillid(billId);
        meterInfoPushBak.insert();
    }

    /**
     * 恢复基站报账数据调整原数据
     */
    public void restoreMeterInfoPushBak() {
        List<MeterInfoPushBak> groupByBillIdList = new MeterInfoPushBak().selectList(new LambdaQueryWrapper<MeterInfoPushBak>()
                .select(MeterInfoPushBak::getBillid)
                .groupBy(MeterInfoPushBak::getBillid)
        );
    }

    /**
     *
     */
    @SneakyThrows
    public void baseStationBillingDataMeterData() {
        int pushCount = 0;
        List<PowerWriteoffRewrite> groupByBillIdList = new PowerWriteoffRewrite().selectList(new LambdaQueryWrapper<PowerWriteoffRewrite>()
                .select(PowerWriteoffRewrite::getAccountbillid)
                .groupBy(PowerWriteoffRewrite::getAccountbillid)
        );
        for (PowerWriteoffRewrite rewriteBillId : groupByBillIdList) {
            // 传输的数据
            List<WriteoffInfo> billList = new ArrayList<>();
            Long billId = rewriteBillId.getAccountbillid();
            WriteoffInfo writeoffInfo = new WriteoffInfo();
            // 电表编号&开始时间&结束时间
            List<WriteoffDetailInfo> meterInfoByAmmeter = syncresultMapper.baseStationBillingDataMeterData(billId);
            if (meterInfoByAmmeter.isEmpty()) {
                continue;
            }
            writeoffInfo.setType(meterInfoByAmmeter.get(0).getType());
            writeoffInfo.setPickingMode(meterInfoByAmmeter.get(0).getPickingMode());
            writeoffInfo.setOtherSystemMainId(meterInfoByAmmeter.get(0).getOtherSystemMainId());
            writeoffInfo.setWriteoffInstanceCode(meterInfoByAmmeter.get(0).getWriteoffInstanceCode());
            writeoffInfo.setWriteoffDetailInfos(meterInfoByAmmeter);
            billList.add(writeoffInfo);
            String syncResult = mssJsonClient.syncWriteoffInfos(billList, billId);
            log.info("推送电费结果:{}", syncResult);
            pushCount++;
            // 日志太多，只打印一条查看
            if (pushCount == 1) {
                log.info("推送的数据:{}", billList);
            }
        }
        log.info("报账单数量:{};推送次数:{}", groupByBillIdList.size(), pushCount);
    }

    public String syncEnergyMeterInfosBybillPro(Long billId) throws Exception {
        String s = "";
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        MssAccountbill bill = billMapper.selectByPrimaryKey(billmap);
        Ammeterorprotocol m = new Ammeterorprotocol();
        m.setParentCode(S_PROVINCE);//设置 存放省份编码
        m.setCountry(Long.valueOf(bill.getFillInCostCenterId()));//设置财辅的组织编码
        m.setElectrotype(billId);//用Electrotype 存放报账单id 进行查询
        List<MeterInfo> meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter(m);

        //处理包含IDC的电表
        doAccountidcMeterInfo(meterInfoByAmmeter, billId);
        if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
            s = s + "#resquest List#:" + JSON.toJSON(meterInfoByAmmeter);
            s = mssJsonClient.syncEnergyMeterInfosAddBillid(meterInfoByAmmeter, billId);

        }
        return s;
    }

    /**
     * 同步集团计量设备报账推送
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncEnergyMeterInfosByequ(Long billId) throws Exception {

        //计量表全量数据数据库插入

        Ammeterorprotocol m = new Ammeterorprotocol();
        m.setParentCode(S_PROVINCE);//设置 存放省份编码
        int n = 0;
        List<MeterInfo2> meterInfoByAmmeter = new ArrayList<>();

        if ("sc".equals(deployTo)) {
             meterInfoByAmmeter = ammeterorprotocolMapper.selectMeterInfoByAmmeter2ForSc(m);
            n = ammeterorprotocolMapper.createMeterInfoByAmmeter2ForScmss(m);
            //meterinfoMapper.insertmeter2(meterInfoByAmmeter);
        }
        if ("ln".equals(deployTo)) {

            n = ammeterorprotocolMapper.createMeterInfoByAmmeter2ForLnmss(m);
        }
        logger.info("计量设备表全量数据准备成功，共准备了{}条", n);


        String s = "";
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        MssAccountbill bill = billMapper.selectByPrimaryKey(billmap);
        Ammeterorprotocol ammeter = new Ammeterorprotocol();
        ammeter.setParentCode(S_PROVINCE);//设置 存放省份编码
        ammeter.setCountry(Long.valueOf(bill.getFillInCostCenterId()));//设置财辅的组织编码
        ammeter.setElectrotype(billId);//用Electrotype 存放报账单id 进行查询

        //List<MeterInfo2> meterInfoByAmmeter = new ArrayList<>();

        if ("sc".equals(deployTo)) {
            meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter2ForSc(ammeter);
        }
        if ("ln".equals(deployTo)) {
            meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter2ForLn(ammeter);
        }
        //处理包含IDC的电表
        //doAccountidcMeterInfo2(meterInfoByAmmeter, billId);
        //logger.info("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
        if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
            s = s + "#resquest List#:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call resquest: "+s);
            s = mssJsonClient.syncEnergyMeterInfos2(meterInfoByAmmeter);
//            insertLog("能耗系统同步计量设备" + billId, "meterInfo", s);
//            s = s + "#respone-List#:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call respone: " + s);
            //logger.debug("syncEnergyMeterInfosRes:" + s);
        }
        return s;
    }

    /**
     * 同步集团计量设备
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncEnergyMeterInfosBybillJiLiang(Long billId) throws Exception {
        //判断billId是否为-1
        //计量表全量数据数据库插入
        if (billId == -1L) {
            Ammeterorprotocol m = new Ammeterorprotocol();
            m.setParentCode(S_PROVINCE);//设置 存放省份编码
            int n = 0;
            if ("sc".equals(deployTo)) {
                ammeterorprotocolMapper.deleteMeterIfo();
                n = ammeterorprotocolMapper.createMeterInfoByAmmeter2ForSc(m);
            }
            if ("ln".equals(deployTo)) {
                ammeterorprotocolMapper.deleteMeterIfo();
                n = ammeterorprotocolMapper.createMeterInfoByAmmeter2ForLn(m);
            }
            logger.info("计量设备表全量数据准备成功，共准备了{}条", n);
            return "计量表数据已初始化";
        }
        String s = "";
        Map<String, Object> billmap = new HashMap<>();
        billmap.put("id", billId);
        MssAccountbill bill = billMapper.selectByPrimaryKey(billmap);
        Ammeterorprotocol m = new Ammeterorprotocol();
        m.setParentCode(S_PROVINCE);//设置 存放省份编码
        m.setCountry(Long.valueOf(bill.getFillInCostCenterId()));//设置财辅的组织编码
        m.setElectrotype(billId);//用Electrotype 存放报账单id 进行查询

        List<MeterInfo2> meterInfoByAmmeter = new ArrayList<>();

        if ("sc".equals(deployTo)) {
            meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter2ForSc(m);
        }
        if ("ln".equals(deployTo)) {
            meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter2ForLn(m);
        }
        //处理包含IDC的电表
        //doAccountidcMeterInfo2(meterInfoByAmmeter, billId);
        //logger.info("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
        if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
            s = s + "#resquest List#:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call resquest: "+s);
            s = mssJsonClient.syncEnergyMeterInfos2(meterInfoByAmmeter);
//            insertLog("能耗系统同步计量设备" + billId, "meterInfo", s);
//            s = s + "#respone-List#:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call respone: " + s);
            //logger.debug("syncEnergyMeterInfosRes:" + s);
        }
        return s;
    }

    /**
     * 双碳电表数据全量生成
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncmeterEquipmentInForTwoC(Long billId) throws Exception {
        //判断billId是否为-1
        //双碳接口 电表全量数据数据库插入
        if (billId == -1L) {
            Ammeterorprotocol m = new Ammeterorprotocol();
            m.setParentCode(S_PROVINCE);//设置 存放省份编码
            int n = 0;
            ammeterorprotocolMapper.deleteMeterIfoTwoc();
            if ("sc".equals(deployTo)) {
                n = ammeterorprotocolMapper.createMeterInfoTowCByAmmeter2ForSc(m);
            }
            if ("ln".equals(deployTo)) {
                n = ammeterorprotocolMapper.createMeterInfoTowCByAmmeter2ForLn(m);
            }

            logger.info("计量设备表全量数据准备成功，共准备了{}条", n);
            return "计量表数据已初始化";
        }
        return "初始化结束";
    }


    /**
     * 同步计量设备全量数据
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncEnergyMeterInfosAll(Long id) throws Exception {

        String s = "";
        List<? extends MeterInfo2> meterInfoByAmmeter = new ArrayList<>();

        Integer sum = 0;

        if (id == -99) {
            sum = meterinfoMapper.countForMeterinfoAllFail();
        } else if (id == -88) {
            sum = meterinfoMapper.countForMeterinfoAllFail();
        } else if (id == -2) {
            sum = meterinfoMapper.countForMeterinfoAll();
        } else {
            sum = meterinfoMapper.count(new Meterinfo());
        }
        int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
        for (int j = 1; j <= pageSum; j++) {
            int offset = (j - 1) * SYNCSUM;

            if (id == -99) {
                meterInfoByAmmeter = ammeterorprotocolMapper.selectMeterInfoFail(id, offset, SYNCSUM);
                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> {
                            meterInfo2.setType(SYNCTYPEDELETE);
                            meterInfo2.setStationStatus("0");
                            meterInfo2.setStatus("0");
                        }
                );
                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> meterInfo2.checkCompany()
                );
            } else if (id == -88) {
                meterInfoByAmmeter = ammeterorprotocolMapper.selectMeterInfoFail(id, offset, SYNCSUM);
                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> {
                            meterInfo2.setType(SYNCTYPEUPDATE);
                        }
                );
                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> meterInfo2.checkCompany()
                );
            } else if (id == -2) {
                meterInfoByAmmeter = ammeterorprotocolMapper.selectMeterInfoAll(id, offset, SYNCSUM);
                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> {
                            meterInfo2.setType(SYNCTYPEDELETE);
                            meterInfo2.setStationStatus("0");
                        }
                );
                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> meterInfo2.checkCompany()
                );
            } else {
                meterInfoByAmmeter = ammeterorprotocolMapper.selectMeterInfo(id, offset, SYNCSUM);
                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> meterInfo2.setType(SYNCTYPECREATE)
                );
/*                meterInfoByAmmeter.stream().forEach(
                        meterInfo2 -> meterInfo2.checkCompany()
                );*/
            }
            //去重
            meterInfoByAmmeter = meterInfoByAmmeter.stream().distinct().collect(toList());

            //logger.info("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
            if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
                //s = s + "#resquest List#:" + JSON.toJSON(meterInfoByAmmeter);
                //System.out.println("call resquest: "+s);
                int size = meterInfoByAmmeter.size();
                int num = size / 50 + 1;
                boolean result = size % 50 == 0;
                List<? extends MeterInfo2> subList_1;
                for (int i = 0; i < num; i++) {

                    if (i == num - 1) {
                        if (result) {
                            break;
                        }
                        subList_1 = meterInfoByAmmeter.subList(50 * i, size);
                    } else {
                        subList_1 = meterInfoByAmmeter.subList(50 * i, 50 * (i + 1));
                    }
                    s = mssJsonClient.syncEnergyMeterInfos2(subList_1);
                }

            }
        }

        return "同步计量设备成功";
    }

    /**
     * 同步计量设置增量数据
     *
     * @param id
     * @return
     * @throws Exception
     */
    public String syncIncrementalMeter(List<? extends MeterInfo2> meterInfoByAmmeter) throws Exception {
        String s = "";
        Integer sum = meterInfoByAmmeter.size();

        int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
        for (int j = 1; j <= pageSum; j++) {
            int offset = (j - 1) * SYNCSUM;

            //去重
            meterInfoByAmmeter = meterInfoByAmmeter.stream().distinct().collect(toList());

            if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
                int size = meterInfoByAmmeter.size();
                int num = size / 50 + 1;
                boolean result = size % 50 == 0;
                List<? extends MeterInfo2> subList_1;
                for (int i = 0; i < num; i++) {
                    if (i == num - 1) {
                        if (result) {
                            break;
                        }
                        subList_1 = meterInfoByAmmeter.subList(50 * i, size);
                    } else {
                        subList_1 = meterInfoByAmmeter.subList(50 * i, 50 * (i + 1));
                    }
                    s = mssJsonClient.syncEnergyMeterInfos2(subList_1);
                }

            }
        }

        return "同步计量设备成功";
    }

    /**
     * 同步双碳全量电表数据
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncmeterEquipmentInforTwocAll(Long id) throws Exception {

        List<? extends TwoCFlag> meterInfoByAmmeter = new ArrayList<>();

        Integer sum = 0;
        sum = twocMapper.countForMeterinfoAllFail();
        int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
        for (int j = 1; j <= pageSum; j++) {
            int offset = (j - 1) * SYNCSUM;

            if (id == -99) {
                meterInfoByAmmeter = twocMapper.selectMeterInfoFail(id, offset, SYNCSUM);
                meterInfoByAmmeter.stream().forEach(
                        meterInfo -> {
                            Twoc infoDB = (Twoc) meterInfo;
                            infoDB.setType(SYNCTYPEDELETE);
                            infoDB.setStationstatus("0");
                            infoDB.setStatus("0");
                        }
                );
            } else if (id == -88) {
                meterInfoByAmmeter = twocMapper.selectMeterInfoFail(id, offset, SYNCSUM);
                meterInfoByAmmeter.stream().forEach(
                        meterInfo -> {
                            Twoc infoDB = (Twoc) meterInfo;
                            infoDB.setType(SYNCTYPEUPDATE);
                            infoDB.setStationstatus("0");
                            infoDB.setStatus("0");
                        }
                );
            } else {
                meterInfoByAmmeter = twocMapper.selectMeterInfo(id, offset, SYNCSUM);
                meterInfoByAmmeter.stream().forEach(
                        meterInfo -> {
                            Twoc infoDB = (Twoc) meterInfo;
                            infoDB.setType(SYNCTYPECREATE);
                        }
                );
            }
            //去重
            meterInfoByAmmeter = meterInfoByAmmeter.stream().distinct().collect(toList());
            //依据citycode分组
            Map<String, ? extends List<? extends TwoCFlag>> infoMap =
                    meterInfoByAmmeter.stream().collect(groupingBy(
                            info -> {
                                Twoc infoDb = (Twoc) info;
                                return infoDb.getCitycode();
                            }
                    ));
            infoMap.forEach(
                    (subjectcode, list) -> {
                        if (list != null && list.size() > 0) {
                            int size = list.size();
                            int num = size / 50 + 1;
                            boolean result = size % 50 == 0;
                            List<? extends TwoCFlag> subList_1;
                            for (int i = 0; i < num; i++) {
                                if (i == num - 1) {
                                    if (result) {
                                        break;
                                    }
                                    subList_1 = list.subList(50 * i, size);
                                } else {
                                    subList_1 = list.subList(50 * i, 50 * (i + 1));
                                }
                                try {
                                    mssJsonClient.syncEnergyMeterInfoTwoc(subjectcode, subList_1);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                        }
                    }
            );

        }

        return "同步计量设备结束，请到syncresult查看日志";
    }

    /**
     * 同步双碳 全量 电表实际用电数据
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncMeterDateInforTwocAll(Long id) throws Exception {

        List<? extends TwoCFlag> meterInfoByAmmeter = new ArrayList<>();

        Integer sum = 0;
        sum = meterdatesfortwocMapper.countForMeterinfoAllFail();
        int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
        for (int j = 1; j <= pageSum; j++) {
            int offset = (j - 1) * SYNCSUM;

            meterInfoByAmmeter = meterdatesfortwocMapper.selectMeterInfo(id, offset, SYNCSUM);

            //去重
            meterInfoByAmmeter = meterInfoByAmmeter.stream().distinct().collect(toList());
            //过滤掉subjectcode为空的数据
            meterInfoByAmmeter = meterInfoByAmmeter.stream().filter(
                    item -> {
                        Meterdatesfortwoc temp = (Meterdatesfortwoc) item;
                        return temp.getSubjectcode() != null;
                    }
            ).collect(toList());
            //依据subjectCode分组
            Map<String, ? extends List<? extends TwoCFlag>> infoMap =
                    meterInfoByAmmeter.stream().collect(groupingBy(
                            info -> {
                                Meterdatesfortwoc infoDb = (Meterdatesfortwoc) info;
                                return infoDb.getSubjectcode();
                            }
                    ));
            infoMap.forEach(
                    (subjectcode, list) -> {
                        if (list != null && list.size() > 0) {
                            int size = list.size();
                            int num = size / 50 + 1;
                            boolean result = size % 50 == 0;
                            List<? extends TwoCFlag> subList_1;
                            for (int i = 0; i < num; i++) {
                                if (i == num - 1) {
                                    if (result) {
                                        break;
                                    }
                                    subList_1 = list.subList(50 * i, size);
                                } else {
                                    subList_1 = list.subList(50 * i, 50 * (i + 1));
                                }
                                try {
                                    mssJsonClient.syncMeterDateInforTwocAll(subjectcode, subList_1);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                        }
                    }
            );

        }

        return "OK";
    }

    /**
     * 同步双碳其它能耗数据
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncMeterOtherDateInforTwocAll(Long id) throws Exception {

        List<? extends TwoCFlag> meterInfoByAmmeter = new ArrayList<>();

        Integer sum = 0;
        sum = meterOtherDatesfortwocMapper.countForMeterinfoAllFail();
        int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
        for (int j = 1; j <= pageSum; j++) {
            int offset = (j - 1) * SYNCSUM;

            meterInfoByAmmeter = meterOtherDatesfortwocMapper.selectMeterInfo(id, offset, SYNCSUM);

            //去重
            meterInfoByAmmeter = meterInfoByAmmeter.stream().distinct().collect(toList());
            //依据subjectCode分组
            Map<String, ? extends List<? extends TwoCFlag>> infoMap =
                    meterInfoByAmmeter.stream().collect(groupingBy(
                            info -> {
                                MeterOtherDatesfortwoc infoDb = (MeterOtherDatesfortwoc) info;
                                return infoDb.getSubjectcode();
                            }
                    ));
            infoMap.forEach(
                    (subjectcode, list) -> {
                        if (list != null && list.size() > 0) {
                            int size = list.size();
                            int num = size / 50 + 1;
                            boolean result = size % 50 == 0;
                            List<? extends TwoCFlag> subList_1;
                            for (int i = 0; i < num; i++) {
                                if (i == num - 1) {
                                    if (result) {
                                        break;
                                    }
                                    subList_1 = list.subList(50 * i, size);
                                } else {
                                    subList_1 = list.subList(50 * i, 50 * (i + 1));
                                }
                                try {
                                    mssJsonClient.syncMeterOtherDateInforTwocAll(subjectcode, subList_1);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                        }
                    }
            );

        }

        return "OK";
    }

    /**
     * 同步双碳废水废气数据
     *
     * @param billId
     * @return
     * @throws Exception
     */
    public String syncMeterPollutionDateInforTwoc(Long id) throws Exception {

        List<? extends TwoCFlag> meterInfoByAmmeter = new ArrayList<>();

        Integer sum = 0;
        sum = meterPollutionDatesfortwocMapper.countForMeterinfoAllFail();
        int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
        for (int j = 1; j <= pageSum; j++) {
            int offset = (j - 1) * SYNCSUM;

            meterInfoByAmmeter = meterPollutionDatesfortwocMapper.selectMeterInfo(id, offset, SYNCSUM);

            //去重
            meterInfoByAmmeter = meterInfoByAmmeter.stream().distinct().collect(toList());
            //依据subjectCode分组
            Map<String, ? extends List<? extends TwoCFlag>> infoMap =
                    meterInfoByAmmeter.stream().collect(groupingBy(
                            info -> {
                                MeterPollutionDatesfortwoc infoDb = (MeterPollutionDatesfortwoc) info;
                                return infoDb.getSubjectcode();
                            }
                    ));
            infoMap.forEach(
                    (subjectcode, list) -> {
                        if (list != null && list.size() > 0) {
                            int size = list.size();
                            int num = size / 50 + 1;
                            boolean result = size % 50 == 0;
                            List<? extends TwoCFlag> subList_1;
                            for (int i = 0; i < num; i++) {
                                if (i == num - 1) {
                                    if (result) {
                                        break;
                                    }
                                    subList_1 = list.subList(50 * i, size);
                                } else {
                                    subList_1 = list.subList(50 * i, 50 * (i + 1));
                                }
                                try {
                                    mssJsonClient.syncMeterPollutionDateInforTwocAll(subjectcode, subList_1);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                        }
                    }
            );

        }

        return "OK";
    }

    public String SyncTest(String id) {
        try {
            return mssJsonClient.syncLnTest(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "测试完毕";
    }

    //同步全量设备更正
    public String syncmeterEquipmentInforsAllCorrections(Long id) throws Exception {
        //1.取出集团全量数据 、能耗全量数据
        List<? extends MeterInfo2> meterInfosForCompany = ammeterorprotocolMapper.selectMeterInfoForCompany();
        List<? extends MeterInfo2> meterInfosForNH = ammeterorprotocolMapper.selectMeterInfoForNH();
        //2.将集团中存在，定额来源不存在的删除
        List<? extends MeterInfo2> meterInfoDelete = meterInfosForCompany.stream().filter(
                meterInfo2 -> !meterInfosForNH.contains(meterInfo2)
        ).collect(toList());
        //3.将集团不存在，定额中存在的新增
        List<? extends MeterInfo2> meterInfoAdd = meterInfosForNH.stream().filter(
                meterInfo2 -> !meterInfosForCompany.contains(meterInfo2)
        ).collect(toList());

        String s = "";
        List<? extends MeterInfo2> meterInfoByAmmeter = new ArrayList<>();

        Integer sum = meterinfoMapper.count(new Meterinfo());
        int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
        for (int j = 1; j <= pageSum; j++) {
            int offset = (j - 1) * SYNCSUM;
            meterInfoByAmmeter = ammeterorprotocolMapper.selectMeterInfo(id, offset, SYNCSUM);
            //去重
            meterInfoByAmmeter = meterInfoByAmmeter.stream().distinct().collect(toList());

            //logger.info("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
            if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
                s = s + "#resquest List#:" + JSON.toJSON(meterInfoByAmmeter);
                //System.out.println("call resquest: "+s);
                int size = meterInfoByAmmeter.size();
                int num = size / 50 + 1;
                boolean result = size % 50 == 0;
                List<? extends MeterInfo2> subList_1;
                for (int i = 0; i < num; i++) {

                    if (i == num - 1) {
                        if (result) {
                            break;
                        }
                        subList_1 = meterInfoByAmmeter.subList(50 * i, size);
                    } else {
                        subList_1 = meterInfoByAmmeter.subList(50 * i, 50 * (i + 1));
                    }
                    s = mssJsonClient.syncEnergyMeterInfos2(subList_1);
                }

            }
        }

        return "同步计量设备成功";
    }


    private void doAccountidcMeterInfo(List<MeterInfo> meterInfoByAmmeter, Long billid) {
        try {
            List<AccountIdc> byBillid = accountIdcMapper.getByBillid(billid);
            if (billid != null && byBillid.size() > 0) {
                List<MeterInfo> newlist = new ArrayList<>();
                for (AccountIdc accountIdc : byBillid) {
                    if (accountIdc.getScale().compareTo(BigDecimal.valueOf(100)) == 0) {
                        // 百分百的IDC机房 不拆分
                        continue;
                    }
                    for (MeterInfo meterInfo : meterInfoByAmmeter) {
                        if (accountIdc.getEnergymetercode().equals(meterInfo.getEnergyMeterCode())) {
                            MeterInfo newmeter = new MeterInfo();
                            BeanUtils.copyProperties(meterInfo, newmeter);
                            if (accountIdc.getElectroTypeId() == 3) {
                                newmeter.setStationType("121");
                            } else {
                                newmeter.setStationType("122");
                            }
                            newmeter.setEnergyMeterCode(meterInfo.getEnergyMeterCode() + "(IDC)");
                            StationInfo stationInfo =
                                    accountIdcMapper.getStationInfoIdc(accountIdc.getStationId());
                            newmeter.setStationCode(stationInfo.getStationcode());
                            newmeter.setStationName(stationInfo.getStationname());
                            newmeter.setStationLocation(stationInfo.getAddress());
                            newlist.add(newmeter);
                            continue;
                        }
                    }
                }
                if (newlist.size() > 0)
                    meterInfoByAmmeter.addAll(newlist);
            }
        } catch (Exception e) {
            e.printStackTrace();
            insertLog("IDC拆分失败" + billid, "doAccountidcMeterInfo", e.getMessage());
        }
    }

    private void doAccountidcMeterInfo2(List<MeterInfo2> meterInfoByAmmeter, Long billid) {
        try {
            List<AccountIdc> byBillid = accountIdcMapper.getByBillid(billid);
            if (billid != null && byBillid.size() > 0) {
                List<MeterInfo2> newlist = new ArrayList<>();
                for (AccountIdc accountIdc : byBillid) {
                    if (accountIdc.getScale().compareTo(BigDecimal.valueOf(100)) == 0) {
                        // 百分百的IDC机房 不拆分
                        continue;
                    }
                    for (MeterInfo2 meterInfo : meterInfoByAmmeter) {
                        if (accountIdc.getEnergymetercode().equals(meterInfo.getEnergyMeterCode())) {
                            MeterInfo2 newmeter = new MeterInfo2();
                            BeanUtils.copyProperties(meterInfo, newmeter);
                            if (accountIdc.getElectroTypeId() == 3) {
                                newmeter.setStationType("121");
                            } else {
                                newmeter.setStationType("122");
                            }
                            newmeter.setEnergyMeterCode(meterInfo.getEnergyMeterCode() + "(IDC)");
                            StationInfo stationInfo =
                                    accountIdcMapper.getStationInfoIdc(accountIdc.getStationId());
                            newmeter.setStationCode(stationInfo.getStationcode());
                            newmeter.setStationName(stationInfo.getStationname());
                            newmeter.setStationLocation(stationInfo.getAddress());
                            newlist.add(newmeter);
                            continue;
                        }
                    }
                }
                if (newlist.size() > 0)
                    meterInfoByAmmeter.addAll(newlist);
            }
        } catch (Exception e) {
            e.printStackTrace();
            insertLog("IDC拆分失败" + billid, "doAccountidcMeterInfo", e.getMessage());
        }
    }

    private void doAccountidcWriteoffDetailInfo(List<WriteoffDetailInfo> list, Long billid) {
        try {
            List<AccountIdc> byBillid = accountIdcMapper.getByBillid(billid);
            if (billid != null && byBillid.size() > 0) {
                List<WriteoffDetailInfo> newlist = new ArrayList<>();
                List<AccountIdc> newlist1 = new ArrayList<>();
                for (AccountIdc accountIdc : byBillid) {
                    AccountIdc newidc = new AccountIdc();
                    BeanUtils.copyProperties(accountIdc, newidc);
                    if (accountIdc.getScale().compareTo(BigDecimal.valueOf(100)) == 0) {
                        // 百分百的IDC机房 不拆分
                        continue;
                    }
                    for (WriteoffDetailInfo writeoffDetailInfo : list) {
/*                        if (!"0.0000".equals(writeoffDetailInfo.getPowerConsumption())
                                && accountIdc.getEnergymetercode().equals(writeoffDetailInfo.getEnergyMeterCode())) {*/
                        if (!"0.00".equals(writeoffDetailInfo.getThisElectricityCharge()) && accountIdc.getEnergymetercode().equals(writeoffDetailInfo.getEnergyMeterCode())) {
                            WriteoffDetailInfo newwriteoff = new WriteoffDetailInfo();
                            BeanUtils.copyProperties(writeoffDetailInfo, newwriteoff);
                            newwriteoff.setEnergyMeterCode(writeoffDetailInfo.getEnergyMeterCode() + "(IDC)");
                            newwriteoff.setThisQuantityOfElectricity(accountIdc.getAmount().toString());
                            newwriteoff.setTotalQuantityOfElectricity(accountIdc.getAmount().toString());
                            newwriteoff.setPowerConsumption(accountIdc.getAmount().multiply(new BigDecimal(0.67)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            newwriteoff.setThisElectricityCharge(accountIdc.getMoney().toString());
                            //newwriteoff.setThisElectricityPrice(new BigDecimal(newwriteoff
                            // .getThisElectricityPrice
                            // ()).multiply(accountIdc.getScale()).divide(new BigDecimal(100)).setScale(2,
                            // BigDecimal
                            // .ROUND_HALF_UP).toString());

                            newwriteoff.setThisElectricityTax(new BigDecimal(newwriteoff.getThisElectricityTax()).multiply(accountIdc.getScale()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            BigDecimal newwrtaxmoney = new BigDecimal(newwriteoff.getThisElectricityTax());
                            newwriteoff.setThisElectricityPrice(accountIdc.getMoney().subtract(newwrtaxmoney).toString());
                            newlist.add(newwriteoff);

                            BigDecimal amount =
                                    new BigDecimal(writeoffDetailInfo.getThisQuantityOfElectricity());
                            writeoffDetailInfo.setThisQuantityOfElectricity(amount.subtract(accountIdc.getAmount()).toString());
                            BigDecimal money = new BigDecimal(writeoffDetailInfo.getThisElectricityCharge());
                            BigDecimal pricemoney =
                                    new BigDecimal(writeoffDetailInfo.getThisElectricityPrice());
                            BigDecimal newpricemoney = new BigDecimal(newwriteoff.getThisElectricityPrice());
                            BigDecimal taxmoney = new BigDecimal(writeoffDetailInfo.getThisElectricityTax());
                            BigDecimal newtaxmoney = new BigDecimal(newwriteoff.getThisElectricityTax());
                            writeoffDetailInfo.setThisElectricityCharge(money.subtract(accountIdc.getMoney()).toString());
                            writeoffDetailInfo.setPowerConsumption("0");
                            /*writeoffDetailInfo.setThisElectricityPrice(money.subtract(accountIdc.getMoney())
                            .toString());*/
                            //writeoffDetailInfo.setThisElectricityTax();

                            writeoffDetailInfo.setThisElectricityTax(taxmoney.subtract(newwrtaxmoney).toString());
                            writeoffDetailInfo.setThisElectricityPrice(pricemoney.subtract(newpricemoney).toString());
                            newidc.setAmount(amount.subtract(accountIdc.getAmount()));
                            newidc.setMoney(money.subtract(accountIdc.getMoney()));
                            BigDecimal oneh = BigDecimal.valueOf(100);
                            newidc.setScale(oneh.subtract(accountIdc.getScale()));
                            continue;
                        }
                    }
                    newidc.setElectroTypeId(0);
                    newlist1.add(newidc);
                }
                if (newlist.size() > 0)
                    list.addAll(newlist);
                // 记录IDC
                if (newlist1.size() > 0)
                    byBillid.addAll(newlist1);
                for (AccountIdc accountIdc : byBillid) {
                    List<AccountIdc> accountIdcs = accountIdcMapper.selectList(accountIdc);
                    if (accountIdcs != null && accountIdcs.size() > 0) {
                        for (AccountIdc item : accountIdcs) {
                            AccountIdc model = new AccountIdc();
                            model.setDelFlag("1");
                            model.setId(item.getId());
                            accountIdcMapper.updateForModel(model);
                        }
                    }
                    accountIdcMapper.insert(accountIdc);//插入记录
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            insertLog("IDC拆分失败" + billid, "doAccountidcWriteoffDetailInfo", e.getMessage());
        }
    }

    private void doAccountidcWriteoffDetailInfo2(List<WriteoffDetailInfo2> list, Long billid) {
        try {
            List<AccountIdc> byBillid = accountIdcMapper.getByBillid(billid);
            if (billid != null && byBillid.size() > 0) {
                List<WriteoffDetailInfo2> newlist = new ArrayList<>();
                List<AccountIdc> newlist1 = new ArrayList<>();
                for (AccountIdc accountIdc : byBillid) {
                    AccountIdc newidc = new AccountIdc();
                    BeanUtils.copyProperties(accountIdc, newidc);
                    if (accountIdc.getScale().compareTo(BigDecimal.valueOf(100)) == 0) {
                        // 百分百的IDC机房 不拆分
                        continue;
                    }
                    for (WriteoffDetailInfo2 writeoffDetailInfo : list) {
/*                        if (!"0.0000".equals(WriteoffDetailInfo2.getPowerConsumption())
                                && accountIdc.getEnergymetercode().equals(WriteoffDetailInfo2.getEnergyMeterCode())) {*/
                        if (!"0.00".equals(writeoffDetailInfo.getThisElectricityCharge()) && accountIdc.getEnergymetercode().equals(writeoffDetailInfo.getEnergyMeterCode())) {
                            WriteoffDetailInfo2 newwriteoff = new WriteoffDetailInfo2();
                            BeanUtils.copyProperties(writeoffDetailInfo, newwriteoff);
                            newwriteoff.setEnergyMeterCode(writeoffDetailInfo.getEnergyMeterCode() + "(IDC)");
                            newwriteoff.setThisQuantityOfElectricity(accountIdc.getAmount().toString());
                            newwriteoff.setTotalQuantityOfElectricity(accountIdc.getAmount().toString());
                            newwriteoff.setPowerConsumption(accountIdc.getAmount().multiply(new BigDecimal(0.98)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            newwriteoff.setThisElectricityCharge(accountIdc.getMoney().toString());
                            //newwriteoff.setThisElectricityPrice(new BigDecimal(newwriteoff
                            // .getThisElectricityPrice
                            // ()).multiply(accountIdc.getScale()).divide(new BigDecimal(100)).setScale(2,
                            // BigDecimal
                            // .ROUND_HALF_UP).toString());

                            newwriteoff.setThisElectricityTax(new BigDecimal(newwriteoff.getThisElectricityTax()).multiply(accountIdc.getScale()).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            BigDecimal newwrtaxmoney = new BigDecimal(newwriteoff.getThisElectricityTax());
                            newwriteoff.setThisElectricityPrice(accountIdc.getMoney().subtract(newwrtaxmoney).toString());
                            newlist.add(newwriteoff);

                            BigDecimal amount =
                                    new BigDecimal(writeoffDetailInfo.getThisQuantityOfElectricity());
                            writeoffDetailInfo.setThisQuantityOfElectricity(amount.subtract(accountIdc.getAmount()).toString());
                            BigDecimal money = new BigDecimal(writeoffDetailInfo.getThisElectricityCharge());
                            BigDecimal pricemoney =
                                    new BigDecimal(writeoffDetailInfo.getThisElectricityPrice());
                            BigDecimal newpricemoney = new BigDecimal(newwriteoff.getThisElectricityPrice());
                            BigDecimal taxmoney = new BigDecimal(writeoffDetailInfo.getThisElectricityTax());
                            BigDecimal newtaxmoney = new BigDecimal(newwriteoff.getThisElectricityTax());
                            writeoffDetailInfo.setThisElectricityCharge(money.subtract(accountIdc.getMoney()).toString());
                            writeoffDetailInfo.setPowerConsumption("0");
                            /*writeoffDetailInfo.setThisElectricityPrice(money.subtract(accountIdc.getMoney())
                            .toString());*/
                            //writeoffDetailInfo.setThisElectricityTax();

                            writeoffDetailInfo.setThisElectricityTax(taxmoney.subtract(newwrtaxmoney).toString());
                            writeoffDetailInfo.setThisElectricityPrice(pricemoney.subtract(newpricemoney).toString());
                            newidc.setAmount(amount.subtract(accountIdc.getAmount()));
                            newidc.setMoney(money.subtract(accountIdc.getMoney()));
                            BigDecimal oneh = BigDecimal.valueOf(100);
                            newidc.setScale(oneh.subtract(accountIdc.getScale()));
                            continue;
                        }
                    }
                    newidc.setElectroTypeId(0);
                    newlist1.add(newidc);
                }
                if (newlist.size() > 0)
                    list.addAll(newlist);
                // 记录IDC
                if (newlist1.size() > 0)
                    byBillid.addAll(newlist1);
                for (AccountIdc accountIdc : byBillid) {
                    List<AccountIdc> accountIdcs = accountIdcMapper.selectList(accountIdc);
                    if (accountIdcs != null && accountIdcs.size() > 0) {
                        for (AccountIdc item : accountIdcs) {
                            AccountIdc model = new AccountIdc();
                            model.setDelFlag("1");
                            model.setId(item.getId());
                            accountIdcMapper.updateForModel(model);
                        }
                    }
                    accountIdcMapper.insert(accountIdc);//插入记录
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            insertLog("IDC拆分失败" + billid, "doAccountidcWriteoffDetailInfo", e.getMessage());
        }
    }

    private void updateprepaid(MssAccountbill bill) {//报账单类型为收款8，业务场景为6
        boolean flag = true;
        List<MssAccountbillitem> mssbillitems;
        BigDecimal oldbalance, newbalance;

        if ("8".equals(bill.getBilltype().toString()) && "6".equals(bill.getPickingMode()))
            //如果客户是关联交易方
            if (mssAbccustomerService.isRelateabccustomer(bill.getSupplierCode())) {
                mssbillitems = mssAccountbillService.getByid(bill.getId()).getItem();
                if (mssbillitems != null && mssbillitems.size() > 0) {
                    for (int i = 0; i < mssbillitems.size(); i++) {
                        if (("2".equals(mssbillitems.get(i).getBudgetType())) && ("1".equals(mssbillitems.get(i).getUsageId()))) /*关联交易方收款 完成后插入prepaid表 并计算余额 *********/ {
                            MssAccountPrepaid mssAccountPrepaid = new MssAccountPrepaid();
                            mssAccountPrepaid.setAccountId(bill.getId());
                            mssAccountPrepaid.setDebitAccountCode(bill.getSupplierCode());
                            mssAccountPrepaid.setDebitAccountName(bill.getSupplierName());
                            mssAccountPrepaid.setCountry(bill.getOrgid());
                            mssAccountPrepaid.setCompanyCode(new Long(bill.getCompanyCode()));
                            oldbalance = mssAccountPrepaidService.computeBalance(mssAccountPrepaid);
                            newbalance = oldbalance.add(bill.getSum());
                            mssAccountPrepaid.setSum(bill.getSum());
                            mssAccountPrepaid.setBalance(newbalance);
                            mssAccountPrepaid.setInputdate(now());
                            mssAccountPrepaid.setDelFlag("0");
                            mssAccountPrepaidService.insert(mssAccountPrepaid);
                        }
                    }

                }
            }
        //1,2,3,5,7
        if ("1".equals(bill.getBilltype().toString()) || "2".equals(bill.getBilltype().toString()) || "3".equals(bill.getBilltype().toString()) || "5".equals(bill.getBilltype().toString()) || "7".equals(bill.getBilltype().toString())) {
            mssbillitems = mssAccountbillService.getByid(bill.getId()).getItem();
            if (mssbillitems != null && mssbillitems.size() > 0) {
                for (int i = 0; i < mssbillitems.size(); i++) {
                    if (("2".equals(mssbillitems.get(i).getBudgetType())) && ("2".equals(mssbillitems.get(i).getUsageId()))) //用途为2 支付代垫外单位 列账属性2 不使用成本
                        if (mssAbccustomerService.isRelateabccustomer(mssbillitems.get(i).getDebitAccountCode()))
                        //是否关联交易方 执行计算
                        {
                            MssAccountPrepaid mssAccountPrepaid = new MssAccountPrepaid();
                            mssAccountPrepaid.setAccountId(bill.getId());
                            mssAccountPrepaid.setAccountItemId(mssbillitems.get(i).getId());
                            mssAccountPrepaid.setDebitAccountCode(mssbillitems.get(i).getDebitAccountCode());
                            mssAccountPrepaid.setDebitAccountName(mssbillitems.get(i).getDebitAccountName());
                            mssAccountPrepaid.setCountry(bill.getOrgid());
                            mssAccountPrepaid.setCompanyCode(new Long(bill.getCompanyCode()));
                            oldbalance = mssAccountPrepaidService.computeBalance(mssAccountPrepaid);
                            mssAccountPrepaid.setTaxAdjustSum(mssbillitems.get(i).getTaxAdjustSum());
                            mssAccountPrepaid.setSum(mssbillitems.get(i).getSum());
                            newbalance =
                                    oldbalance.subtract(mssbillitems.get(i).getSum().add(mssbillitems.get(i).getTaxAdjustSum()));
                            mssAccountPrepaid.setBalance(newbalance);
                            mssAccountPrepaid.setInputdate(now());
                            mssAccountPrepaid.setDelFlag("0");
                            mssAccountPrepaidService.insert(mssAccountPrepaid);
                        }

                }
            }
        }

    }

    /**
     * 集团 同步计量设备信息模板
     *
     * @param year
     * @return
     */
    public String selectmeterEquipmentInfors(Integer month) {
        String s = "";
        //1.组装数据
        List<MeterEquipmentInfo2> meterEquipmentInfo2s = new ArrayList<>();

        if ("sc".equals(deployTo)) {
            meterEquipmentInfo2s = ammeterorprotocolMapper.getMeterEquipmentInfosForSc(month);
        }
        if ("ln".equals(deployTo)) {
            meterEquipmentInfo2s = ammeterorprotocolMapper.getMeterEquipmentInfosForLn(month);
        }
        String type = "1";
        String finalType = type;
        List<MeterEquipmentInfo> meterEquipmentInfos = meterEquipmentInfo2s.stream().map(
                meterEquipmentInfo2 -> {
                    MeterEquipmentInfo meterEquipmentInfo = new MeterEquipmentInfo();
                    BeanUtils.copyProperties(meterEquipmentInfo2, meterEquipmentInfo);
                    meterEquipmentInfo.setUsage(meterEquipmentInfo2.getUsageCopy());
                    meterEquipmentInfo.setType(finalType);
                    return meterEquipmentInfo;
                }
        ).collect(toList());
        //2.日志输出数据
//        logger.debug("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));

        //3.推送数据
        if (meterEquipmentInfos != null && meterEquipmentInfos.size() > 0) {
            s = s + "#request List#:" + JSON.toJSON(meterEquipmentInfos);
            s = mssJsonClient.syncMeterEquipmentInfos(meterEquipmentInfos);
            //4.插入操作日志
            //insertLog("同步计量设备信息模板" + id, "meterInfo", s);
            s = s + "#respone List#:" + JSON.toJSON(meterEquipmentInfos);
            logger.debug("syncMeterEquipmentInfors:" + s);
        }
        return s;

    }

    public String selectmeterEquipmentInfors(String updateMsg) throws Exception {
        String s = "";
        //1.组装数据
        String[] strings = updateMsg.split("/");
        Long ammeterorprotocolId = Long.valueOf(strings[0]);
        String type = "1";
        String msg = strings[1];
        if ("delete".equals(msg)) {
            type = "3";
        } else if ("update".equals(msg)) {
            type = "2";
        }
        String finalType = type;

        List<? extends MeterInfo2> meterInfoByAmmeter = new ArrayList<>();

        Ammeterorprotocol m = ammeterorprotocolMapper.getById(ammeterorprotocolId);
        m.setParentCode(S_PROVINCE);
        if ("sc".equals(deployTo)) {
            meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter2ForSc(m);
        }
        if ("ln".equals(deployTo)) {
            meterInfoByAmmeter = ammeterorprotocolMapper.getMeterInfoByAmmeter2ForLn(m);
            logger.info("换表计量设备信息：{}", meterInfoByAmmeter);

        }

        meterInfoByAmmeter.stream().forEach(
                meterInfo2 -> meterInfo2.setType(finalType)
        );

        //logger.info("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));
        if (meterInfoByAmmeter != null && meterInfoByAmmeter.size() > 0) {
            s = s + "#resquest List#:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call resquest: "+s);
            s = mssJsonClient.syncEnergyMeterInfos2(meterInfoByAmmeter);
            s = s + "#respone-List#:" + JSON.toJSON(meterInfoByAmmeter);
            //System.out.println("call respone: " + s);
            //logger.debug("syncEnergyMeterInfosRes:" + s);
        }
        return s;

    }

    /**
     * 同步省能耗管理平台中抄表数据
     *
     * @param year
     * @return
     */
    public String selectCopyMeterInfors(String updateMsg) {
        String s = "";
        //1.组装数据
        String[] strings = updateMsg.split("/");
        Long billId = Long.valueOf(strings[0]);
        String type = "1";
        String msg = strings[1];
        if ("delete".equals(msg)) {
            type = "3";
        } else if ("update".equals(msg)) {
            type = "2";
        }
        String finalType = type;
        if ("sc".equals(deployTo)) {
            copyMeters = ammeterorprotocolMapper.getCopyMeterInfors(billId);
        }
        if ("ln".equals(deployTo)) {
            copyMeters = ammeterorprotocolMapper.getCopyMeterInforsForLn(billId);
        }
        //2.日志输出数据
//        logger.debug("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));

        //3.推送数据
        if (copyMeters != null && copyMeters.size() > 0) {
            s = s + "#request List#:" + JSON.toJSON(copyMeters);
            s = mssJsonClient.syncCopyMeterInfors(copyMeters);
            //4.插入操作日志
            //insertLog("同步省能耗管理平台中抄表数据" + id, "meterInfo", s);
            s = s + "#respone List#:" + JSON.toJSON(copyMeters);
            logger.debug("syncCopyMeterInfors:" + s);
        }
        return s;

    }

    /**
     * 同步智能采集的电量数据
     *
     * @param year
     * @return
     */
    public String selectCollectMeterInfors2(Long pcid, String collectTime) {
        String s = "";
        //1.组装数据 辽宁，四川一致
        List<CollectMeter> collectMeterInfors = null;

        if (deployTo.equals("sc")) {
            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInfors2SC(pcid, collectTime);
        }

        if (deployTo.equals("ln")) {
            if (pcid == 99L) {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInfors2SC(pcid, collectTime);
            } else {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInfors2(pcid, collectTime);
            }
        }

        //处理数据，满足协议格式
        CollectMeter.processDataFields(collectMeterInfors);

        //3.推送数据
        if (collectMeterInfors != null && collectMeterInfors.size() > 0) {
            int size = collectMeterInfors.size();
            int num = size / 50 + 1;
            boolean result = size % 50 == 0;
            List<CollectMeter> subList_1;

            for (int i = 0; i < num; i++) {

                if (i == num - 1) {
                    if (result) {
                        break;
                    }
                    subList_1 = collectMeterInfors.subList(50 * i, size);
                } else {
                    subList_1 = collectMeterInfors.subList(50 * i, 50 * (i + 1));
                }
                //s = s + "#request List#:" + JSON.toJSON(collectMeterInfors);
                s = mssJsonClient.syncCollectMeterInfors(subList_1);
                //4.插入操作日志
//                insertLog("同步智能采集的电量数据", "collectOuter", s);
//                s = s + "#respone List#:" + JSON.toJSON(collectMeterInfors);
//                logger.debug("selectCollectMeterInfors:" + s);

            }
        }
        insertLog("同步智能采集数据", "sumForCollect", collectMeterInfors.size() + "条");
        return "同步采集数据调用完毕";

    }

    public String selectCollectMeterInfors2Pro(Long pcid, String collectTime, String budget) {
        String s = "";
        //1.组装数据 辽宁，四川一致
        List<CollectMeter> collectMeterInfors = null;

        if (deployTo.equals("sc")) {
            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInfors2SC(pcid, collectTime);
        }
        if (deployTo.equals("ln")) {
            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInfors2Pro(pcid, collectTime, budget);
        }

        //处理数据，满足协议格式
        CollectMeter.processDataFields(collectMeterInfors);

        //3.推送数据
        if (collectMeterInfors != null && collectMeterInfors.size() > 0) {
            int size = collectMeterInfors.size();
            int num = size / 50 + 1;
            boolean result = size % 50 == 0;
            List<CollectMeter> subList_1;

            for (int i = 0; i < num; i++) {

                if (i == num - 1) {
                    if (result) {
                        break;
                    }
                    subList_1 = collectMeterInfors.subList(50 * i, size);
                } else {
                    subList_1 = collectMeterInfors.subList(50 * i, 50 * (i + 1));
                }
                //s = s + "#request List#:" + JSON.toJSON(collectMeterInfors);
                s = mssJsonClient.syncCollectMeterInfors(subList_1);
                //4.插入操作日志
//                insertLog("同步智能采集的电量数据", "collectOuter", s);
//                s = s + "#respone List#:" + JSON.toJSON(collectMeterInfors);
//                logger.debug("selectCollectMeterInfors:" + s);

            }
        }
        insertLog("同步智能采集数据", "sumForCollect", collectMeterInfors.size() + "条");
        return "同步采集数据调用完毕";

    }

    public String selectCollectMeterInforsPlus(Long pcid, String collectTime, String budget, List<String> stationcodes) {
        String s = "";
        //1.组装数据 辽宁，四川一致
        List<CollectMeter> collectMeterInfors = null;

        if (deployTo.equals("sc")) {
            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlus(pcid, collectTime, stationcodes);
            log.info("有{}个局站采集数据从能耗采集，准备传输", collectMeterInfors.size());
        }
        if (deployTo.equals("ln")) {
            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforLnPlus(pcid, collectTime, budget, stationcodes);
            log.info("在计量设备表有{}个局站匹配，准备传输", collectMeterInfors.size());
        }

        //处理数据，满足协议格式
        //CollectMeter.initData(collectMeterInfors);
        CollectMeter.processDataFields(collectMeterInfors);

        //3.推送数据
        if (collectMeterInfors != null && collectMeterInfors.size() > 0) {
            int size = collectMeterInfors.size();
            int num = size / 50 + 1;
            boolean result = size % 50 == 0;
            List<CollectMeter> subList_1;

            for (int i = 0; i < num; i++) {

                if (i == num - 1) {
                    if (result) {
                        break;
                    }
                    subList_1 = collectMeterInfors.subList(50 * i, size);
                } else {
                    subList_1 = collectMeterInfors.subList(50 * i, 50 * (i + 1));
                }
                //s = s + "#request List#:" + JSON.toJSON(collectMeterInfors);
                s = mssJsonClient.syncCollectMeterInforsPlus(subList_1, budget);
                //4.插入操作日志
//                insertLog("同步智能采集的电量数据", "collectOuter", s);
//                s = s + "#respone List#:" + JSON.toJSON(collectMeterInfors);
//                logger.debug("selectCollectMeterInfors:" + s);

            }
        }
        insertLog("同步智能采集数据", "sumForCollect", collectMeterInfors.size() + "条");
        return "同步采集数据调用完毕";

    }

    public String selectCollectMeterInforsPlusPro(Long pcid, String collectTime, String budget, List<String> stationcodes, List<CollectMeter> synccollectMeters, Integer timeFlag) {
        log.info("开始准备{}-{} -> collecter数据", budget, collectTime);
        List<CollectMeter> collectMeterInfors = new ArrayList<>();

        if (deployTo.equals("sc")) {
            if ("20240101".equals(collectTime)) {
                OperationLogHelper.getInstance().save(
                        String.format("%s->%s采集传送日志最终结果,timeFlag=%d", budget, collectTime, timeFlag), "syncLog", JSON.toJSONString(stationcodes), true
                );
            }
            if (timeFlag == 1) {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupAmmeterpol(pcid, collectTime, budget, stationcodes);
            } else if (timeFlag == 2) {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupStationCode(pcid, collectTime, budget, stationcodes);
            } else if (timeFlag == 3) {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupAll(pcid, collectTime, budget, stationcodes);
            }
            log.info("{}-{}采集数据{}条准备完毕，准备推送", budget, collectTime, collectMeterInfors.size());
        }
        if (deployTo.equals("ln")) {
            if (timeFlag == 1) {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupAmmeterpol(pcid, collectTime, budget, stationcodes);
            } else if (timeFlag == 2) {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupStationCode(pcid, collectTime, budget, stationcodes);
            } else if (timeFlag == 3) {
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupAll(pcid, collectTime, budget, stationcodes);
            }
            log.info("{}-{}采集数据{}条准备完毕，准备推送", budget, collectTime, collectMeterInfors.size());
        }

        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志db加载", budget, collectTime), "syncLog", JSON.toJSONString(stationcodes), true
            );
        }

        collectMeterInfors = collectMeterInfors.stream().flatMap(
                item -> item.flatMap()
        ).collect(toList());

        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志程序拆分", budget, collectTime), "syncLog", JSON.toJSONString(collectMeterInfors), true
            );
        }

        log.info("获取{}账期应该采集，但是却未出现jt表中的数据", budget);
        List<CollectMeter> tempList = collectMeterInfors.stream().filter(
                item -> item.filterCityAndCountryOpposite(item)
        ).collect(toList());
        List<CollectMeter> otherList = CollectMeter.tempList(tempList, synccollectMeters);
//        List<CollectMeter> otherList2 = CollectMeter.tempList2(tempList, otherList);

        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志空值构造", budget, collectTime), "syncLog", JSON.toJSONString(otherList), true
            );
        }

        log.info("过滤掉未在jt表中找到采集数据");
        log.info("过滤前 {}条", collectMeterInfors.size());
        collectMeterInfors = collectMeterInfors.stream().filter(
                item -> item.filterCityAndCountry(item)
        ).collect(toList());
        log.info("过滤后 {}条", collectMeterInfors.size());
        log.info("过滤结束");


        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志空值过滤", budget, collectTime), "syncLog", JSON.toJSONString(collectMeterInfors), true
            );
        }

        log.info("获取每日采集stationcode相同但city+country不同的采集数据");
        List<CollectMeter> tempAddCollects = CollectMeter.addCollects(collectMeterInfors, synccollectMeters);
        log.info("加入采集列表");
        collectMeterInfors.addAll(tempAddCollects);
        collectMeterInfors.addAll(otherList);

        log.info("处理数据，满足协议格式");
        CollectMeter.processDataFields(collectMeterInfors);
        collectMeterInfors = CollectMeter.processEnergyData(collectMeterInfors);


        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志最终结果", budget, collectTime), "syncLog", JSON.toJSONString(collectMeterInfors), true
            );
        }


        log.info("推送数据");
        int bitsize = 200;
        List<CollectMeter> finalCollectMeterInfors = collectMeterInfors;
        Map<Integer, List<CollectMeter>> dataMap = IntStream.range(0, collectMeterInfors.size()).boxed().collect(
                groupingBy(
                        i -> i / bitsize + 1,
                        mapping(i -> finalCollectMeterInfors.get(i), toList())
                )
        );
        dataMap.forEach(
                (k, v) -> {
                    log.info("{}-{}第{}批数据:{}条开始推送", budget, collectTime, k, v.size());
                    String res = mssJsonClient.syncCollectMeterInforsPlus(v, budget);
                    log.info("{}-{}第{}批数据:{}条推送结束,响应:{}", budget, collectTime, k, v.size(), res);
                }
        );
        return "同步采集数据调用完毕";
    }

    public String selectCollectMeterInforsPlusProPro(Long pcid, String collectTime, String budget, List<String> stationcodes, List<CollectMeter> synccollectMeters, Integer timeFlag, Integer interval) {
        log.info("开始准备{}-{} -> collecter数据", budget, collectTime);
        List<CollectMeter> collectMeterInfors = null;

        if (deployTo.equals("sc")) {
            if (timeFlag<5)
            {collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProPro(pcid, collectTime, budget, stationcodes);
                log.info("{}-{}采集数据{}条准备完毕，准备推送", budget, collectTime, collectMeterInfors.size());}
            else if(timeFlag==5)
            {collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProProConsist(pcid, collectTime, budget, stationcodes);
                log.info("{}-{}采集数据{}条准备完毕，准备推送", budget, collectTime, collectMeterInfors.size());}
            else if(timeFlag==6)
            {collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProProConsistjtmss(pcid, collectTime, budget, stationcodes);
                log.info("{}-{}采集数据{}条准备完毕，准备推送", budget, collectTime, collectMeterInfors.size());}
        }

        if (deployTo.equals("ln")) {
            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProPro(pcid, collectTime, budget, stationcodes);
            log.info("{}-{}采集数据{}条准备完毕，准备推送", budget, collectTime, collectMeterInfors.size());
        }

        log.info("处理数据，满足协议格式");
        CollectMeter.processDataFields(collectMeterInfors);
        collectMeterInfors = CollectMeter.processEnergyData(collectMeterInfors);


        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志最终结果", budget, collectTime), "syncLog", JSON.toJSONString(collectMeterInfors), true
            );
        }
        log.info("推送数据");
        int bitsize = 200;
        List<CollectMeter> finalCollectMeterInfors = collectMeterInfors;
        Map<Integer, List<CollectMeter>> dataMap = IntStream.range(0, collectMeterInfors.size()).boxed().collect(
                groupingBy(
                        i -> i / bitsize + 1,
                        mapping(i -> finalCollectMeterInfors.get(i), toList())
                )
        );
        dataMap.forEach(
                (k, v) -> {
                    // 集团收到数据有缺失，避免推送太快，每次间隔一定的时间
                    if (interval != null) {
                        try {
                            Thread.sleep(interval);
                        } catch (InterruptedException e) {
                            log.error("线程休眠异常", e);
                        }
                    }
                    log.info("{}-{}第{}批数据:{}条开始推送", budget, collectTime, k, v.size());
                    String res = mssJsonClient.syncCollectMeterInforsPlus(v, budget);
                    log.info("{}-{}第{}批数据:{}条推送结束,响应:{}", budget, collectTime, k, v.size(), res);
                }
        );
        return "同步采集数据调用完毕";
    }


    public String selectCollectMeterInfors(Long pcid) {
        String s = "";
        //1.组装数据 辽宁，四川一致
        List<CollectMeter> collectMeterInfors = new ArrayList<>();
        Integer sum = 0;
        if (deployTo.equals("sc")) {
 /*           sum = ammeterorprotocolMapper.getCollectMeterInforscountSC(pcid);
            int pageSum = (int) Math.ceil(sum * 1.0 / SYNCSUM);
            for (int j = 1; j <= pageSum; j++) {
                int offset = (j - 1) * SYNCSUM;*/

            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforsSC(pcid);
            //处理数据，满足协议格式
            CollectMeter.processDataFields(collectMeterInfors);
//            //过滤数据
//                collectMeterInfors.stream().filter(
//                        collectMeter -> collectMeter.getStationCode() != null
//                                && collectMeter.getStationCode() != ""
//                ).forEach(
//                        CollectMeter::check
//                );
            //3.推送数据
            if (collectMeterInfors != null && collectMeterInfors.size() > 0) {
                int size = collectMeterInfors.size();
                int num = size / 50 + 1;
                boolean result = size % 50 == 0;
                List<CollectMeter> subList_1;

                for (int i = 0; i < num; i++) {

                    if (i == num - 1) {
                        if (result) {
                            break;
                        }
                        subList_1 = collectMeterInfors.subList(50 * i, size);
                    } else {
                        subList_1 = collectMeterInfors.subList(50 * i, 50 * (i + 1));
                    }
                    //s = s + "#request List#:" + JSON.toJSON(collectMeterInfors);
                    s = mssJsonClient.syncCollectMeterInfors(subList_1);
                    //4.插入操作日志
//                insertLog("同步智能采集的电量数据", "collectOuter", s);
//                s = s + "#respone List#:" + JSON.toJSON(collectMeterInfors);
//                logger.debug("selectCollectMeterInfors:" + s);


                }
            }
        }
        if (deployTo.equals("ln")) {
            collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforsSC(pcid);

            //插入数据表collectmeter
            //int n = ammeterorprotocolMapper.insertByCollectMeter();

            //3.推送数据
            //过滤数据
            collectMeterInfors.stream().filter(
                    collectMeter -> collectMeter.getStationCode() != null
                            && collectMeter.getStationCode() != ""
            ).forEach(
                    CollectMeter::check
            );
            //处理数据，满足协议格式
            CollectMeter.processDataFields(collectMeterInfors);

            if (collectMeterInfors != null && collectMeterInfors.size() > 0) {
                int size = collectMeterInfors.size();
                int num = size / 50 + 1;
                boolean result = size % 50 == 0;
                List<CollectMeter> subList_1;

                for (int i = 0; i < num; i++) {

                    if (i == num - 1) {
                        if (result) {
                            break;
                        }
                        subList_1 = collectMeterInfors.subList(50 * i, size);
                    } else {
                        subList_1 = collectMeterInfors.subList(50 * i, 50 * (i + 1));
                    }
                    //s = s + "#request List#:" + JSON.toJSON(collectMeterInfors);
                    s = mssJsonClient.syncCollectMeterInfors(subList_1);
                    //4.插入操作日志
//                insertLog("同步智能采集的电量数据", "collectOuter", s);
//                s = s + "#respone List#:" + JSON.toJSON(collectMeterInfors);
//                logger.debug("selectCollectMeterInfors:" + s);

                }
            }
        }

        //2.日志输出数据
//        logger.debug("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));


        insertLog("同步智能采集数据", "sumForCollect", collectMeterInfors.size() + "条");
        return "同步采集数据成功";

    }

    /**
     * 按月同步 能耗抄表数据
     *
     * @param month
     * @return
     */
    public String selectCopyMeterInfors(int month) {
        String s = "";
        //1.组装数据
        String type = "1";
        String finalType = type;
        if ("sc".equals(deployTo)) {
            copyMeters = ammeterorprotocolMapper.getCopyMeterInforsForScCron(month);
        }
        if ("ln".equals(deployTo)) {
            copyMeters = ammeterorprotocolMapper.getCopyMeterInforsForLnCron(month);
        }
        //2.日志输出数据
//        logger.debug("syncEnergyMeterInfos" + JSON.toJSON(meterInfoByAmmeter));

        //3.推送数据
        if (copyMeters != null && copyMeters.size() > 0) {
//            s = s + "#request List#:" + JSON.toJSON(copyMeters);
            s = mssJsonClient.syncCopyMeterInfors(copyMeters);
            //4.插入操作日志
            //insertLog("同步省能耗管理平台中抄表数据" + id, "meterInfo", s);
            s = s + "#respone List#:" + JSON.toJSON(copyMeters);
            logger.debug("syncCopyMeterInfors:" + s);
        }
        return s;

    }

    /**
     * 定时调用存储过程向 power_station_avedayelec 插入数据
     *
     * @param maxMonth
     * @param min
     * @return
     */
    public String CallpowerStationAvedayelec(Integer maxMonth, Integer min) {
        ammeterorprotocolMapper.CallpowerStationAvedayelec(maxMonth, min);
        return "调用 存储过程power_station_avedayelec 成功";
    }

    public String syncRoomEnergyUse(String premonth, boolean delflag, String environment) {
        StringJoiner joiner = new StringJoiner("\n", "同步结果:\n", "||\n完毕");
        List<MachineRoomEnergyUseDTO> dtos = new ArrayList<>();
        if (delflag) {
            log.info("删除机房用电{}账期数据", premonth);
            int n1 = twocMapper.deleteMachineRoomInfo(premonth);
            log.info("重新生成机房用电{}账期数据", premonth);
            List<String> roomids = new ArrayList<>();
            if ("sc".equals(deployTo)) {
                roomids = Arrays.asList("574622261542131552");
            }
            if ("ln".equals(deployTo)) {
                roomids = Arrays.asList("574628146049980590",
                        "574628146049980598",
                        "574628146049980601",
                        "574628146049980602");
            }
            String provincecode =
                    "sc".equals(deployTo) ? "13" : "26";
            int n2 = twocMapper.CreateMachineRoomInfo(premonth, roomids, provincecode);
        }
        log.info("获取机房用电{}账期未同步数据", premonth);
        List<MachineRoomEnergyUseEntity> infoList = twocMapper.getMachineRoomInfo(premonth);

        Map<String, List<MachineRoomEnergyUseEntity>> cityNameMap = infoList.stream().collect(
                groupingBy(MachineRoomEnergyUseEntity::getCityName)
        );
        cityNameMap.forEach(
                (cityName, list) -> {
                    cityName = deployTo.equals("sc") && cityName.equals("省公司本部") ? "四川省公司本部"
                            : deployTo.equals("ln") && cityName.equals("省公司本部") ? "辽宁省公司本部"
                            : cityName;
                    String subjectCode = ReportingSubjectCodes.getSubjectCode(cityName);
                    subjectCode = StringUtils.isEmpty(subjectCode) ?
                            list.get(0).getCityCode() : subjectCode;

                    String msgid = subjectCode + System.currentTimeMillis();
                    MachineRoomEnergyUseDTO dto = new MachineRoomEnergyUseDTO();
                    dto.setSubjectCode(subjectCode);
                    dto.setMsgId(msgid);
                    dto.setRoomDatas(list);
                    dtos.add(dto);
                }
        );
        dtos.forEach(
                item -> {
                    String temp = mssJsonClient.
                            syncRoomEnergyUse(item, environment);
                    joiner.add(temp);
                }
        );

        return joiner.toString();
    }

    public String syncStationEnergyUse(String premonth, boolean delflag, String environment) {
        StringJoiner joiner = new StringJoiner("\n", "同步结果:\n", "||\n完毕");
        List<StationEnergyUseDTO> dtos = new ArrayList<>();
        if (delflag) {
            log.info("删除局站用电{}账期数据", premonth);
            int n1 = twocMapper.deleteStaionInfo(premonth);
            log.info("重新生成局站用电{}账期数据", premonth);
            List<String> stationids = new ArrayList<>();
            if ("sc".equals(deployTo)) {
                stationids = Arrays.asList("574622261542131552");
            }
            if ("ln".equals(deployTo)) {
                stationids = Arrays.asList("574628146049980590",
                        "574628146049980598",
                        "574628146049980601",
                        "574628146049980602");
            }
            String provincecode =
                    "sc".equals(deployTo) ? "13" : "26";
            int n2 = twocMapper.CreateStationInfo(premonth, stationids, provincecode);
        }
        log.info("获取局站用电{}账期未同步数据", premonth);
        List<StationEnergyUseEntity> infoList = twocMapper.getStaionInfo(premonth);

        Map<String, List<StationEnergyUseEntity>> cityNameMap = infoList.stream().collect(
                groupingBy(StationEnergyUseEntity::getCityName)
        );
        cityNameMap.forEach(
                (cityName, list) -> {
                    cityName = deployTo.equals("sc") && cityName.equals("省公司本部") ? "四川省公司本部"
                            : deployTo.equals("ln") && cityName.equals("省公司本部") ? "辽宁省公司本部"
                            : cityName;
                    String subjectCode = ReportingSubjectCodes.getSubjectCode(cityName);
                    subjectCode = StringUtils.isEmpty(subjectCode) ?
                            list.get(0).getCityCode() : subjectCode;

                    String msgid = subjectCode + System.currentTimeMillis();
                    StationEnergyUseDTO dto = new StationEnergyUseDTO();
                    dto.setSubjectCode(subjectCode);
                    dto.setMsgId(msgid);
                    dto.setRoomDatas(list);
                    dtos.add(dto);
                }
        );
        dtos.forEach(
                item -> {
                    String temp = mssJsonClient.
                            syncStaionEnergyUse(item, environment);
                    joiner.add(temp);
                }
        );

        return joiner.toString();
    }

    @Override
    public void nonElectricUniflowCallBack(WFModel wfModel) throws Exception {
        logger.debug("-----------------lt:" + wfModel.toString());
        if ("PROCESS_STARTED".equals(wfModel.getCallbackType())) {//更新流程Id  流程 提交
            try {
                doStartFlow(wfModel.getBusiId(), wfModel.getProcInstId());
                logger.debug("非电费报账单新增" + wfModel.getBusiId());
            } catch (NumberFormatException e) {
                e.printStackTrace();
                logger.error("提交流程失败:" + e.getMessage());
                throw new BaseException("提交流程失败:" + e.getMessage());//
            }
        } else if ("sys".equals(wfModel.getVariables().get("appointUserId"))) {
            try {// 送财辅接口
                sendToMssNonelectric(Long.valueOf(wfModel.getBusiId()));
                logger.debug("送财辅接口(非电费)" + wfModel.getBusiId());
            } catch (Exception e) {
                e.printStackTrace();
                doErrorFlow(wfModel.getBusiId());
                insertLog("提交财辅系统接口异常(非电费)", "nonElectricUniflowCallBack", e.getMessage());
                logger.error("提交财辅系统接口异常(非电费)，提交失败" + e.getMessage());
                throw e;
            }
        } else if ("PROCESS_COMPLETED".equals(wfModel.getCallbackType())) {//
//            流程完成后执行的回调  不在这里处理
//            改为系统自动轮询结果 getStausBybills
//            try {
//                doEndFlow(wfModel.getBusiId());
//            } catch (Exception e) {
//                e.printStackTrace();
//                throw new BaseException("提交流程失败:" + e.getMessage());//
//            }
        } else if ("TURNBACK_TO_START".equals(wfModel.getCallbackType())) {//流程 退回
            doExistFlow(wfModel.getBusiId());
        } else if ("PROCESS_CANCELLED".equals(wfModel.getCallbackType())) {//流程 终止
            doKillFlow(wfModel.getBusiId());
        }
    }

    @Override
    public void sendToMssNonelectric(Long id) throws Exception {
        MssAccountbill bill = null;
        try {
            // 获取报账单 包含 明细 归集单 挑对 外部收款人 等
            bill = mssAccountbillService.getByid(id);
            checkMssAccountbill(bill);
            // 接口操作 普通报账
            String sendbillXML = sendNonElectricBillXML(bill);
            System.out.println(sendbillXML);
            String resbillXML = doMssHttp(sendbillXML);
            logger.info("接收到响应结果:{}", resbillXML);

            logger.info("sendToMssNonelectric : insertXML start");
            insertXML(id.toString(), sendbillXML, resbillXML, "sendBill");
            Map<String, Object> map = prasebillXML(resbillXML);//ResultItem
            if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
                LinkedList<Map<String, Object>> items = (LinkedList<Map<String, Object>>) map.get("items");
                Map<String, Object> ResultItem = items.get(0);
                //  选项：1-调用成功，0-调用失败
                //说明：当bizMessage结构中的TYPE为S时，则触发的所有报账单数据校验通过，系统已接受等待生成报账单，
                // 当TYPE为E时，则触发的所有报账单数据校验不通过，
                // 对于ResultItem结构中的resultCode反馈为1的数据可重新触发，报账单数据校验通过，可以发起报账，
                // 于resultCode反馈为0的数据，可通过ResultItem结构中的errorMsg查看校验不通过的错误信息。
                if (ResultItem != null && "1".equals(ResultItem.get("resultCode"))) {
                    // 修改状态
                    bill.setId(id);
                    bill.setStatus(-4);//等待生成 此时可能没有报账单号
                    if (ResultItem.get("writeoffInstanceCode") != null)
                        bill.setWriteoffInstanceCode(ResultItem.get("writeoffInstanceCode").toString());//报账单号
                    billMapper.updateForModel(bill);
                    insertLog("非电费报账单送财辅成功" + bill.getId(), "sendToMssNonelectric", null);
                    //logger.debug("送财辅成功:" + "【" + bill.getId() + "】");
                } else {
                    bill.setId(id);
                    bill.setStatus(-3);// 送财辅失败
                    billMapper.updateForModel(bill);
                    insertLog("非电费报账单送财辅失败 数据异常", "sendToMssNonelectric", JSON.toJSON(map).toString());
                    logger.error("非电费报账单送财辅失败 数据异常:" + JSON.toJSON(map).toString() + "【" + bill.getId() + "】");
                }
            } else {
                bill.setId(id);
                bill.setStatus(-3);// 送财辅失败
                MssAccountbill mssAccountbill = mssAccountbillService.get(id);
                if (mssAccountbill != null && mssAccountbill.getStatus() == 2)// 避免 多次回调造成的 送财辅失败
                    billMapper.updateForModel(bill);
                logger.error("非电费报账单送财辅失败 接口异常:" + JSON.toJSON(map).toString() + "【" + bill.getId() + "】");
            }
        } catch (Exception e) {
            logger.info("sendToMss  doMssHttp Exception:{}" + e);
            bill = new MssAccountbill();
            bill.setId(id);
            bill.setStatus(-3);// 送财辅失败
            billMapper.updateForModel(bill);
            insertLog("非电费报账单送财辅失败 系统异常，报账单:【" + bill.getId() + "】", "sendToMssNonelectric", e.getMessage());
            logger.error("非电费报账单送财辅失败 系统异常:" + e.getMessage() + "【" + bill.getId() + "】");
            throw e;
        }
    }

    public String sendNonElectricBillXML(MssAccountbill bill) {
        String baseinfo = setBaseinfo(sn_bill);
        String requestMessage = setNonElectricRequestMessage(bill);
        String res = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
                "xmlns:ser=\"http://services.allcommonwriteoff.ws.dakar.eshore.com/\">" +
                "<soapenv:Header/>" +
                "<soapenv:Body>" +
                "<ser:OP_AutoCreateWriteoff>" +
                "<I_REQUEST>" +
                baseinfo +
                "<MESSAGE>" +
                "<bizMessage>" +
                requestMessage +
                "</bizMessage>" +
                "</MESSAGE>" +
                "</I_REQUEST>" +
                "</ser:OP_AutoCreateWriteoff>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";
        res = res.replace("null", "");
        return res;
    }

    public String setNonElectricRequestMessage(MssAccountbill bill) {
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        StringBuilder item = new StringBuilder();
        item.append("<otherSystemMainId>").append(bill.getId()).append("</otherSystemMainId>");//外围系统主单
        // 改为 hrloginid 放到 guid
        String guid = bill.getGuid();
        if ("ln".equals(deployTo)) {
            guid = guid + orgEndStr;
        }
        item.append("<account>").append(guid).append("</account>");
        item.append("<fillInName>").append(bill.getFillInName()).append("</fillInName>");
        item.append("<fillInOrgCode>").append(bill.getFillInCostCenterId()).append("</fillInOrgCode>");
        //可以通过“获取报账组织信息”接口获取
        item.append("<sapCompayCode>").append(bill.getCompanyNameTxt()).append("</sapCompayCode>");
        item.append("<economicItemCode>").append(bill.getAccountCode()).append("</economicItemCode>");//经济事项编码 可由业务提供
        if (bill.getEnergytype().doubleValue() == 1) {
            item.append("<economicItemName>").append("水费").append("</economicItemName>");//经济事项名称
        } else if (bill.getEnergytype().doubleValue() == 2) {
            item.append("<economicItemName>").append("气费").append("</economicItemName>");//经济事项名称
        } else if (bill.getEnergytype().doubleValue() == 3) {
            item.append("<economicItemName>").append("油费").append("</economicItemName>");//经济事项名称
        }
        item.append("<paymentType>").append(bill.getPaymentType()).append("</paymentType>");//收支方式
        item.append("<happenDate>").append(bill.getHappenDate()).append("</happenDate>");//费用发生日 YYYY-MM-DD
        item.append("<budgetSet>").append(bill.getBudgetsetname()).append("</budgetSet>");//报账期间 YYYY-MM
        item.append("<bizType>").append(bill.getBizTypeCode()).append("</bizType>");//业务类型 选项：0-列账，1-付款,2-列并付
        if ("0".equals(bill.getBizTypeCode())) {
            item.append("<isPayment>").append("0").append("</isPayment>");//是否付款	非必须	0-否，1-是，2-其他********新增
        } else {
            item.append("<isPayment>").append("1").append("</isPayment>");//是否付款	非必须	0-否，1-是，2-其他********新增
        }
        item.append("<isStaffPayment>").append(bill.getIsStaffPayment()).append("</isStaffPayment>");//是否员工代垫
        double sum = bill.getSum() == null ? 0 : bill.getSum().doubleValue();
        double inputTaxSum = bill.getInputTaxSum() == null ? 0 : bill.getInputTaxSum().doubleValue();
        item.append("<sum>").append(nf.format(sum + inputTaxSum)).append("</sum>");//
        if ("sc".equals(deployTo)) {
            String accountsedate = "-";
            try {
                accountsedate = rBillitemAccountMapper.selectAccountsedate(bill.getId());
            } catch (Exception e) {
            }
            if (!"-".equals(accountsedate))
                item.append("<desc>").append(bill.getAbstractValue() + "【" + BILLNAMES[bill.getBilltype().intValue() - 1] + "】").append("【" + accountsedate + "】").append("</desc>");
            else
                item.append("<desc>").append(bill.getAbstractValue() + "【" + BILLNAMES[bill.getBilltype().intValue() - 1] + "】").append("</desc>");
        } else
            item.append("<desc>").append(bill.getAbstractValue()).append("</desc>");
        item.append("<pickingMode>").append(bill.getPickingMode()).append("</pickingMode>");
        item.append("<invoiceType>").append(bill.getInvoiceType()).append("</invoiceType>");//票据类型
        item.append("<isNeedImage>").append("1").append("</isNeedImage>");//是否需要手工扫描 0-否，1-是
        item.append("<isRealGift>").append(bill.getIsExistKindGift()).append("</isRealGift>");//是否存在实物赠送
        item.append("<businessHappenTimeFlag>").append(bill.getBusihappendtimeflag()).append(
                "</businessHappenTimeFlag>");//业务发生时间点标志
        item.append("<realGiftSum>").append(bill.getKindGiftSum() == null ? 0 :
                nf.format(bill.getKindGiftSum().doubleValue())).append(
                "</realGiftSum>");//是否存在实物赠送
        item.append("<realGiftTaxSum>").append(bill.getKindGiftTaxSum() == null ? 0 :
                nf.format(bill.getKindGiftTaxSum().doubleValue())).append(
                "</realGiftTaxSum>");//实物赠送税额
        item.append("<isInputTax>").append(bill.getIsInputTax()).append("</isInputTax>");//是否涉及进项税转出
        if ("1".equals(bill.getIsInputTax())) {//是否进项税转出为否时，进项税转出税额和进项税转出业务类型不需要传值
            item.append("<inputTaxTurnSum>").append(bill.getInputTaxTurnSum() == null ? 0 :
                    nf.format(bill.getInputTaxTurnSum().doubleValue())).append("</inputTaxTurnSum>");//进项税转出税额
            item.append("<inputTaxTurnBizType>").append(bill.getInputTaxTurnBizType()).append("</inputTaxTurnBizType" +
                    ">");//进项税转出业务类型
        }
        item.append("<isEmergency>").append(bill.getIsEmergency()).append("</isEmergency>");//是否加急
        if (StringUtils.isNotEmpty(bill.getContractno())) {
            item.append("<contractNo>").append(bill.getContractno()).append("</contractNo>");
            item.append("<contractName>").append(bill.getContractName()).append("</contractName>");
        } else {
            item.append("<contractNo/>").append("<contractName/>");
        }
        //1为一单清
        //0和空为非一单清 // 税额拆分到明细当中
        item.append("<singleClearFlag>").append("1").append("</singleClearFlag>");
        String paytype = "1";
        // (1-总账、2-供应商、3-客户)
        if (StringUtils.isNotEmpty(bill.getSuppliertype())) {// 报账单新增时控制
            if ("1".equals(bill.getSuppliertype())) {
                paytype = "2";
            } else {
                paytype = "3";
            }
        }
        StringBuilder relateSupplier = new StringBuilder();
        if (StringUtils.isNotEmpty(bill.getSupplierCode())) {
            relateSupplier.append("<relateSuppliers><relateSupplier>");
            relateSupplier.append("<supplierCode>").append(bill.getSupplierCode()).append("</supplierCode>");
            relateSupplier.append("<supplierName>").append(bill.getSupplierName()).append("</supplierName>");
            relateSupplier.append("<accountType>").append(paytype).append("</accountType>");
            relateSupplier.append("<sum>").append(sum + inputTaxSum).append("</sum>");
            relateSupplier.append("</relateSupplier></relateSuppliers>");
            // 供应商结束
        }
        StringBuilder lineItems = new StringBuilder();//明细
        List<MssAccountbillitem> lineItemlist = bill.getItem();
        if ("ln".equals(deployTo) && "8".equals(bill.getBilltype().toString())) {
            //辽宁收款 U889 不传供应商判断
            for (MssAccountbillitem bitem : lineItemlist) {
                if ("8".equals(bitem.getUsageId())) {
                    relateSupplier = new StringBuilder();
                    break;
                }
            }
        }
        for (MssAccountbillitem bitem : lineItemlist) {
            lineItems.append("<lineItem>");
            lineItems.append("<otherSystemDetailId>").append(bitem.getId()).append("</otherSystemDetailId>");
            if (bill.getEnergytype().doubleValue() == 1) {//水
                switch (bitem.getUsageId()) {
                    case "1":// 经营管理用
                        lineItems.append("<usageCode>").append("U165").append("</usageCode>");
                        break;
                    case "5"://进项税
                        lineItems.append("<usageCode>").append("U888").append("</usageCode>");
                        break;
                    case "21"://支付代垫外单位或员工水费
                        lineItems.append("<usageCode>").append("U169").append("</usageCode>");
                        break;
                    case "22"://收到外单位或员工还代垫水费
                        lineItems.append("<usageCode>").append("U170").append("</usageCode>");
                        break;
                    case "23"://代征污水处理费
                        lineItems.append("<usageCode>").append("U171").append("</usageCode>");
                        break;
                    case "24"://代征垃圾处理费
                        lineItems.append("<usageCode>").append("U172").append("</usageCode>");
                        break;
                    default:
                        //lineItems.append("<usageCode>").append("</usageCode>");
                        break;
                }
            } else { //气 煤
                if ("1".equals(bitem.getUsageId())) {// 经营管理用
                    lineItems.append("<usageCode>").append("U165").append("</usageCode>");
                } else if ("2".equals(bitem.getUsageId())) { //进项税
                    lineItems.append("<usageCode>").append("U888").append("</usageCode>");
                }/*else{
                    lineItems.append("<usageCode>").append("</usageCode>");
                }*/
            }
            if ("sc".equals(deployTo)) {
                lineItems.append("<orderNo>").append(bitem.getOrderNo()).append("</orderNo>");
                if (bitem.getUsageId().equals("2") && !("11".equals(bill.getBilltype().toString())))   //代垫增加统御科目210128
                {
                    lineItems.append("<preAccountCode>").append(bitem.getReconciliationAccountCode()).append("</preAccountCode>");
                    lineItems.append("<preAccountName>").append(bitem.getReconciliationAccountName()).append("</preAccountName>");
                }
                if ("11".equals(bill.getBilltype().toString())) {
                    if (StringUtils.isNotEmpty(bitem.getDebitAccountCode())) {
                        //(1-总账、2-供应商、3-客户) 此处固定为 3
                        if (StringUtils.isNotEmpty(bitem.getReservedOne())) {
                            lineItems.append("<debitAccountType>").append(bitem.getReservedOne()).append("</debitAccountType>");
                        } else {
                            lineItems.append("<debitAccountType>").append("3").append("</debitAccountType>");
                        }
                        lineItems.append("<debitAccountCode>").append(bitem.getDebitAccountCode()).append("</debitAccountCode>");
                        lineItems.append("<debitAccountName>").append(bitem.getDebitAccountName()).append("</debitAccountName>");
                    }
                }
            }
            lineItems.append("<usageName>").append(bitem.getUsageName()).append("</usageName>");
            lineItems.append("<budgetType>").append(bitem.getBudgetType()).append("</budgetType>");
            if ("ln".equals(deployTo) && "8".equals(bill.getBilltype().toString()) && "8".equals(bitem.getUsageId())) {//辽宁收款 U889进项税（集成）
                lineItems.append("<priceSum>").append(bitem.getTaxAdjustSum() == null ? 0 :
                        nf.format(bitem.getTaxAdjustSum().doubleValue())).append("</priceSum>");
                lineItems.append("<inputTaxSum>").append("0").append("</inputTaxSum>");
            } else {
                lineItems.append("<priceSum>").append(bitem.getSum() == null ? 0 :
                        nf.format(bitem.getSum().doubleValue())).append("</priceSum>");
                lineItems.append("<inputTaxSum>").append(bitem.getTaxAdjustSum() == null ? 0 :
                        nf.format(bitem.getTaxAdjustSum().doubleValue())).append("</inputTaxSum>");
            }
            lineItems.append("<sum>").append(nf.format(bitem.getTaxAdjustSum() == null ? 0 :
                    bitem.getTaxAdjustSum().doubleValue() + (bitem.getSum() == null ? 0 : bitem.getSum().doubleValue()))).append("</sum>");
            lineItems.append("<desc>").append(bitem.getAbstractValue()).append("</desc>");
            //  CW0686  转供电  CW1000 能耗费  CW5500 进项税 成本 23年修改为1011
            if ("1000".equals(bitem.getBudgetItemId())) {// 生产用	1budgetItemId
                lineItems.append("<budgetItemCode>").append("CW1011").append("</budgetItemCode>");
                lineItems.append("<budgetItemName>").append("能源费").append("</budgetItemName>");
            } else if ("1145".equals(bitem.getBudgetItemId())) { //管理用
                lineItems.append("<budgetItemCode>").append("CW1145").append("</budgetItemCode>");
                lineItems.append("<budgetItemName>").append("能源费").append("</budgetItemName>");
            } else if ("2000".equals(bitem.getBudgetItemId())) {//铁塔用 3
                lineItems.append("<budgetItemCode>").append("CW0686").append("</budgetItemCode>");
                lineItems.append("<budgetItemName>").append("转供电—铁塔公司").append("</budgetItemName>");
            } else {
                lineItems.append("<budgetItemCode>").append("CW5500").append("</budgetItemCode>");
                lineItems.append("<budgetItemName>").append("进项税 成本").append("</budgetItemName>");
            }

            lineItems.append("<budgetOrgCode>").append(bitem.getResponseCenterCode()).append("</budgetOrgCode>");
            lineItems.append("<budgetOrgName>").append(bitem.getResponseCenterName()).append("</budgetOrgName>");
            lineItems.append("<sapCostCenterCode>").append(bitem.getCostCenterCode()).append("</sapCostCenterCode>");
            lineItems.append("<sapCostCenterName>").append(bitem.getCostCenterName()).append("</sapCostCenterName>");
            lineItems.append("<count>").append(bitem.getAmount()).append("</count>");
            lineItems.append("<price>").append(bitem.getPrice() == null ? 0 :
                    nf.format(bitem.getPrice().doubleValue())).append("</price>");
            lineItems.append("</lineItem>");
        }
        StringBuilder payMentItems = new StringBuilder();//外部收款人
        List<MssAccountbillpayinfo> payinfo = bill.getPayinfo();// 供应商
        //是否员工代垫（0为否、1为是） 是员工代垫 不传银行信息 避免接口错误
        if (payinfo != null && payinfo.size() > 0 && "0".equals(bill.getIsStaffPayment())) {
            payMentItems.append("<payMentItems>");
            for (MssAccountbillpayinfo pay : payinfo) {
                payMentItems.append("<payMentItem>");
                payMentItems.append("<employeeBankAccount>").append(pay.getEmployeebankac()).append(
                        "</employeeBankAccount>");
                payMentItems.append("<employeeName>").append(pay.getEmployeename()).append("</employeeName>");
                // 因为 bankAddress 不存在 此处 为支行全称 前 四个字 总行简称
                payMentItems.append("<bank>").append(pay.getBank().substring(0, 4)).append("</bank>");
                //避免数据同步异常造成的接口异常 此处不传值 pay.getBankcode()
                if (StringUtils.isNotEmpty(pay.getRowno()) && pay.getRowno().matches("^[0-9]*$")) // 数据因为数据同步问题 截取 Rowno
                    payMentItems.append("<bankCode>").append(pay.getRowno().substring(0, 3)).append
                            ("</bankCode" + ">");
                payMentItems.append("<payeeCode>").append(pay.getPayeecode()).append("</payeeCode>");
//                1-对外对公，3-对外对私,2-对内
                if (StringUtils.isNotEmpty(pay.getAccountname())) {
                    String replaceAll = pay.getAccountname().replaceAll("\r|\n", "");
                    if ("对公".equals(replaceAll)) {
                        payMentItems.append("<accountType>").append(1).append("</accountType>");
                    } else if ("对私".equals(replaceAll)) {
                        payMentItems.append("<accountType>").append(3).append("</accountType>");
                    } else { //默认为对公
                        payMentItems.append("<accountType>").append(1).append("</accountType>");
                    }
                } else { //默认为对公
                    payMentItems.append("<accountType>").append(1).append("</accountType>");
                }
                payMentItems.append("<payeeType>").append(pay.getPayeetype()).append("</payeeType>");
                payMentItems.append("<rowno>").append(pay.getRowno()).append("</rowno>");
                // 因为 bankAddress 不存在 此处 为支行全称
                payMentItems.append("<bankAddress>").append(pay.getBank()).append("</bankAddress>");
                payMentItems.append("<province>").append(pay.getProvince()).append("</province>");
                payMentItems.append("<city>").append(pay.getCity()).append("</city>");
                payMentItems.append("<sum>").append(pay.getSum() == null ? 0 : nf.format(pay.getSum().doubleValue())).append("</sum>");
                payMentItems.append("</payMentItem>");
            }
            payMentItems.append("</payMentItems>");
        }
        StringBuilder openItemPickings = new StringBuilder();// 挑对
        List<MssAccountclearitem> clearitems = bill.getClearitem();
        String sapCostCenterCode = "";
        if (bill.getItem() != null) {
            sapCostCenterCode = bill.getItem().get(0).getCostCenterCode();
        }
        String profitCenterGroupCode = "";
        if (StringUtils.isNotEmpty(sapCostCenterCode)) {
            profitCenterGroupCode = sapCostCenterCode.substring(0, 5);
        }
        if (clearitems != null && clearitems.size() > 0) {
            openItemPickings.append("<openItemPickings>");
            for (MssAccountclearitem clearitem : clearitems) {
                openItemPickings.append("<openItemPicking>");
                openItemPickings.append("<sapCertificateCode>").append(clearitem.getSapcertificatecode()).append(
                        "</sapCertificateCode>");
                openItemPickings.append("<sapItemMun>").append(clearitem.getSapitemmun()).append("</sapItemMun>");
                openItemPickings.append("<tYear>").append(clearitem.getTyear()).append("</tYear>");
                openItemPickings.append("<companyCode>").append(clearitem.getCompanycode()).append("</companyCode>");
                openItemPickings.append("<accountCode>").append(clearitem.getAccountcode()).append("</accountCode>");
//                if (StringUtils.isNotEmpty(clearitem.getAccountcode()) && !clearitem.getAccountcode().startsWith
//                ("G")) {//(1-总账、2-供应商、3-客户)
//                    // 是G开头的不一定是供应商
//                    paytype = "3";// 不是G开头的一定是客户
//                }
                //选输(1-总账、2-供应商、3-客户),有科目时必须
                openItemPickings.append("<accountType>").append(paytype).append("</accountType>");
                openItemPickings.append("<otherSystemDetailId>").append(clearitem.getOthersystemdetailid()).append(
                        "</otherSystemDetailId>");
                openItemPickings.append("<pickingSum>").append(clearitem.getPickingsum() == null ? 0 :
                        nf.format(clearitem.getPickingsum().doubleValue())).append("</pickingSum>");
                //收款、调账、挂账支付 报账金额送挑对金额
                if ("3".equals(bill.getBilltype().toString()) || "8".equals(bill.getBilltype().toString()) || "11".equals(bill.getBilltype().toString()))
                    openItemPickings.append("<writeoffSum>").append(clearitem.getPickingsum() == null ? 0 :
                            nf.format(clearitem.getPickingsum().doubleValue())).append("</writeoffSum>");
                else
                    openItemPickings.append("<writeoffSum>").append(clearitem.getWriteoffsum() == null ? 0 :
                            nf.format(clearitem.getWriteoffsum().doubleValue())).append("</writeoffSum>");
                openItemPickings.append("<inputTaxSum>").append(clearitem.getInputtaxsum() == null ? 0 :
                        nf.format(clearitem.getInputtaxsum().doubleValue())).append("</inputTaxSum>");
                openItemPickings.append("<isLastWriteoff>").append(clearitem.getIslastwriteoff()).append(
                        "</isLastWriteoff>");
                openItemPickings.append("<profitCenterGroupCode>").append(profitCenterGroupCode).append(
                        "</profitCenterGroupCode>");
                openItemPickings.append("</openItemPicking>");
            }
            openItemPickings.append("</openItemPickings>");
        }

        String beforeHandSum = "0";//事前审批金额
        String processSuffixName = null;
        if ("ln".equals(deployTo)) // 辽宁 新能耗标识
            processSuffixName = "新能耗费集成";
        if ("sc".equals(deployTo)) {
            //省本部流程，送4400-FSJJSX_13，分公司流程才送FSJJSX_13
            if ("1000922".equals(bill.getCompanyCode())) {
                processCode = "4400-FSJJSX_13";
            } else {
                processCode = "FSJJSX_13";
            }
        }
        /*<![CDATA[*/
        String res = "<![CDATA[<requestMessage>" +
                "<processCode>" + processCode + "</processCode>" +
                "<processSuffixName>" + processSuffixName + "</processSuffixName>" +
                "<writeoffItems>" +
                "<item>" +
                item.toString() +
                relateSupplier.toString() +
                "<lineItems>" +
                lineItems.toString() +
                "</lineItems>" +
                payMentItems.toString() +
                openItemPickings.toString() +
                "</item>" +
                "</writeoffItems>" +
                "<beforeAuditCode/>" +
                "<beforeHandSum>" + beforeHandSum + "</beforeHandSum>" +
                "</requestMessage>]]>";
        res = res.replace("null", "");
        return res;
    }


    // 处理 轮询 接口 	获取报账单状态
    @Override
    public void getStausBybills3() {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(3);// 已生成SAP凭证
        // 查询 所有 送财辅中的报账单
        List<MssAccountbill> mssAccountbills = billMapper.selectListByAuto(bill);
        for (MssAccountbill mssbill : mssAccountbills) {
            try {
                handleMssInterface(mssbill);
            } catch (Exception e) {
                e.printStackTrace();
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                insertLog("报账轮询接口(查询报账单状态)报错，报账单:【" + mssbill.getId() + "】", "getStausBybills", e.getMessage());
                logger.error("报账轮询接口(查询报账单状态)报错，报账单:【" + mssbill.getId() + "】" + e.getMessage() + sw.toString());
            }
        }
    }

    @Override
    public void getStausBybillsPro3() {
        MssAccountbill bill = new MssAccountbill();
        bill.setStatus(3);// 已生成SAP凭证
        // 查询 所有 送财辅中的报账单
        List<MssAccountbill> mssAccountbills = billMapper.selectListByAuto(bill);
        mssAccountbills.forEach(
                item -> {
                    if (item.Unfinsh(item.getId(), executeMap3)) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(
                                () -> {
                                    try {
                                        executeMap3.put(item.getId(), BillExecuteState.EXECUTE);
                                        log.info("{}开始轮询", item.getId());
                                        handleMssInterfacePro3(item, executeMap3);
                                        log.info("{}轮询成功", item.getId());
                                    } catch (Exception e) {
                                        executeMap3.put(item.getId(), BillExecuteState.EXCEPTION);
                                        log.info("{}轮询失败", item.getId());
                                        throw new RuntimeException(e);
                                    }
                                }, taskPool3
                        );
                        executeMap3.put(item.getId(), BillExecuteState.EXIST);
                        log.info("{}加入线程池等待队列", item.getId());
                    }
                }
        );
    }

    public Map<String, Object> handleMssInterfacePro3(MssAccountbill mssbill, ConcurrentHashMap<Long, BillExecuteState> executeMap) throws Exception {
        String writeoffInstanceCode = mssbill.getWriteoffInstanceCode();
        String billId = mssbill.getId().toString();

        //根据 报账编码 外围主单id 查询报账单状态
//        String serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        if ("sc".equals(deployTo)) {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        } else if ("ln".equals(deployTo)) {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        } else {
//            serviceName = "SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus";
//        }

        String sendXML = sendwriteoffInstanceCodeXML(writeoffInstanceCode, billId, sn_status);
        String resXML = doMssHttp(sendXML);
        //System.out.println("handleMssInterface resXML"+resXML);
        Map<String, Object> map = null;
        if ("sc".equals(deployTo)) {
            map = praseOP_GainWriteoffInstStatus(resXML);
        } else if ("ln".equals(deployTo)) {
            map = praseOP_GainWriteoffInstStatus(resXML);
        } else {
            map = praseOP_GetWriteoffBaseDataResponse(resXML);
        }
        insertXML(billId, sendXML, resXML, "getStatus");
        MssAccountbill bill = new MssAccountbill();
        if ("S".equals(map.get("TYPE")) || "s".equals(map.get("TYPE"))) {
            bill.setId(mssbill.getId());
            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("items");
            Map<String, Object> item = items.get(0);

            // 能耗状态 状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,'退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单'

            if ("-4".equals(item.get("status"))) {//等待生成报账单中
                bill.setStatus(-4);
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
            } else if ("-3".equals(item.get("status"))) {//报账单发起失败
                bill.setStatus(-3);
                billMapper.updateForModel(bill);
            } else if ("-1".equals(item.get("status"))) {//报账单删除
//                bill.setStatus(-1);
                // 没有 财辅退单状态 财辅删除 直接对应能耗为财辅退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {


                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
            } else if ("-2".equals(item.get("status"))) {//财辅 退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {
                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
//                //更新抄表数据
//                mssInterfaceServiceImpl.selectWriteoffDetailInfo2(billId+"/update");
            } else if ("2".equals(item.get("status"))) {// 生成报账单成功

                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusFail);
                // 生成报账单成功
                bill.setStatus(3);//生成报帐单
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
                /* if ("2".equals(mssbill.getBizTypeCode()) || "1".equals(mssbill.getBizTypeCode())) {//支付类报账单送集团*/
                boolean budgetflag = false;
/*                List<MssAccountbillitem> mssbillitems=mssAccountbillService.getByid(mssbill.getId()).getItem();
                if (mssbillitems != null && mssbillitems.size() > 0) {
                    for (int i = 0; i <mssbillitems.size() ; i++) {
                        if ("1".equals(mssbillitems.get(i).getBudgetType()))
                            budgetflag=true;
                            break;
                    }
                }*/
                if ("1".equals(mssbill.getBilltype().toString()) || "2".equals(mssbill.getBilltype().toString()) ||
                        "3".equals(mssbill.getBilltype().toString()) || "5".equals(mssbill.getBilltype().toString()) || "6".equals(mssbill.getBilltype().toString()) || "7".equals(mssbill.getBilltype().toString()) || "8".equals(mssbill.getBilltype().toString()) || "9".equals(mssbill.getBilltype().toString()) || "10".equals(mssbill.getBilltype().toString()))
                    budgetflag = true;
                if (budgetflag) {//占成本类报账单送集团 *********/
                    try {
/*                        boolean senFlag1 = true;
                        boolean senFlag2 = true;
                        senFlag1 = billMapper.selectSendFalg1(billId);
                        senFlag2 = billMapper.selectSendFalg2(billId);
                        log.info(
                          "billid={}同步电表基础信息{}||同步电表用电信息{}",
                          billId, senFlag1 ? "执行" : "不执行", senFlag2 ? "执行" : "不执行"
                        );*/
                        syncEnergyMeterInfosBybill(mssbill.getId());//同步电表
                        selectWriteoffDetailInfo(mssbill.getId(), "1");// 同步报账单
//                        //更新抄表数据
//                        mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId()+"/"+"create");
                        //同步成功更新同步状态 Iresult 为 5 标识推送成功
                        bill.setIresult("5");
                        billMapper.updateForModel(bill);
                        mssInterfaceMapper.updateAmmeterno(mssbill.getId());// 更新电表 标识已经推送

                    } catch (Exception e) {
                        e.printStackTrace();
                        insertLog("同步集团电费化小失败，报账单:【" + mssbill.getId() + "】", "meterInfo", e.getMessage());
                        logger.error("报账单" + mssbill.getId() + " 同步集团电费化小失败：" + e.getMessage());
                    }
                }
            } else if ("3".equals(item.get("status"))) {//已生成SAP凭证
//                bill.setStatus(4);

                bill.setStatus(7);// 完成
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode").toString());// 财辅报账单号
                // 获取凭证信息 并记录
                updatebillYearMonth(bill);
                doEndFlow(bill); // 更新状态
                //updateprepaid(bill);
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "完成");// 走完流程

                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
                //更新抄表数据
                //try {
                //    //mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId() + "/" + "create");
                //} catch (Exception e) {
                //    //System.out.println("同步抄表数据失败");
                //    //insertLog("同步集团抄表数据", "collectError", "同步的报账id" + mssbill.getId());
                //}
            }
        }

        return map;

    }

    public Map<String, Object> handleMssInterfacePro3Test(MssAccountbill mssbill, ConcurrentHashMap<Long, BillExecuteState> executeMap) throws Exception {
        String writeoffInstanceCode = mssbill.getWriteoffInstanceCode();
        String billId = mssbill.getId().toString();

        MssAccountbill bill = new MssAccountbill();
        if (true) {
            bill.setId(mssbill.getId());
            List<Map<String, Object>> items = new ArrayList<>();

            Map<String, Object> temp = new HashMap();
            temp.put("status", "2");
            items.add(temp);
            Map<String, Object> item = items.get(0);

            // 能耗状态 状态1,'草稿',2,'待办',3,'生成报帐单',4,'生成凭证',7,'完成',5,'财务通过',-1,'报帐单删除',-2,'退单',-4,'等待生成,-3,'送财辅失败',8 '财辅退单'

            if ("-4".equals(item.get("status"))) {//等待生成报账单中
                bill.setStatus(-4);
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                billMapper.updateForModel(bill);
            } else if ("-3".equals(item.get("status"))) {//报账单发起失败
                bill.setStatus(-3);
                billMapper.updateForModel(bill);
            } else if ("-1".equals(item.get("status"))) {//报账单删除
//                bill.setStatus(-1);
                // 没有 财辅退单状态 财辅删除 直接对应能耗为财辅退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {


                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
            } else if ("-2".equals(item.get("status"))) {//财辅 退单
                bill.setStatus(8);
                bill.setIresult("-1");// 退单 标识
                doEndFlow(bill); // 更新状态
                //todo :修改预算历史状态
                //批量更新 报账单 对应报账历史明细
                if ("ln".equals(deployTo)) {
                    List<BudgetReimbursementHistory> list =
                            budgetReimbursementHistoryMapper.getListByMsId(bill.getId());

                    list.stream().forEach(b -> System.out.println(b));

                    if (list.size() != 0) {
                        int n = budgetReimbursementHistoryMapper.updateListFlag(list);
                        if (n != list.size()) {
                            logger.info("MsId={}的报账 预算历史 状态更新成功，但是数目可能不对，可能需到数据库核查", bill.getId());
                        }
                    } else {
                        // 注意 对应报账单无 报账明细数据
                        logger.info("MsId={}的报账单无 报账明细 数据，注意核查", bill.getId());
                    }
                }
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "财辅终止");// 终止流程
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
//                //更新抄表数据
//                mssInterfaceServiceImpl.selectWriteoffDetailInfo2(billId+"/update");
            } else if ("2".equals(item.get("status"))) {// 生成报账单成功

                if (mssbill.unfinshPool(executeMap)) {
                    bill.setStatus(3);//生成报帐单
                    bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode"));// 财辅报账单号
                    billMapper.updateForModel(bill);
                    /* if ("2".equals(mssbill.getBizTypeCode()) || "1".equals(mssbill.getBizTypeCode())) {//支付类报账单送集团*/
                    boolean budgetflag = false;
/*                List<MssAccountbillitem> mssbillitems=mssAccountbillService.getByid(mssbill.getId()).getItem();
                if (mssbillitems != null && mssbillitems.size() > 0) {
                    for (int i = 0; i <mssbillitems.size() ; i++) {
                        if ("1".equals(mssbillitems.get(i).getBudgetType()))
                            budgetflag=true;
                            break;
                    }/mssaccount/mssInterface/updatedoSyncSpanInfoFalg?doSyncSpanInfoFalg=1
                }*/
                    if ("1".equals(mssbill.getBilltype().toString()) || "2".equals(mssbill.getBilltype().toString()) ||
                            "3".equals(mssbill.getBilltype().toString()) || "5".equals(mssbill.getBilltype().toString()) || "6".equals(mssbill.getBilltype().toString()) || "7".equals(mssbill.getBilltype().toString()) || "8".equals(mssbill.getBilltype().toString()) || "9".equals(mssbill.getBilltype().toString()) || "10".equals(mssbill.getBilltype().toString()))
                        budgetflag = true;
                    if (budgetflag) {//占成本类报账单送集团 *********/

                        syncEnergyMeterInfosBybill(mssbill.getId());//同步电表
                        selectWriteoffDetailInfo(mssbill.getId(), "1");// 同步报账单

//                        //更新抄表数据
//                        mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId()+"/"+"create");
                        //同步成功更新同步状态 Iresult 为 5 标识推送成功
                        bill.setIresult("5");
                        billMapper.updateForModel(bill);
                        mssInterfaceMapper.updateAmmeterno(mssbill.getId());// 更新电表 标识已经推送
                    }

                }
                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusFail);
            } else if ("3".equals(item.get("status"))) {//已生成SAP凭证
//                bill.setStatus(4);

                bill.setStatus(7);// 完成
                bill.setWriteoffInstanceCode("" + item.get("writeoffInstanceCode").toString());// 财辅报账单号
                // 获取凭证信息 并记录
                updatebillYearMonth(bill);
                doEndFlow(bill); // 更新状态
                //updateprepaid(bill);
                User user = setUserForFlow();
                executeCompleteTask(mssbill.getProcessinstid(), user, "完成");// 走完流程

                executeMap.put(mssbill.getId(), BillExecuteState.GetStatusSuccess);
                //更新抄表数据
                //try {
                //    //mssInterfaceServiceImpl.selectWriteoffDetailInfo2(mssbill.getId() + "/" + "create");
                //} catch (Exception e) {
                //    //System.out.println("同步抄表数据失败");
                //    //insertLog("同步集团抄表数据", "collectError", "同步的报账id" + mssbill.getId());
                //}
            }
        }

        return null;

    }
    public String selectCollectMeterInforsPlusProFix(Long pcid, String collectTime, String budget, List<String> stationcodes, List<CollectMeter> synccollectMeters, Integer timeFlag) {
        log.info("开始准备{}-{}--collecter数据，需要查询的局站code：{}", budget, collectTime,stationcodes);

        List<CollectMeter> collectMeterInfors = new ArrayList<>();
        if (deployTo.equals("sc")) {
            if (timeFlag == 1) {
                log.info("timeFlag == 1 ,通过ammeter_protocol_id查询每天的电量");
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupAmmeterpol(pcid, collectTime, budget, stationcodes);
            } else if (timeFlag == 2) {
                log.info("timeFlag == 2 ,通过station查询每天的电量");
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupStationCode(pcid, collectTime, budget, stationcodes);
            } else if (timeFlag == 3) {
                log.info("timeFlag == 3 ,全量查询每天的电量");
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupAll(pcid, collectTime, budget, stationcodes);
            } else if (timeFlag == 4)
            {
                log.info("timeFlag == 4 ,全量查询每天的电量");
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupjwcheck(pcid, collectTime, budget, stationcodes);

            }
            else if (timeFlag == 5) {
                log.info("timeFlag == 5 ,全量查询每天的电量");
                collectMeterInfors = ammeterorprotocolMapper.getCollectMeterInforScPlusProProGroupjwcheckpro(pcid, collectTime, budget, stationcodes);
            }
            log.info("{}-{}采集数据{}条准备完毕，准备推送", budget, collectTime, collectMeterInfors.size());
        }

        // 获取到当前时间 若是当前月份则将算出的日均电量保存至collectmeter表中
        String nowMonth = DateUtil.format(new Date(), "yyyy-MM");
        // 筛选出city或者countyCode或者stationCode不为空的数据
        if (nowMonth.equals(budget)) {
            collectMeterInfors = collectMeterInfors.stream().filter(node -> {
                return StrUtil.isNotBlank(node.getCityCode()) && StrUtil.isNotBlank(node.getCountyCode())
                        && StrUtil.isNotBlank(node.getStationCode());
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collectMeterInfors)) {
                collectMeterInfors.forEach(node -> {
                    node.setCollectTime("0000");
                    ammeterorprotocolMapper.clearCollectMeterByCode(node);
                });
                ammeterorprotocolMapper.batchInsertAllCollectMeterInfo(collectMeterInfors);
            }

            return "日均电量保存至collectmeter表中";

        }

        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志db加载", budget, collectTime), "syncLog", JSON.toJSONString(stationcodes), true
            );
        }

        collectMeterInfors = collectMeterInfors.stream().flatMap(
                item -> item.flatMap()
        ).collect(toList());

        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志程序拆分", budget, collectTime), "syncLog", JSON.toJSONString(collectMeterInfors), true
            );
        }

        log.info("获取{}账期应该采集，但是却未出现jt表中的数据", budget);
        List<CollectMeter> tempList = collectMeterInfors.stream().filter(
                item -> item.filterCityAndCountryOpposite(item)
        ).collect(toList());
        List<CollectMeter> otherList = CollectMeter.tempList(tempList, synccollectMeters);
//        List<CollectMeter> otherList2 = CollectMeter.tempList2(tempList, otherList);

        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志空值构造", budget, collectTime), "syncLog", JSON.toJSONString(otherList), true
            );
        }

        log.info("过滤掉未在jt表中找到采集数据,过滤前 {}条", collectMeterInfors.size());
        collectMeterInfors = collectMeterInfors.stream().filter(
                item -> item.filterCityAndCountry(item)
        ).collect(toList());
        log.info("过滤后 {}条,过滤结束", collectMeterInfors.size());

        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志空值过滤", budget, collectTime), "syncLog", JSON.toJSONString(collectMeterInfors), true
            );
        }

        log.info("获取每日采集stationcode相同但city+country不同的采集数据");
        List<CollectMeter> tempAddCollects = CollectMeter.addCollects(collectMeterInfors, synccollectMeters);
        log.info("加入采集列表");
        collectMeterInfors.addAll(tempAddCollects);
        collectMeterInfors.addAll(otherList);

        log.info("开始传输数据,过滤后的每日电量数据：{}", collectMeterInfors);
        log.info("处理数据，满足协议格式");
        CollectMeter.processDataFields(collectMeterInfors);
        collectMeterInfors = CollectMeter.processEnergyData(collectMeterInfors);


        if ("20230101".equals(collectTime)) {
            OperationLogHelper.getInstance().save(
                    String.format("%s->%s采集传送日志最终结果", budget, collectTime), "syncLog", JSON.toJSONString(collectMeterInfors), true
            );
        }

        log.info("推送数据");
        int bitsize = 200;
        List<CollectMeter> finalCollectMeterInfors = collectMeterInfors;
        Map<Integer, List<CollectMeter>> dataMap = IntStream.range(0, collectMeterInfors.size()).boxed().collect(
                groupingBy(
                        i -> i / bitsize + 1,
                        mapping(i -> finalCollectMeterInfors.get(i), toList())
                )
        );
        dataMap.forEach(
                (k, v) -> {
                    log.info("{}-{}第{}批数据:{}条开始推送", budget, collectTime, k, v.size());
                    String res = mssJsonClient.syncCollectMeterInforsPlusFix(v, budget);
                    log.info("{}-{}第{}批数据:{}条推送结束,响应:{}", budget, collectTime, k, v.size(), res);
                }
        );
        return "同步采集数据调用完毕";
    }

    public String selectCollectMeterInforsPlusProFixQxm(List<CollectMeter> synccollectMeters, String budget) {
//        log.info("开始传输数据,过滤后的每日电量数据：{}", synccollectMeters);
        log.info("处理数据，满足协议格式");
        CollectMeter.processDataFields(synccollectMeters);
//        collectMeterInfors = CollectMeter.processEnergyData(collectMeterInfors);

        log.info("推送数据");
        List<List<CollectMeter>> partLists = Lists.partition(synccollectMeters, 2000);
        int i = 1;
        String res = "";
        for (List<CollectMeter> partList : partLists) {
            log.info("{}第{}批数据:{}条开始推送", budget, i, partList.size());
            res = mssJsonClient.syncCollectMeterInforsPlusFix(partList, budget);
            log.info("{}第{}批数据:{}条推送结束,响应:{}", budget, i, partList.size(), res);
            i += 1;
        }
        return res;
    }

    public String checkBillStations(long billId) {
        List<HashMap<String, Object>> stationList =  billMapper.getStationListByBillId(billId);
        String res = "";
        String stationTypeAbc = "1110,1120,1130,1210";//A\B\C\idc类
        boolean isQk = false;//是否包含强控项
        int cnt = 0;//总条数
        int cntQd = 0;//不在清单条数
        int cntPp = 0;//信息不符条数
        String resQd = "";
        if (CollectionUtil.isNotEmpty(stationList)) {
            List<MeterInfo3> meterInfoList = new ArrayList<>();
            for (HashMap<String, Object> stationItem : stationList) {
                MeterInfo3 meterInfo = createMeterinfoByStation(stationItem);
                if (ObjectUtil.isNotEmpty(meterInfo)) {
                    //在meterinfo_all_jt中查询站点（优化为返回列表）
                    List<HashMap<String, Object>> meterInfoDbList = meterinfoMapper.getMeterInfoListByStationCode(meterInfo.getEnergyMeterCode());
                    if (CollectionUtil.isEmpty(meterInfoDbList)) {
                        //不在meterinfo_all_jt表中
                        meterInfo.setType("1");
                        //判断是否包含强控项
                        if(stationTypeAbc.contains(meterInfo.getStationType())){
                            resQd += "站址类型："+meterInfo.getStationType() +"，站址编码：" + meterInfo.getStationCode() + "，电表编码：" + meterInfo.getEnergyMeterCode() + "不在集团清单;\n";
                            isQk = true;
                        }
                        cnt +=1;
                        cntQd +=1;
                    } else {
                        // 对列表循环判断，检查是否有匹配的记录
                        boolean hasMatchingRecord = false;
                        HashMap<String, Object> matchedRecord = null;
                        for (HashMap<String, Object> old : meterInfoDbList) {
                            String oldElectrotype = ObjectUtil.isEmpty(old.get("stationType")) ? "" : old.get("stationType").toString();
                            // 检查各字段是否匹配
                            if (oldElectrotype.equals(meterInfo.getStationType()) &&
                                    ObjectUtil.equal(meterInfo.getCityCode(), old.get("cityCode").toString()) &&
                                    ObjectUtil.equal(meterInfo.getCountyCode(), old.get("countyCode").toString()) &&
                                    ObjectUtil.equal(meterInfo.getStationCode(), old.get("stationCode").toString()) &&
                                    ObjectUtil.equal(meterInfo.getStationStatus(), old.get("stationStatus").toString())) {
                                hasMatchingRecord = true;
                                matchedRecord = old;
                                break; // 找到匹配记录，跳出循环
                            }
                        }
                        if (!hasMatchingRecord) {
                            //信息有变更或无匹配记录
                            meterInfo.setType("2");
                            //判断是否包含强控项
                            if(stationTypeAbc.contains(meterInfo.getStationType())){
                                isQk = true;
                                resQd += "站址类型："+meterInfo.getStationType() +",站址编码：" + meterInfo.getStationCode() + "，电表编码：" + meterInfo.getEnergyMeterCode() + "与集团信息不符;\n";
                            }
                            cnt +=1;
                            cntPp +=1;
                        }
                    }
                    if (StrUtil.isNotBlank(meterInfo.getType())) {
                        meterInfoList.add(meterInfo);
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(meterInfoList)) {
                //上传基础信息
                CompletableFuture.runAsync(
                        () ->{
                            sendMeterInfoListnew(billId, meterInfoList);
                            // 集团可能会漏，做两次推送增加成功率
                            sendMeterInfoListnew(billId, meterInfoList);
                        }
                );
            }
        }
        if (cnt > 0) {
            res = "电费报账业务稽核失败：原因[报账电表不在集团能源平台清单中或与集团信息符]，异常电表数：" + cnt;
            if(cntQd > 0) {
                res += "，不在清单中电表数：" + cntQd;
            }
            if(cntPp > 0) {
                res += "，信息不符条数：" + cntPp + "。";
            } else {
                res += "。";
            }
            if (isQk) {
                //是否包含强控项
                res += "其中强控项清单：[" + resQd + "]";
                res += " 包含[集团强控项ABC及idc类]，不能提交账单，请上报支撑处理。";
            } else {
                res += " 已上报集团，可以提交报账单，如在财辅无法提交，请上报支撑处理。";
            }
        }
        return res;
    }

    private void sendMeterInfoList(List<MeterInfo3> meterInfoList) {
        try {
            log.info("需要上传基站信息数量----》{}", meterInfoList.size());
            String res = mssJsonClient.syncEnergyMeterInfos2New(null, meterInfoList);
            log.info("上传基站信息返回结果----》{}", res);
        } catch (Exception e) {
            log.info("上传集团基础信息出错----》{}", e.getMessage());
            e.printStackTrace();
        }
    }
    private void sendMeterInfoListnew(Long billid,List<MeterInfo3> meterInfoList) {
        try {
            log.info("需要上传基站信息数量----》{}", meterInfoList.size());
            String res = mssJsonClient.syncEnergyMeterInfos2New(billid, meterInfoList);
            log.info("上传基站信息返回结果----》{}", res);
        } catch (Exception e) {
            log.info("上传集团基础信息出错----》{}", e.getMessage());
            e.printStackTrace();
        }
    }

    private MeterInfo3 createMeterinfoByStation(HashMap<String, Object> data) {
        if (ObjectUtil.isEmpty(data.get("energyMeterCode")) || ObjectUtil.isEmpty(data.get("stationCode"))) {
            return null;
        }
        MeterInfo3 meterinfo = new MeterInfo3();
        meterinfo.setProvinceCode(S_PROVINCE);
        meterinfo.setCityCode(ObjectUtil.isEmpty(data.get("cityCode")) ? "" : data.get("cityCode").toString());
        meterinfo.setCityName(ObjectUtil.isEmpty(data.get("cityName")) ? "" : data.get("cityName").toString());
        meterinfo.setCountyCode(ObjectUtil.isEmpty(data.get("countyCode")) ? "" : data.get("countyCode").toString());
        meterinfo.setCountyName(ObjectUtil.isEmpty(data.get("countyName")) ? "" : data.get("countyName").toString());
        meterinfo.setStationCode(ObjectUtil.isEmpty(data.get("stationCode")) ? "" : data.get("stationCode").toString());
        meterinfo.setStationName(ObjectUtil.isEmpty(data.get("stationName")) ? "" : data.get("stationName").toString());
        meterinfo.setEnergyMeterCode(ObjectUtil.isEmpty(data.get("energyMeterCode")) ? "" : data.get("energyMeterCode").toString());
        meterinfo.setEnergyMeterName(ObjectUtil.isEmpty(data.get("energyMeterName")) ? "" : data.get("energyMeterName").toString());
        meterinfo.setEnergyType("45");
        meterinfo.setTypeStationCode(ObjectUtil.isEmpty(data.get("typeStationCode")) ? "" : data.get("typeStationCode").toString());
        meterinfo.setEnergySupplyWay(ObjectUtil.isEmpty(data.get("energySupplyWay")) ? "" : data.get("energySupplyWay").toString());
        meterinfo.setContractPrice(ObjectUtil.isEmpty(data.get("contractPrice")) ? "" : data.get("contractPrice").toString());
        if (StrUtil.isBlank(meterinfo.getContractPrice()) &&
                ObjectUtil.isNotEmpty(data.get("ammeterid")) &&
                StrUtil.isNotBlank(data.get("ammeterid").toString())) {
            //电价为空，通过最近台账获取电价
            BigDecimal price = billMapper.getAccountPrice(Long.parseLong(data.get("ammeterid").toString()));
            meterinfo.setContractPrice(price.toString());
        }
        meterinfo.setUsage(ObjectUtil.isEmpty(data.get("usageCopy")) ? "1" : data.get("usageCopy").toString());
        meterinfo.setStatus(ObjectUtil.isEmpty(data.get("status")) ? "" : data.get("status").toString());
        meterinfo.setStationStatus(ObjectUtil.isEmpty(data.get("stationStatus")) ? "" : data.get("stationStatus").toString());
        meterinfo.setStationLocation(ObjectUtil.isEmpty(data.get("stationLocation")) ? "" : data.get("stationLocation").toString());
        meterinfo.setStationType(ObjectUtil.isEmpty(data.get("stationType")) ? "" : data.get("stationType").toString());
        meterinfo.setLargeIndustrialElectricityFlag(ObjectUtil.isEmpty(data.get("largeIndustrialElectricityFlag")) ? "" : data.get("largeIndustrialElectricityFlag").toString());
        meterinfo.setPowerGridEnergyMeterCode(ObjectUtil.isEmpty(data.get("powerGridEnergyMeterCode")) ? "" : data.get("powerGridEnergyMeterCode").toString());
        meterinfo.setSiteCode(meterinfo.getStationCode());
        return meterinfo;
    }

    /**
     * 在网表计数据清单-新增申请-推送
     */
    @Override
    public void sendMeterInfo() {
        //查询需要推送的数据
        List<MeterInfo3> meterInfoList = meterinfoAllJtApplyMapper.meterInfoList(S_PROVINCE);
        if (CollectionUtil.isNotEmpty(meterInfoList)) {
            meterInfoList.forEach(item->{
                if("2".equals(item.getType())){
                    // 优化为返回列表进行循环判断
                    List<HashMap<String, Object>> meterInfoDbList = meterinfoMapper.getMeterInfoListByStationCode(item.getEnergyMeterCode());
                    if (CollectionUtil.isEmpty(meterInfoDbList)) {
                        item.setType("1");
                    } else {
                        // 使用第一条记录的ID（通常情况下应该只有一条记录）
                        item.setId(Long.parseLong(meterInfoDbList.get(0).get("id").toString()));
                    }
                }
            });
            //上传基础信息
            CompletableFuture.runAsync(
                    () -> sendMeterInfoList(meterInfoList)
            );
        }
    }

    /**
     * 业、财电量一致性稽核
     * @param billId 报账单id
     * @return
     */
    @Override
    public String checkBusMssCons(long billId) {
        //查询报账单内的局站财务日均电量及业务日均电量
        List<StationMssBussElectVo> stationList = billMapper.getStationMssBuss(billId);
        String res = "";
        String stationTypeAbc = "1110,1120,1130,1210";//A\B\C\idc类
        String stationTypeD14 = "1310,1320,1330,1411,1412,1421,1422,1431,1432";//D类及基站
        BigDecimal big10 = new BigDecimal("10");
        BigDecimal big20 = new BigDecimal("20");
        int cnt = 0;
        String resQd = "";
        boolean isQk = false;//是否包含强控项
        if (CollectionUtil.isNotEmpty(stationList)) {
            for (StationMssBussElectVo data : stationList) {
                BigDecimal rate = data.getRate();
                boolean isNormal = true;
                //A\B\C\idc类10%差异为正常
                if (stationTypeAbc.contains(data.getStationtype())) {
                    if (rate.compareTo(big10) > 0) {
                        isNormal = false;
                        isQk = true;
                        cnt += 1;
                        resQd += "站址编码：" + data.getStationcode();
                        resQd += "，站址名称：" + data.getStationName();
                        resQd += "，局站类型：" + data.getStationtype();
                        resQd += "，起始日期：" + data.getStartdate();
                        resQd += "，截止日期：" + data.getEnddate();
                        resQd += "，报账日均电量：" + data.getMssdata();
                        resQd += "，业务日均电量：" + data.getBussData();
                        resQd += "，差异：" + data.getRate() + "%";
                        resQd += "。超过集团稽核值10%;\n";
                    }
                }
                //D类及基站20%差异为正常
                if (isNormal && stationTypeD14.contains(data.getStationtype())) {
                    if (rate.compareTo(big20) > 0) {
                        isNormal = false;
                        cnt += 1;
                    }
                }
            }
        }
        if (cnt > 0) {
            res = "电费报账业务稽核失败：原因[业财电量不匹配]。不匹配记录数：" + cnt + "，";
            if (isQk) {
                //是否包含强控项
                res += "其中强控项清单：[" + resQd + "]";
                res += " 包含[集团强控项ABC及idc类]，不能提交账单，请上报支撑处理。";
            } else {
                res += " 已上报集团，可以提交报账单，如在财辅无法提交，请上报支撑处理。";
            }

        }
        return res;
    }

    /**
     * 检查账单报账电表是否在铁塔转供清单中
     * @param billId 报账单id
     * @return
     */
    @Override
    public String checkTower(long billId) {
        //检查账单报账电表是否在铁塔转供清单中
        List<CheckTowerVo> stationList = billMapper.checkTower(billId);
        String res = "";
        if (CollectionUtil.isNotEmpty(stationList)) {
            for (CheckTowerVo data : stationList) {
                if (StringUtils.isNotBlank(res)) {
                    res += "\n";
                }
                res += "项目编号：" + data.getAmmeterName();
                res += "，项目名称：" + data.getProjectname();
                res += "，对外结算类型：" + data.getDirectsupplyflag();
                res += "，站址类型：" + data.getStationtype();
                res += "，站址编码：" + data.getStationcode();
                res += "，站址名称：" + data.getStationName();
                res += "，5gr站址：" + data.getStationcode5gr() + ";";
            }
        }
        if (StrUtil.isNotBlank(res)) {
            res = "根据排查整治专项行动要求，即日暂停转供电报账，具体要求请关注支撑群公告或联系支撑。具体清单：[" + res + "]";
        }
        return res;
    }
}
