package com.sccl.modules.business.cost.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 基站电量业财一致率
 */
@Data
public class ConsistencyAuditTmpVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 稽核时间 yyyy-MM
     */
    private String auditTime;

    /**
     * 局站信息表主键id
     */
    private String stationId;

    /**
     * 局站名称
     */
    private String stationName;

    /**
     * 局站类型
     */
    private String stationType;

    /**
     * 稽核年份 yyyy
     */
    private String year;

    /**
     * 稽核月份 MM
     */
    private String month;

    /**
     * 数据标识 1机房 2基站
     */
    private String dataType;

    /**
     * 关联电表数
     */
    private Integer bzdbsl;

    /**
     * 所属分公司
     */
    private Long company;

    /**
     * 所属部门
     */
    private Long country;

    /**
     * 市局组织编码
     */
    protected String cityCode;

    /**
     * 区县组织编码
     */
    protected String countyCode;

    /**
     * 业务日均电量
     */
    private BigDecimal ywrjdl;

    /**
     * 财务日均电量
     */
    private BigDecimal cwrjdl;

    /**
     * 一致率
     */
    private String rate;

    /**
     * 一致率(不包含百分号)
     */
    private BigDecimal rateD;

    /**
     * 单据状态
     */
    private Integer status;

    /**
     * 开始日期
     */
    private String startdate;

    /**
     * 结束日期
     */
    private String enddate;
}
