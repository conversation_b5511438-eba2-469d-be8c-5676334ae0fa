package com.sccl.modules.mssaccount.mssinterface.domain;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TowerAccountVo {

    /**
     *  铁塔台账唯一ID
     */
    private String towerid;

    /**
     *  toweraccount 主键id
     */
    private Long id;

    /**
     *  地市
     */
    private String company;

    /**
     *  区县
     */
    private String country;

    /**
     *  铁塔站址编码
     */
    private String stationaddresscode;

    /**
     *  缴费户号
     */
    private String supplybureauammetercode;

    /**
     *  支付单号
     */
    private String paymentOrderNumber;

    /**
     *  用电起时间
     */
    private String startdate;

    /**
     *  用电止时间
     */
    private String enddate;

    /**
     *  用电起度
     */
    private BigDecimal prevtotalreadings;

    /**
     *  用电止度
     */
    private BigDecimal curtotalreadings;

    /**
     *  倍率
     */
    private BigDecimal multtimes;

    /**
     *  总用电量
     */
    private BigDecimal totalusedreadings;

    /**
     *  实际支付电费
     */
    private BigDecimal accountmoney;

    /**
     *  电信分摊比例
     */
    private BigDecimal percent;

    /**
     *  税款
     */
    private BigDecimal taxamount;

    /**
     *  价款
     */
    private BigDecimal price;

    /**
     *  地市
     */
    private String towerCity;

    /**
     *  区县
     */
    private String towerDistrict;

    /**
     *  铁塔站址编码
     */
    private String towersitecode;

    /**
     *  缴费户号
     */
    private String payaccountnum;

    /**
     *  用电起时间
     */
    private String usestarttime;

    /**
     *  用电止时间
     */
    private String useendtime;

    /**
     *  用电起度
     */
    private String usestartdegree;

    /**
     *  用电止度
     */
    private String useenddegree;

    /**
     *  倍率
     */
    private String magnification;

    /**
     *  总用电量
     */
    private String energyused;

    /**
     *  实际支付电费
     */
    private String actualpay;

    /**
     *  电信分摊比例
     */
    private String apportionmentratio;

    /**
     *  缴费票据类型
     */
    private String paymentreceipttype;

    /**
     *  抄表照片
     */
    private String meterreadingphoto;

    /**
     *  发票或收据
     */
    private String invoiceorreceipt;

    /**
     *  分摊确认单
     */
    private String apportionmentconfirmation;

    /**
     *  电量明细清单
     */
    private String powerdetaillist;

    /**
     *  电费分割单
     */
    private String electricitybillsplit;

    /**
     *  线损说明或合同截图
     */
    private String linelossexplanationorcontract;

    /**
     *  税款
     */
    private String telecomtax;

    /**
     *  价款
     */
    private String telecomprice;

    /**
     *  操作
     */
    private String operate;

    /**
     *  电信反馈
     */
    private String feedBack;

}