<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.cost.mapper.DeviationBusiMapper">
    <resultMap type="com.sccl.modules.business.cost.domain.DeviationBusi" id="DeviationBusiResult">
        <id property="id" column="id"/>
        <result property="auditTime" column="audit_time"/>
        <result property="stationCode" column="stationCode"/>
        <result property="stationName" column="stationName"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="cityCode" column="cityCode"/>
        <result property="cityName" column="cityName"/>
        <result property="countyCode" column="countyCode"/>
        <result property="countyName" column="countyName"/>
        <result property="power" column="power"/>
        <result property="createdBy" column="createdBy"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="selectVo">
        select id,
            audit_time,
            stationCode,
            stationName,
            `year`,
            `month`,
            cityCode,
            cityName,
            countyCode,
            countyName,
            power,
            createdBy,
            createTime,
            updateTime,
            del_flag
        from deviation_busi
    </sql>

    <sql id="other-condition">
        <if test="id != null">and id = #{id}</if>
        <if test="auditTime != null">and audit_time = #{auditTime}</if>
        <if test="stationCode != null">and stationCode = #{stationCode}</if>
        <if test="stationName != null">and stationName = #{stationName}</if>
        <if test="year != null">and `year` = #{year}</if>
        <if test="month != null">and `month` = #{month}</if>
        <if test="cityCode != null">and cityCode = #{cityCode}</if>
        <if test="cityName != null">and cityName = #{cityName}</if>
        <if test="countyCode != null">and countyCode = #{countyCode}</if>
        <if test="countyName != null">and countyName = #{countyName}</if>
        <if test="power != null">and power = #{power}</if>
        <if test="createdBy != null">and createdBy = #{createdBy}</if>
        <if test="createTime != null">and createTime = #{createTime}</if>
        <if test="updateTime != null">and updateTime = #{updateTime}</if>
        <if test="delFlag != null">and del_flag = #{delFlag}</if>
    </sql>

    <sql id="like-condition">
        <if test="id != null">and id like concat('%', #{id}, '%')</if>
        <if test="auditTime != null">and audit_time like concat('%', #{auditTime}, '%')</if>
        <if test="stationCode != null">and stationCode like concat('%', #{stationCode}, '%')</if>
        <if test="stationName != null">and stationName like concat('%', #{stationName}, '%')</if>
        <if test="year != null">and `year` like concat('%', #{year}, '%')</if>
        <if test="month != null">and `month` like concat('%', #{month}, '%')</if>
        <if test="cityCode != null">and cityCode like concat('%', #{cityCode}, '%')</if>
        <if test="cityName != null">and cityName like concat('%', #{cityName}, '%')</if>
        <if test="countyCode != null">and countyCode like concat('%', #{countyCode}, '%')</if>
        <if test="countyName != null">and countyName like concat('%', #{countyName}, '%')</if>
        <if test="power != null">and power like concat('%', #{power}, '%')</if>
        <if test="createdBy != null">and createdBy like concat('%', #{createdBy}, '%')</if>
        <if test="createTime != null">and createTime like concat('%', #{createTime}, '%')</if>
        <if test="updateTime != null">and updateTime like concat('%', #{updateTime}, '%')</if>
        <if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
    </sql>


    <select id="selectByLike" parameterType="com.sccl.modules.business.cost.domain.DeviationBusi" resultMap="DeviationBusiResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="DeviationBusiResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="DeviationBusiResult">
        <include refid="selectVo"/>
        where del_flag = '0' and id = #{id}
    </select>

    <select id="count" parameterType="com.sccl.modules.business.cost.domain.DeviationBusi" resultType="Integer">
        select count(*) from deviation_busi
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>

    <select id="selectModle" resultMap="DeviationBusiResult">
        <include refid="selectVo"/>
        <where>
            del_flag = 0
            <include refid="other-condition"/>
        </where>
    </select>


    <insert id="insert" parameterType="com.sccl.modules.business.cost.domain.DeviationBusi" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into deviation_busi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id, audit_time,stationCode,stationName,`year`,`month`,
            cityCode,cityName,countyCode,countyName,power,
            createdBy,createTime,updateTime,del_flag
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            #{auditTime},
            #{stationCode},
            #{stationName},
            #{year},
            #{month},
            #{cityCode},
            #{cityName},
            #{countyCode},
            #{countyName},
            #{power},
            #{createdBy},
            #{createTime},
            #{updateTime},
            #{delFlag}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into deviation_busi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            audit_time,
            stationCode,
            stationName,
            `year`,
            `month`,
            cityCode,
            cityName,
            countyCode,
            countyName,
            power,
            createdBy,
            createTime,
            updateTime,
            del_flag
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.auditTime},
                #{item.stationCode},
                #{item.stationName},
                #{item.year},
                #{item.month},
                #{item.cityCode},
                #{item.cityName},
                #{item.countyCode},
                #{item.countyName},
                #{item.power},
                #{item.createdBy},
                #{item.createTime},
                #{item.updateTime},
                #{item.delFlag}
            </trim>
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.sccl.modules.business.cost.domain.DeviationBusi">
        update deviation_busi
        <trim prefix="SET" suffixOverrides=",">
            audit_time = #{auditTime},
            stationCode = #{stationCode},
            stationName = #{stationName},
            `year` = #{year},
            `month` = #{month},
            cityCode = #{cityCode},
            cityName = #{cityName},
            countyCode = #{countyCode},
            countyName = #{countyName},
            power = #{power},
            createdBy = #{createdBy},
            createTime = #{createTime},
            updateTime = #{updateTime},
            del_flag = #{delFlag},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="com.sccl.modules.business.cost.domain.DeviationBusi">
        update deviation_busi
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="cityName != null">cityName = #{cityName},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="countyName != null">countyName = #{countyName},</if>
            <if test="power != null">power = #{power},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModelBatch" parameterType="com.sccl.modules.business.cost.domain.DeviationBusi">
        update deviation_busi
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="stationCode != null">stationCode = #{stationCode},</if>
            <if test="stationName != null">stationName = #{stationName},</if>
            <if test="year != null">`year` = #{year},</if>
            <if test="month != null">`month` = #{month},</if>
            <if test="cityCode != null">cityCode = #{cityCode},</if>
            <if test="cityName != null">cityName = #{cityName},</if>
            <if test="countyCode != null">countyCode = #{countyCode},</if>
            <if test="countyName != null">countyName = #{countyName},</if>
            <if test="power != null">power = #{power},</if>
            <if test="createdBy != null">createdBy = #{createdBy},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE deviation_busi SET DEL_FLAG='1' where id = #{id}
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE deviation_busi SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from deviation_busi where id = #{id}
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from deviation_busi where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="delByAuditTime">
        delete from deviation_busi
        where audit_time = #{auditTime}
    </delete>

    <insert id="audit" parameterType="com.sccl.modules.business.cost.vo.DeviationAuditVo">
        insert into deviation_busi(audit_time,stationCode,stationName,`year`,`month`,
            cityCode,cityName,countyCode,countyName,power,
            createdBy,createTime,updateTime,del_flag)
        select #{auditTime} audit_time,
            a.stationCode,
            a.stationName,
            #{year} `year`,
            #{month} `month`,
            a.cityCode,
            a.cityName,
            a.countyCode,
            a.countyName,
            sum(a.energyData) power,
            '-1' createdBy,
            #{createTime} createTime,
            #{updateTime} updateTime,
            '0' del_flag
        from fail_sync_collectmeter a
        inner join (select max(b0.id) id
            from fail_sync_collectmeter b0
            where b0.del_Flag = 0
            and b0.syncFlag = 1
            and b0.collectTime >= #{kssj}
            and b0.collectTime &lt;= #{jssj}
            group by b0.collectTime,b0.cityCode,b0.countyCode,b0.stationCode
        ) b on b.id = a.id
        where a.del_Flag = 0
        and a.syncFlag = 1
        and a.collectTime >= #{kssj}
        and a.collectTime &lt;= #{jssj}
        group by a.cityCode,a.countyCode,a.stationCode
    </insert>
</mapper>